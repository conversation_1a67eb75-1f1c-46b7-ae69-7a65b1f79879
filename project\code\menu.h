#ifndef __MENU_H__
#define __MENU_H__
extern float Kp;
extern float Ki;
extern float Kd;

extern float Kp1;
extern float Ki1;
extern float Kd1;

extern int top;
extern int low;

extern int b_d1;
extern int b_d2;
extern int b_d3;

extern int top1;  // 非环岛巡线时的上限
extern int low1;  // 非环岛巡线时的下限

void menu_init();
void display_menu();
void button_handler();
void update_menu_animation();
extern int special_mode;




#endif