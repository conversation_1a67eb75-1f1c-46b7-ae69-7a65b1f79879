/*********************************************************************************************************************
* RT1064DVL6A Opensourec Library 即（RT1064DVL6A 开源库）是一个基于官方 SDK 接口的第三方开源库
* Copyright (c) 2022 SEEKFREE 逐飞科技
* 
* 本文件是 RT1064DVL6A 开源库的一部分
* 
* RT1064DVL6A 开源库 是免费软件
* 您可以根据自由软件基金会发布的 GPL（GNU General Public License，即 GNU通用公共许可证）的条款
* 即 GPL 的第3版（即 GPL3.0）或（您选择的）任何后来的版本，重新发布和/或修改它
* 
* 本开源库的发布是希望它能发挥作用，但并未对其作任何的保证
* 甚至没有隐含的适销性或适合特定用途的保证
* 更多细节请参见 GPL
* 
* 您应该在收到本开源库的同时收到一份 GPL 的副本
* 如果没有，请参阅<https://www.gnu.org/licenses/>
* 
* 额外注明：
* 本开源库使用 GPL3.0 开源许可证协议 以上许可申明为译文版本
* 许可申明英文版在 libraries/doc 文件夹下的 GPL3_permission_statement.txt 文件中
* 许可证副本在 libraries 文件夹下 即该文件夹下的 LICENSE 文件
* 欢迎各位使用并传播本程序 但修改内容时必须保留逐飞科技的版权声明（即本声明）
* 
* 文件名称          zf_driver_pit
* 公司名称          成都逐飞科技有限公司
* 版本信息          查看 libraries/doc 文件夹内 version 文件 版本说明
* 开发环境          IAR 8.32.4 or MDK 5.33
* 适用平台          RT1064DVL6A
* 店铺链接          https://seekfree.taobao.com/
* 
* 修改记录
* 日期              作者                备注
* 2022-09-21        SeekFree            first version
********************************************************************************************************************/

#ifndef _zf_driver_pit_h_
#define _zf_driver_pit_h_

#include "zf_common_typedef.h"
#include "fsl_pit.h"

typedef enum                                                                    // 枚举 PIT
{
    PIT_CH0 = kPIT_Chnl_0,
    PIT_CH1,
    PIT_CH2,
    PIT_CH3,
    PIT_MAX,
}pit_index_enum;

#define PIT_SOURCE_CLOCK CLOCK_GetFreq(kCLOCK_PerClk)                           // 定义PIT定时器的输入时钟


void pit_enable (pit_index_enum pit_chn);
void pit_disable (pit_index_enum pit_chn);

void pit_init (pit_index_enum pit_chn, uint32 period);

//====================================================宏定义函数区====================================================

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     PIT标志位获取
// 参数说明     pit_chn           使用的 PIT 编号
// 参数说明     ms              PIT 周期 ms 级别
// 返回参数     void
// 使用示例     pit_flag_clear(PIT_CH0);
// 备注信息     
//-------------------------------------------------------------------------------------------------------------------
#define pit_flag_get(pit_chn)       PIT_GetStatusFlags(PIT, (pit_chnl_t)pit_chn)

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     PIT标志位清除
// 参数说明     pit_chn           使用的 PIT 编号
// 参数说明     ms              PIT 周期 ms 级别
// 返回参数     void
// 使用示例     pit_flag_clear(PIT_CH0);
// 备注信息     
//-------------------------------------------------------------------------------------------------------------------
#define pit_flag_clear(pit_chn)     PIT_ClearStatusFlags(PIT, (pit_chnl_t)pit_chn, kPIT_TimerFlag)


//-------------------------------------------------------------------------------------------------------------------
// 函数简介     TIM PIT 中断初始化 ms 周期
// 参数说明     pit_chn           使用的 PIT 编号
// 参数说明     ms              PIT 周期 ms 级别
// 返回参数     void
// 使用示例     pit_ms_init(PIT_CH0, 1);
// 备注信息     
//-------------------------------------------------------------------------------------------------------------------
#define pit_ms_init(pit_chn, ms)  (pit_init((pit_chn), (ms) * (PIT_SOURCE_CLOCK / 1000)))

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     TIM PIT 中断初始化 us 周期
// 参数说明     pit_chn           使用的 PIT 编号
// 参数说明     us              PIT 周期 us 级别
// 返回参数     void
// 使用示例     pit_us_init(PIT_CH0, 100);
// 备注信息     
//-------------------------------------------------------------------------------------------------------------------
#define pit_us_init(pit_chn, us)  (pit_init((pit_chn), (us) * (PIT_SOURCE_CLOCK / 1000000)))

//====================================================宏定义函数区====================================================

#endif
