#include "zf_common_headfile.h"

// 修改后的变量定义：
// storageU1 用于存储数据部分（4 个字节）：data0（检测标志）、data1（x轴偏移量）、data2（测距低字节）、data3（测距高字节）
int8 storageU1[4];   
// rx_art1 用于存储完整的 8 字节数据帧：头帧2 字节 + 数据部分4 字节 + 尾帧2 字节
int8 rx_art1[8];     
int8 rx_data1 = 0;   // 当前接收到的字节

int pia = 0;
int box_detected = 0;
int distance_mm = 0; // 完整的测距值（单位：毫米）

// 串口中断接收函数
void uart1_rx_interrupt_new_handler(void)
{ 
    // 查询接收一个字节，若有数据则存入 rx_data1
    uart_query_byte(UART_1, &rx_data1);
    
    // 若位置矫正未完成，则进行数据接收
    // if(flag_Position_correction_finish == 0)
    // { 
        // 调用接收函数，由其内部判断完整帧再调用 rt1064_storageU1()
        uart1_rx_art(rx_data1);
    // }
}

// 数据帧接收状态机（扩展为 8 字节数据帧）
void uart1_rx_art(uint8 data)
{
    static uint8 stage = 0;
    uint8 i;  // 循环变量，用于清空缓存数组

    switch(stage)
    {
        case 0: // 等待接收头帧1：0x2B
            if(data == 0x2B)
            {
                rx_art1[0] = data;
                stage = 1;
            }
            else
            {
                stage = 0;
                for(i = 0; i < 8; i++)
                {
                    rx_art1[i] = 0x00;
                }
            }
            break;

        case 1: // 等待接收头帧2：0x11
            if(data == 0x11)
            {
                rx_art1[1] = data;
                stage = 2;
            }
            else
            {
                stage = 0;
                for(i = 0; i < 8; i++)
                {
                    rx_art1[i] = 0x00;
                }
            }
            break;

        case 2: // 接收数据部分1：data0（检测标志）
            rx_art1[2] = data;
            stage = 3;
            break;

        case 3: // 接收数据部分2：data1（x轴偏移量）
            rx_art1[3] = data;
            stage = 4;
            break;

        case 4: // 接收数据部分3：data2（测距低字节）
            rx_art1[4] = data;
            stage = 5;
            break;

        case 5: // 接收数据部分4：data3（测距高字节）
            rx_art1[5] = data;
            stage = 6;
            break;

        case 6: // 等待接收尾帧1：0x5A
            if(data == 0x5A)
            {
                rx_art1[6] = data;
                stage = 7;
            }
            else
            {
                stage = 0;
                for(i = 0; i < 8; i++)
                {
                    rx_art1[i] = 0x00;
                }
            }
            break;

        case 7: // 等待接收尾帧2：0x59
            if(data == 0x59)
            {
                rx_art1[7] = data;
                // 完整接收到一帧数据，调用数据处理函数
                rt1064_storageU1();
            }
            // 无论是否接收正确，状态都重置，并清空缓存数组
            stage = 0;
            for(i = 0; i < 8; i++)
            {
                rx_art1[i] = 0x00;
            }
            break;

        default:
            stage = 0;
            for(i = 0; i < 8; i++)
            {
                rx_art1[i] = 0x00;
            }
            break;
    }
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     从 UART_1 存储数据的函数
// 参数说明     void
// 返回参数     void
// 使用示例    rt1064_storageU1()
//-------------------------------------------------------------------------------------------------------------------
void rt1064_storageU1(void)
{
    // 将接收到的数据部分存入 storageU1 数组中
    storageU1[0] = rx_art1[2]; // 存储箱子检测标志
    storageU1[1] = rx_art1[3]; // 存储 x 轴偏移量（低 8 位）
    storageU1[2] = rx_art1[4]; // 存储测距低字节
    storageU1[3] = rx_art1[5]; // 存储测距高字节

    // 更新全局变量，供后续逻辑使用
    box_detected = storageU1[0];
    pia = storageU1[1];


// 将 storageU1 中的数据转换为无符号 8 位整数再组合
uint8_t low_byte = (uint8_t)storageU1[2];
uint8_t high_byte = (uint8_t)storageU1[3];
distance_mm = ((int)high_byte << 8) | low_byte;

}


// 修改后的变量定义：
// storageU1 用于存储数据部分（4 个字节）：data0（检测标志）、data1（x轴偏移量）、data2（测距低字节）、data3（测距高字节）
int8 storageU2[4];   
// rx_art1 用于存储完整的 8 字节数据帧：头帧2 字节 + 数据部分4 字节 + 尾帧2 字节
int8 rx_art2[8];     
int8 rx_data2 = 0;   // 当前接收到的字节



// 串口中断接收函数
void uart2_rx_interrupt_new_handler(void)
{ 
    // 查询接收一个字节，若有数据则存入 rx_data1
    uart_query_byte(UART_2, &rx_data2);
    
    // 若位置矫正未完成，则进行数据接收
    // if(flag_Position_correction_finish == 0)
    // { 
        // 调用接收函数，由其内部判断完整帧再调用 rt1064_storageU1()
        uart2_rx_art(rx_data2);
    // }
}

// 数据帧接收状态机（扩展为 8 字节数据帧）
void uart2_rx_art(uint8 data)
{
    static uint8 stage = 0;
    uint8 i;  // 循环变量，用于清空缓存数组

    switch(stage)
    {
        case 0: // 等待接收头帧1：0x2B
            if(data == 0x2D)
            {
                rx_art2[0] = data;
                stage = 1;
            }
            else
            {
                stage = 0;
                for(i = 0; i < 8; i++)
                {
                    rx_art2[i] = 0x00;
                }
            }
            break;

        case 1: // 等待接收头帧2：0x11
            if(data == 0x13)
            {
                rx_art2[1] = data;
                stage = 2;
            }
            else
            {
                stage = 0;
                for(i = 0; i < 8; i++)
                {
                    rx_art2[i] = 0x00;
                }
            }
            break;

        case 2: // 接收数据部分1：data0（检测标志）
            rx_art2[2] = data;
            stage = 3;
            break;

        case 3: // 接收数据部分2：data1（x轴偏移量）
            rx_art2[3] = data;
            stage = 4;
            break;

        case 4: // 接收数据部分3：data2（测距低字节）
            rx_art2[4] = data;
            stage = 5;
            break;

        case 5: // 接收数据部分4：data3（测距高字节）
            rx_art2[5] = data;
            stage = 6;
            break;

        case 6: // 等待接收尾帧1：0x5A
            if(data == 0x5C)
            {
                rx_art2[6] = data;
                stage = 7;
            }
            else
            {
                stage = 0;
                for(i = 0; i < 8; i++)
                {
                    rx_art2[i] = 0x00;
                }
            }
            break;

        case 7: // 等待接收尾帧2：0x59
            if(data == 0x5B)
            {
                rx_art2[7] = data;
                // 完整接收到一帧数据，调用数据处理函数
                rt1064_storageU2();
            }
            // 无论是否接收正确，状态都重置，并清空缓存数组
            stage = 0;
            for(i = 0; i < 8; i++)
            {
                rx_art2[i] = 0x00;
            }
            break;

        default:
            stage = 0;
            for(i = 0; i < 8; i++)
            {
                rx_art2[i] = 0x00;
            }
            break;
    }
}

//-------------------------------------------------------------------------------------------------------------------
// 函数简介     从 UART_2 存储数据的函数
// 参数说明     void
// 返回参数     void
// 使用示例    rt1064_storageU2()
//-------------------------------------------------------------------------------------------------------------------
void rt1064_storageU2(void)
{
    // 将接收到的数据部分存入 storageU2 数组中
    storageU2[0] = rx_art2[2]; // 存储箱子检测标志
    storageU2[1] = rx_art2[3]; // 存储 x 轴偏移量（低 8 位）
    storageU2[2] = rx_art2[4]; // 存储测距低字节
    storageU2[3] = rx_art2[5]; // 存储测距高字节

    flag_finish = storageU2[0];
    class = storageU2[1];
    number = storageU2[3];

    if (class == 15) {
        if ((number & 1) == 1) { // number 为奇数
            class1 = 0;
        } else { // number 为偶数
            class1 = 1;
        }
    } else if (class == 11 || class == 9 || class == 13 || class == 12 || class == 14 || class == 8 || class == 10) {
        class1 = 0; // dianzi左
    } else {
        class1 = 1;
    }
}


// 修改后的变量定义：
// storageU1 用于存储数据部分（4 个字节）：data0（检测标志）、data1（x轴偏移量）、data2（测距低字节）、data3（测距高字节）
int8 storageU4[4];   
// rx_art1 用于存储完整的 8 字节数据帧：头帧2 字节 + 数据部分4 字节 + 尾帧2 字节
int8 rx_art4[8];     
int8 rx_data4 = 0;   // 当前接收到的字节

// 串口中断接收函数
void uart4_rx_interrupt_new_handler(void)
{ 
    // 查询接收一个字节，若有数据则存入 rx_data4
    uart_query_byte(UART_4, &rx_data4);
    
    // 若位置矫正未完成，则进行数据接收
    // if(flag_Position_correction_finish == 0)
    // { 
        // 调用接收函数，由其内部判断完整帧再调用 rt1064_storageU1()
        uart4_rx_art(rx_data4);
    // }
}

// 数据帧接收状态机（扩展为 8 字节数据帧）
void uart4_rx_art(uint8 data)
{
    static uint8 stage = 0;
    uint8 i;  // 循环变量，用于清空缓存数组

    switch(stage)
    {
        case 0: // 等待接收头帧1：0x2B
            if(data == 0x2B)
            {
                rx_art4[0] = data;
                stage = 1;
            }
            else
            {
                stage = 0;
                for(i = 0; i < 8; i++)
                {
                    rx_art4[i] = 0x00;
                }
            }
            break;

        case 1: // 等待接收头帧2：0x11
            if(data == 0x11)
            {
                rx_art4[1] = data;
                stage = 2;
            }
            else
            {
                stage = 0;
                for(i = 0; i < 8; i++)
                {
                    rx_art4[i] = 0x00;
                }
            }
            break;

        case 2: // 接收数据部分1：data0（检测标志）
            rx_art4[2] = data;
            stage = 3;
            break;

        case 3: // 接收数据部分2：data1（x轴偏移量）
            rx_art4[3] = data;
            stage = 4;
            break;

        case 4: // 接收数据部分3：data2（测距低字节）
            rx_art4[4] = data;
            stage = 5;
            break;

        case 5: // 接收数据部分4：data3（测距高字节）
            rx_art4[5] = data;
            stage = 6;
            break;

        case 6: // 等待接收尾帧1：0x5A
            if(data == 0x5A)
            {
                rx_art4[6] = data;
                stage = 7;
            }
            else
            {
                stage = 0;
                for(i = 0; i < 8; i++)
                {
                    rx_art4[i] = 0x00;
                }
            }
            break;

        case 7: // 等待接收尾帧2：0x59
            if(data == 0x59)
            {
                rx_art4[7] = data;
                // 完整接收到一帧数据，调用数据处理函数
                rt1064_storageU4();
            }
            // 无论是否接收正确，状态都重置，并清空缓存数组
            stage = 0;
            for(i = 0; i < 8; i++)
            {
                rx_art4[i] = 0x00;
            }
            break;

        default:
            stage = 0;
            for(i = 0; i < 8; i++)
            {
                rx_art4[i] = 0x00;
            }
            break;
    }
}
int top_detected = 0;
int pia1 = 0;
int slope = 0;
int putbox = 0; // 放箱子标志
int left_right = 0; // 左右旋转标志
//-------------------------------------------------------------------------------------------------------------------
// 函数简介     从 UART_1 存储数据的函数
// 参数说明     void
// 返回参数     void
// 使用示例    rt1064_storageU1()
//-------------------------------------------------------------------------------------------------------------------
void rt1064_storageU4(void)
{
    // 将接收到的数据部分存入 storageU4 数组中
    storageU4[0] = rx_art4[2]; // 检测标志
    storageU4[1] = rx_art4[3]; // x轴偏移量（低 8 位）
    storageU4[2] = rx_art4[4]; // slope 低字节（乘以100后的低 8 位）
    storageU4[3] = rx_art4[5]; // slope 高字节（乘以100后的高 8 位）

    // // 更新全局变量，供后续逻辑使用
    // top_detected = storageU4[0];
    // pia1 = storageU4[1];

    // // 组合斜率数据：将高低字节合成一个16位整数
    // uint8_t low_byte = (uint8_t)storageU4[2];
    // uint8_t high_byte = (uint8_t)storageU4[3];
    // int16_t slope_int = (int16_t)(((int)high_byte << 8) | low_byte);

    // 如果斜率为负，数据应以补码形式传输，上述转换会保留符号位
    top_detected = storageU4[0];
    slope = storageU4[1];
    // slope = storageU4[2];
    // // 还原带小数部分的斜率（发送时乘以100，所以接收时除以100.0）
    // float slope_value = slope_int / 100.0f;

    // 如果需要全局变量保存整数值，可继续赋值
    // slope = slope_int;
    // tft180_show_float(0,80,slope_value,6,2);

}

