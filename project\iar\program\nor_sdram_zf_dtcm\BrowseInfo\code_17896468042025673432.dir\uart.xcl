"D:\yuanshi\project\code\uart.c"
-std=c11
-ferror-limit=0
-fbracket-depth=512
-fsigned-char
-MD
-MF
D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\BrowseInfo\code_17896468042025673432.dir\uart.pbi.dep
-o
D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\BrowseInfo\code_17896468042025673432.dir\uart.pbi
-I
D:\yuanshi\project\code
-I
D:\yuanshi\project\iar\program\..\..\user\inc
-I
D:\yuanshi\project\iar\program\..\..\code
-I
D:\yuanshi\project\iar\program\..\..\..\libraries\sdk\startup\iar
-I
D:\yuanshi\project\iar\program\..\..\..\libraries\sdk\deceive
-I
D:\yuanshi\project\iar\program\..\..\..\libraries\sdk\drives
-I
D:\yuanshi\project\iar\program\..\..\..\libraries\sdk\xip
-I
D:\yuanshi\project\iar\program\..\..\..\libraries\sdk\utilities
-I
D:\yuanshi\project\iar\program\..\..\..\libraries\sdk\utilities\debug_console
-I
D:\yuanshi\project\iar\program\..\..\..\libraries\sdk\utilities\str
-I
D:\yuanshi\project\iar\program\..\..\..\libraries\sdk\cmsis_drivers
-I
D:\yuanshi\project\iar\program\..\..\..\libraries\sdk\CMSIS\Include
-I
D:\yuanshi\project\iar\program\..\..\..\libraries\sdk\CMSIS\Driver\Include
-I
D:\yuanshi\project\iar\program\..\..\..\libraries\sdk\components\lists
-I
D:\yuanshi\project\iar\program\..\..\..\libraries\sdk\components\osa
-I
D:\yuanshi\project\iar\program\..\..\..\libraries\sdk\components\serial_manager
-I
D:\yuanshi\project\iar\program\..\..\..\libraries\sdk\components\uart
-I
D:\yuanshi\project\iar\program\..\..\..\libraries\sdk\board
-I
D:\yuanshi\project\iar\program\..\..\..\libraries\components\fatfs\source
-I
D:\yuanshi\project\iar\program\..\..\..\libraries\components\fatfs\source\fsl_sd_disk
-I
D:\yuanshi\project\iar\program\..\..\..\libraries\components\sdmmc
-I
D:\yuanshi\project\iar\program\..\..\..\libraries\components\sdmmc\common
-I
D:\yuanshi\project\iar\program\..\..\..\libraries\components\sdmmc\host\usdhc
-I
D:\yuanshi\project\iar\program\..\..\..\libraries\components\sdmmc\osa
-I
D:\yuanshi\project\iar\program\..\..\..\libraries\components\sdmmc\sd
-I
D:\yuanshi\project\iar\program\..\..\..\libraries\components\usb\device
-I
D:\yuanshi\project\iar\program\..\..\..\libraries\components\usb\include
-I
D:\yuanshi\project\iar\program\..\..\..\libraries\components\usb\phy
-I
D:\yuanshi\project\iar\program\..\..\..\libraries\components\usb\usb_cdc_adapter
-I
D:\yuanshi\project\iar\program\..\..\..\libraries\zf_common
-I
D:\yuanshi\project\iar\program\..\..\..\libraries\zf_driver
-I
D:\yuanshi\project\iar\program\..\..\..\libraries\zf_device
-I
D:\yuanshi\project\iar\program\..\..\..\libraries\zf_components
-I
D:\iar\arm\inc\c\aarch32
-I
D:\iar\arm\inc
-I
D:\iar\arm\inc\c
-I
D:\yuanshi\project\user\inc
-I
D:\yuanshi\project\code
-I
D:\yuanshi\libraries\sdk\startup\iar
-I
D:\yuanshi\libraries\sdk\deceive
-I
D:\yuanshi\libraries\sdk\drives
-I
D:\yuanshi\libraries\sdk\xip
-I
D:\yuanshi\libraries\sdk\utilities
-I
D:\yuanshi\libraries\sdk\utilities\debug_console
-I
D:\yuanshi\libraries\sdk\utilities\str
-I
D:\yuanshi\libraries\sdk\cmsis_drivers
-I
D:\yuanshi\libraries\sdk\CMSIS\Include
-I
D:\yuanshi\libraries\sdk\CMSIS\Driver\Include
-I
D:\yuanshi\libraries\sdk\components\lists
-I
D:\yuanshi\libraries\sdk\components\osa
-I
D:\yuanshi\libraries\sdk\components\serial_manager
-I
D:\yuanshi\libraries\sdk\components\uart
-I
D:\yuanshi\libraries\sdk\board
-I
D:\yuanshi\libraries\components\fatfs\source
-I
D:\yuanshi\libraries\components\fatfs\source\fsl_sd_disk
-I
D:\yuanshi\libraries\components\sdmmc
-I
D:\yuanshi\libraries\components\sdmmc\common
-I
D:\yuanshi\libraries\components\sdmmc\host\usdhc
-I
D:\yuanshi\libraries\components\sdmmc\osa
-I
D:\yuanshi\libraries\components\sdmmc\sd
-I
D:\yuanshi\libraries\components\usb\device
-I
D:\yuanshi\libraries\components\usb\include
-I
D:\yuanshi\libraries\components\usb\phy
-I
D:\yuanshi\libraries\components\usb\usb_cdc_adapter
-I
D:\yuanshi\libraries\zf_common
-I
D:\yuanshi\libraries\zf_driver
-I
D:\yuanshi\libraries\zf_device
-I
D:\yuanshi\libraries\zf_components
-D__CHAR_BITS__=8
-D__CHAR_MAX__=127
-D__CHAR_MIN__=(-__CHAR_MAX__-1)
-D__CHAR_SIZE__=1
-D__UNSIGNED_CHAR_MAX__=0xff
-D__SIGNED_CHAR_MAX__=127
-D__SIGNED_CHAR_MIN__=(-__SIGNED_CHAR_MAX__-1)
-D__CHAR_ALIGN__=1
-D__SHORT_SIZE__=2
-D__UNSIGNED_SHORT_MAX__=0xffff
-D__SIGNED_SHORT_MAX__=32767
-D__SIGNED_SHORT_MIN__=(-__SIGNED_SHORT_MAX__-1)
-D__SHORT_ALIGN__=2
-D__INT_SIZE__=4
-D__UNSIGNED_INT_MAX__=0xffffffffU
-D__SIGNED_INT_MAX__=2147483647
-D__SIGNED_INT_MIN__=(-__SIGNED_INT_MAX__-1)
-D__INT_ALIGN__=4
-D__LONG_SIZE__=4
-D__UNSIGNED_LONG_MAX__=0xffffffffUL
-D__SIGNED_LONG_MAX__=2147483647L
-D__SIGNED_LONG_MIN__=(-__SIGNED_LONG_MAX__-1)
-D__LONG_ALIGN__=4
-D__LONG_LONG_SIZE__=8
-D__UNSIGNED_LONG_LONG_MAX__=0xffffffffffffffffULL
-D__SIGNED_LONG_LONG_MAX__=9223372036854775807LL
-D__SIGNED_LONG_LONG_MIN__=(-__SIGNED_LONG_LONG_MAX__-1)
-D__LONG_LONG_ALIGN__=8
-D__INT8_T_TYPE__=signed char
-D__INT8_T_MAX__=127
-D__INT8_T_MIN__=(-__INT8_T_MAX__-1)
-D__UINT8_T_TYPE__=unsigned char
-D__UINT8_T_MAX__=0xff
-D__INT8_SIZE_PREFIX__="hh"
-D__INT16_T_TYPE__=signed short int
-D__INT16_T_MAX__=32767
-D__INT16_T_MIN__=(-__INT16_T_MAX__-1)
-D__UINT16_T_TYPE__=unsigned short int
-D__UINT16_T_MAX__=0xffff
-D__INT16_SIZE_PREFIX__="h"
-D__INT32_T_TYPE__=signed int
-D__INT32_T_MAX__=2147483647
-D__INT32_T_MIN__=(-__INT32_T_MAX__-1)
-D__UINT32_T_TYPE__=unsigned int
-D__UINT32_T_MAX__=0xffffffffU
-D__INT32_SIZE_PREFIX__=""
-D__INT64_T_TYPE__=signed long long int
-D__INT64_T_MAX__=9223372036854775807LL
-D__INT64_T_MIN__=(-__INT64_T_MAX__-1)
-D__UINT64_T_TYPE__=unsigned long long int
-D__UINT64_T_MAX__=0xffffffffffffffffULL
-D__INT64_SIZE_PREFIX__="ll"
-D__INT_LEAST8_T_TYPE__=signed char
-D__INT_LEAST8_T_MAX__=127
-D__INT_LEAST8_T_MIN__=(-__INT_LEAST8_T_MAX__-1)
-D__UINT_LEAST8_T_TYPE__=unsigned char
-D__UINT_LEAST8_T_MAX__=0xff
-D__INT8_C_SUFFIX__=
-D__UINT8_C_SUFFIX__=
-D__INT_LEAST8_SIZE_PREFIX__="hh"
-D__INT_LEAST16_T_TYPE__=signed short int
-D__INT_LEAST16_T_MAX__=32767
-D__INT_LEAST16_T_MIN__=(-__INT_LEAST16_T_MAX__-1)
-D__UINT_LEAST16_T_TYPE__=unsigned short int
-D__UINT_LEAST16_T_MAX__=0xffff
-D__INT16_C_SUFFIX__=
-D__UINT16_C_SUFFIX__=
-D__INT_LEAST16_SIZE_PREFIX__="h"
-D__INT_LEAST32_T_TYPE__=signed int
-D__INT_LEAST32_T_MAX__=2147483647
-D__INT_LEAST32_T_MIN__=(-__INT_LEAST32_T_MAX__-1)
-D__UINT_LEAST32_T_TYPE__=unsigned int
-D__UINT_LEAST32_T_MAX__=0xffffffffU
-D__INT32_C_SUFFIX__=
-D__UINT32_C_SUFFIX__=U
-D__INT_LEAST32_SIZE_PREFIX__=""
-D__INT_LEAST64_T_TYPE__=signed long long int
-D__INT_LEAST64_T_MAX__=9223372036854775807LL
-D__INT_LEAST64_T_MIN__=(-__INT_LEAST64_T_MAX__-1)
-D__UINT_LEAST64_T_TYPE__=unsigned long long int
-D__UINT_LEAST64_T_MAX__=0xffffffffffffffffULL
-D__INT64_C_SUFFIX__=LL
-D__UINT64_C_SUFFIX__=ULL
-D__INT_LEAST64_SIZE_PREFIX__="ll"
-D__INT_FAST8_T_TYPE__=signed int
-D__INT_FAST8_T_MAX__=2147483647
-D__INT_FAST8_T_MIN__=(-__INT_FAST8_T_MAX__-1)
-D__UINT_FAST8_T_TYPE__=unsigned int
-D__UINT_FAST8_T_MAX__=0xffffffffU
-D__INT_FAST8_SIZE_PREFIX__=""
-D__INT_FAST16_T_TYPE__=signed int
-D__INT_FAST16_T_MAX__=2147483647
-D__INT_FAST16_T_MIN__=(-__INT_FAST16_T_MAX__-1)
-D__UINT_FAST16_T_TYPE__=unsigned int
-D__UINT_FAST16_T_MAX__=0xffffffffU
-D__INT_FAST16_SIZE_PREFIX__=""
-D__INT_FAST32_T_TYPE__=signed int
-D__INT_FAST32_T_MAX__=2147483647
-D__INT_FAST32_T_MIN__=(-__INT_FAST32_T_MAX__-1)
-D__UINT_FAST32_T_TYPE__=unsigned int
-D__UINT_FAST32_T_MAX__=0xffffffffU
-D__INT_FAST32_SIZE_PREFIX__=""
-D__INT_FAST64_T_TYPE__=signed long long int
-D__INT_FAST64_T_MAX__=9223372036854775807LL
-D__INT_FAST64_T_MIN__=(-__INT_FAST64_T_MAX__-1)
-D__UINT_FAST64_T_TYPE__=unsigned long long int
-D__UINT_FAST64_T_MAX__=0xffffffffffffffffULL
-D__INT_FAST64_SIZE_PREFIX__="ll"
-D__INTMAX_T_TYPE__=signed long long int
-D__INTMAX_T_MAX__=9223372036854775807LL
-D__INTMAX_T_MIN__=(-__INTMAX_T_MAX__-1)
-D__UINTMAX_T_TYPE__=unsigned long long int
-D__UINTMAX_T_MAX__=0xffffffffffffffffULL
-D__INTMAX_C_SUFFIX__=LL
-D__UINTMAX_C_SUFFIX__=ULL
-D__INTMAX_SIZE_PREFIX__="ll"
-D__ATOMIC_BOOL_LOCK_FREE=2
-D__ATOMIC_CHAR_LOCK_FREE=2
-D__ATOMIC_CHAR16_T_LOCK_FREE=2
-D__ATOMIC_CHAR32_T_LOCK_FREE=2
-D__ATOMIC_WCHAR_T_LOCK_FREE=2
-D__ATOMIC_SHORT_LOCK_FREE=2
-D__ATOMIC_INT_LOCK_FREE=2
-D__ATOMIC_LONG_LOCK_FREE=2
-D__ATOMIC_LLONG_LOCK_FREE=0
-D__ATOMIC_POINTER_LOCK_FREE=2
-D__FLOAT_SIZE__=4
-D__FLOAT_ALIGN__=4
-D__FLT_MANT_DIG__=24
-D__FLT_DIG__=6
-D__FLT_DECIMAL_DIG__=9
-D__FLT_MAX_EXP__=128
-D__FLT_MIN_EXP__=-125
-D__FLT_MAX_10_EXP__=38
-D__FLT_MIN_10_EXP__=-37
-D__FLT_MAX__=3.40282347E+38
-D__FLT_MIN__=1.17549435E-38
-D__FLT_HAS_SUBNORM__=1
-D__FLT_TRUE_MIN__=1.40129846E-45
-D__FLT_DENORM_MIN__=1.40129846E-45
-D__FLT_EPSILON__=1.1920929E-7
-D__DOUBLE_SIZE__=8
-D__DOUBLE_ALIGN__=8
-D__DBL_MANT_DIG__=53
-D__DBL_DIG__=15
-D__DBL_DECIMAL_DIG__=17
-D__DBL_MAX_EXP__=1024
-D__DBL_MIN_EXP__=-1021
-D__DBL_MAX_10_EXP__=308
-D__DBL_MIN_10_EXP__=-307
-D__DBL_MAX__=1.7976931348623157E+308
-D__DBL_MIN__=2.2250738585072014E-308
-D__DBL_HAS_SUBNORM__=1
-D__DBL_TRUE_MIN__=4.9406564584124654E-324
-D__DBL_DENORM_MIN__=4.9406564584124654E-324
-D__DBL_EPSILON__=2.2204460492503131E-16
-D__LONG_DOUBLE_SIZE__=8
-D__LONG_DOUBLE_ALIGN__=8
-D__LDBL_MANT_DIG__=53
-D__LDBL_DIG__=15
-D__LDBL_DECIMAL_DIG__=17
-D__LDBL_MAX_EXP__=1024
-D__LDBL_MIN_EXP__=-1021
-D__LDBL_MAX_10_EXP__=308
-D__LDBL_MIN_10_EXP__=-307
-D__LDBL_MAX__=1.7976931348623157E+308
-D__LDBL_MIN__=2.2250738585072014E-308
-D__LDBL_HAS_SUBNORM__=1
-D__LDBL_TRUE_MIN__=4.9406564584124654E-324
-D__LDBL_DENORM_MIN__=4.9406564584124654E-324
-D__LDBL_EPSILON__=2.2204460492503131E-16
-D____FP16_SIZE__=2
-D____FP16_ALIGN__=2
-D__FLT16_MANT_DIG__=11
-D__FLT16_DIG__=3
-D__FLT16_DECIMAL_DIG__=5
-D__FLT16_MAX_EXP__=16
-D__FLT16_MIN_EXP__=-13
-D__FLT16_MAX_10_EXP__=4
-D__FLT16_MIN_10_EXP__=-4
-D__FLT16_MAX__=6.5504E+4
-D__FLT16_MIN__=6.1035E-5
-D__FLT16_HAS_SUBNORM__=1
-D__FLT16_TRUE_MIN__=5.9605E-8
-D__FLT16_DENORM_MIN__=5.9605E-8
-D__FLT16_EPSILON__=9.7656E-4
-D___FLOAT16_SIZE__=2
-D___FLOAT16_ALIGN__=2
-D__NAN_HAS_HIGH_MANTISSA_BIT_SET__=0
-D__SUBNORMAL_FLOATING_POINTS__=1
-D__SIZE_T_TYPE__=unsigned int
-D__SIZE_T_MAX__=0xffffffffU
-D__PTRDIFF_T_TYPE__=signed int
-D__PTRDIFF_T_MAX__=2147483647
-D__PTRDIFF_T_MIN__=(-__PTRDIFF_T_MAX__-1)
-D__INTPTR_T_TYPE__=signed int
-D__INTPTR_T_MAX__=2147483647
-D__INTPTR_T_MIN__=(-__INTPTR_T_MAX__-1)
-D__UINTPTR_T_TYPE__=unsigned int
-D__UINTPTR_T_MAX__=0xffffffffU
-D__INTPTR_SIZE_PREFIX__=""
-D__JMP_BUF_ELEMENT_TYPE__=unsigned long long int
-D__JMP_BUF_NUM_ELEMENTS__=16
-D__TID__=0xcf60
-D__VER__=9040001
-D__BUILD_NUMBER__=364
-D__IAR_SYSTEMS_ICC__=9
-D_MAX_ALIGNMENT=8
-D__LITTLE_ENDIAN__=1
-D__BOOL_TYPE__=unsigned char
-D__BOOL_SIZE__=1
-D__WCHAR_T_TYPE__=unsigned int
-D__WCHAR_T_SIZE__=4
-D__WCHAR_T_MAX__=0xffffffffU
-D__DEF_PTR_MEM__=__data
-D__DEF_PTR_SIZE__=4
-D__DATA_MEM0__=__data
-D__DATA_MEM0_POINTER_OK__=1
-D__DATA_MEM0_UNIQUE_POINTER__=1
-D__DATA_MEM0_VAR_OK__=1
-D__DATA_MEM0_INTPTR_TYPE__=int
-D__DATA_MEM0_UINTPTR_TYPE__=unsigned int
-D__DATA_MEM0_INTPTR_SIZE_PREFIX__=""
-D__DATA_MEM0_MAX_SIZE__=0x7fffffffU
-D_RSIZE_MAX=0x7fffffffU
-D__DATA_MEM0_HEAP_SEGMENT__="HEAP"
-D__DATA_MEM0_PAGE_SIZE__=0
-D__DATA_MEM0_HEAP__=0
-D__CODE_MEM0__=__code
-D__CODE_MEM0_POINTER_OK__=1
-D__CODE_MEM0_UNIQUE_POINTER__=1
-D__HEAP_MEM0__=0
-D__HEAP_DEFAULT_MEM__=0
-D__HEAPND_MEMORY_LIST1__()=
-D__MULTIPLE_HEAPS__=0
-D__DEF_HEAP_MEM__=__data
-D__DEF_STACK_MEM_INDEX__=0
-D__PRAGMA_PACK_ON__=1
-D__MULTIPLE_INHERITANCE__=1
-D__FOR_DEBUG__=
-D__AAPCS_VFP__=1
-D__ARM4TM__=4
-D__ARM5TM__=5
-D__ARM5T__=5
-D__ARM5__=5
-D__ARM6MEDIA__=6
-D__ARM6M__=11
-D__ARM6SM__=12
-D__ARM6T2__=6
-D__ARM6__=6
-D__ARM7EM__=13
-D__ARM7M__=7
-D__ARM7__=7
-D__ARMFPV5__=5
-D__ARMVFPV1__=1
-D__ARMVFPV2__=2
-D__ARMVFPV3_D16__=1
-D__ARMVFPV3_FP16__=1
-D__ARMVFPV3__=3
-D__ARMVFPV4__=4
-D__ARMVFP_D16__=1
-D__ARMVFP_FP16__=1
-D__ARMVFP__=__ARMFPV5__
-D__ARM_32BIT_STATE=1
-D__ARM_ACLE=201
-D__ARM_ALIGN_MAX_PWR=8
-D__ARM_ALIGN_MAX_STACK_PWR=3
-D__ARM_ARCH=7
-D__ARM_ARCH_ISA_THUMB=2
-D__ARM_ARCH_PROFILE='M'
-D__ARM_FEATURE_CLZ=1
-D__ARM_FEATURE_COPROC=15
-D__ARM_FEATURE_DIRECTED_ROUNDING=1
-D__ARM_FEATURE_DSP=1
-D__ARM_FEATURE_FMA=1
-D__ARM_FEATURE_IDIV=1
-D__ARM_FEATURE_LDREX=7
-D__ARM_FEATURE_MVE=0
-D__ARM_FEATURE_NUMERIC_MAXMIN=1
-D__ARM_FEATURE_QBIT=1
-D__ARM_FEATURE_SAT=1
-D__ARM_FEATURE_SIMD32=1
-D__ARM_FEATURE_UNALIGNED=1
-D__ARM_FP=14
-D__ARM_FP16_ARGS=1
-D__ARM_FP16_FORMAT_IEEE=1
-D__ARM_MEDIA__=1
-D__ARM_PCS_VFP=1
-D__ARM_PROFILE_M__=1
-D__ARM_SIZEOF_MINIMAL_ENUM=1
-D__ARM_SIZEOF_WCHAR_T=4
-D__ARM_SIZE_MINIMAL_ENUM=1
-D__ARM_SIZE_WCHAR_T=4
-D__CODE_SIZE_LIMIT=0
-D__CORE__=__ARM7EM__
-D__CPU_MODE__=1
-D__ICCARM_INTRINSICS_VERSION__=2
-D__ICCARM__=1
-D__INTERWORKING__=1
-D__thumb__=1
-D__PLAIN_INT_BITFIELD_IS_SIGNED__=0
-D__HAS_WEAK__=1
-D__HAS_PACKED__=1
-D__HAS_JOINED_TYPES__=1
-D__HAS_LOCATED_DECLARATION__=1
-D__HAS_LOCATED_WITH_INIT__=1
-D__IAR_COMPILERBASE__=0xa0e01
-D__IAR_COMPILERBASE_STR__=10.14.1.1470
-D__UNICODE_SOURCE_SUPPORTED__=1
-D__VTABLE_MEM__=
-D__SIGNED_CHARS__=1
-D__PRAGMA_REDEFINE_EXTNAME=1
-D__STDC__=1
-D__STDC_VERSION__=201710L
-D__STDC_IEC_559__=1
-D__STDC_IEC_559_COMPLEX__=1
-D__MEMORY_ORDER_RELAXED__=0
-D__MEMORY_ORDER_CONSUME__=1
-D__MEMORY_ORDER_ACQUIRE__=2
-D__MEMORY_ORDER_RELEASE__=3
-D__MEMORY_ORDER_ACQ_REL__=4
-D__MEMORY_ORDER_SEQ_CST__=5
-D__STDC_UTF_16__=1
-D__STDC_UTF_32__=1
-D__STDC_LIB_EXT1__=201112L
-D__STDC_NO_THREADS__=1
-D__STDC_ISO_10646__=201103L
-D__STDC_HOSTED__=1
-D__EDG_IA64_ABI=1
-D__EDG_IA64_ABI_VARIANT_CTORS_AND_DTORS_RETURN_THIS=1
-D__EDG_IA64_ABI_USE_INT_STATIC_INIT_GUARD=1
-D__cpp_designated_initializers=201707L
-D__cpp_hex_float=201603L
-D__cpp_binary_literals=201304L
-D__cpp_unicode_literals=200710L
-D__cpp_static_assert=200410L
-D__EDG__=1
-D__EDG_VERSION__=603
-D__EDG_SIZE_TYPE__=unsigned int
-D__EDG_PTRDIFF_TYPE__=int
-D__EDG_DELTA_TYPE=int
-D__EDG_IA64_VTABLE_ENTRY_TYPE=int
-D__EDG_VIRTUAL_FUNCTION_INDEX_TYPE=unsigned short
-D__EDG_LOWER_VARIABLE_LENGTH_ARRAYS=1
-D__EDG_IA64_ABI_USE_VARIANT_ARRAY_COOKIES=1
-D__EDG_ABI_COMPATIBILITY_VERSION=9999
-D__EDG_ABI_CHANGES_FOR_RTTI=1
-D__EDG_ABI_CHANGES_FOR_ARRAY_NEW_AND_DELETE=1
-D__EDG_ABI_CHANGES_FOR_PLACEMENT_DELETE=1
-D__EDG_BSD=0
-D__EDG_SYSV=0
-D__EDG_ANSIC=1
-D__EDG_CPP11_IL_EXTENSIONS_SUPPORTED=1
-D__EDG_FLOAT80_ENABLING_POSSIBLE=0
-D__EDG_FLOAT128_ENABLING_POSSIBLE=0
-D__EDG_INT128_EXTENSIONS_ALLOWED=0
-DCPU_MIMXRT1064DVL6A=1
-DSKIP_SYSCLK_INIT=1
-DXIP_EXTERNAL_FLASH=1
-DXIP_BOOT_HEADER_ENABLE=1
-DXIP_BOOT_HEADER_DCD_ENABLE=1
-DPRINTF_FLOAT_ENABLE=1
-DSCANF_FLOAT_ENABLE=1
-DPRINTF_ADVANCED_ENABLE=1
-DSCANF_ADVANCED_ENABLE=1
-DFSL_DRIVER_TRANSFER_DOUBLE_WEAK_IRQ=0
-DUSB_STACK_BM=1
-DDEBUG=1
-DMCUXPRESSO_SDK=1
-DSD_ENABLED=1
-D_DLIB_CONFIG_FILE_HEADER_NAME="D:\iar\arm\inc\c\DLib_Config_Full.h"
-D_DLIB_CONFIG_FILE_STRING="D:\\iar\\arm\\inc\\c\\DLib_Config_Full.h"
-D__VERSION__="IAR ANSI C/C++ Compiler V9.40.1.364/W64 for ARM"
-D_VA_DEFINED=
-D_VA_LIST=struct __va_list
-D__ICCARM_OLD_DEFINED_VAARGS__=1
-D__VA_STACK_ALIGN__=8
-D__CODE_MEMORY_LIST1__()=__CODE_MEM_HELPER1__(__code, 0 )
-D__CODE_MEMORY_LIST2__(_P1)=__CODE_MEM_HELPER2__(__code, 0 ,  _P1 )
-D__CODE_MEMORY_LIST3__(_P1,_P2)=__CODE_MEM_HELPER3__(__code, 0 ,  _P1 ,  _P2 )
-D__DATA_MEMORY_LIST1__()=__DATA_MEM_HELPER1__(__data, 0 )
-D__DATA_MEMORY_LIST2__(_P1)=__DATA_MEM_HELPER2__(__data, 0 ,  _P1 )
-D__DATA_MEMORY_LIST3__(_P1,_P2)=__DATA_MEM_HELPER3__(__data, 0 ,  _P1 ,  _P2 )
-D__CODE_PTR_MEMORY_LIST1__()=__CODE_PTR_MEM_HELPER1__(__code, 0 )
-D__CODE_PTR_MEMORY_LIST2__(_P1)=__CODE_PTR_MEM_HELPER2__(__code, 0 ,  _P1 )
-D__CODE_PTR_MEMORY_LIST3__(_P1,_P2)=__CODE_PTR_MEM_HELPER3__(__code, 0 ,  _P1 ,  _P2 )
-D__DATA_PTR_MEMORY_LIST1__()=__DATA_PTR_MEM_HELPER1__(__data, 0 )
-D__DATA_PTR_MEMORY_LIST2__(_P1)=__DATA_PTR_MEM_HELPER2__(__data, 0 ,  _P1 )
-D__DATA_PTR_MEMORY_LIST3__(_P1,_P2)=__DATA_PTR_MEM_HELPER3__(__data, 0 ,  _P1 ,  _P2 )
-D__VAR_MEMORY_LIST1__()=__VAR_MEM_HELPER1__(__data, 0 )
-D__VAR_MEMORY_LIST2__(_P1)=__VAR_MEM_HELPER2__(__data, 0 ,  _P1 )
-D__VAR_MEMORY_LIST3__(_P1,_P2)=__VAR_MEM_HELPER3__(__data, 0 ,  _P1 ,  _P2 )
-D__VARD_MEMORY_LIST1__()=__VARD_MEM_HELPER1__(__data, 0, _ )
-D__HEAP_MEMORY_LIST1__()=__HEAP_MEM_HELPER1__(__data, 0 )
-D__HEAP_MEMORY_LIST2__(_P1)=__HEAP_MEM_HELPER2__(__data, 0 ,  _P1 )
-D__HEAP_MEMORY_LIST3__(_P1,_P2)=__HEAP_MEM_HELPER3__(__data, 0 ,  _P1 ,  _P2 )
-D__HVAR_MEMORY_LIST1__()=__HVAR_MEM_HELPER1__(__data, 0 )
-D__HEAPD_MEMORY_LIST1__()=__HEAPD_MEM_HELPER1__(__data, 0, _ )
-D__HEAPU_MEMORY_LIST1__()=__HEAPU_MEM_HELPER1__(__data, 0 )
-D__TOPM_DATA_MEMORY_LIST1__()=
-D__TOPM_DATA_MEMORY_LIST2__(_P1)=
-D__TOPM_DATA_MEMORY_LIST3__(_P1,_P2)=
-D__TOPP_DATA_MEMORY_LIST1__()=__TOPP_DATA_MEM_HELPER1__(__data, 0 )
-D__TOPP_DATA_MEMORY_LIST2__(_P1)=__TOPP_DATA_MEM_HELPER2__(__data, 0 ,  _P1 )
-D__TOPP_DATA_MEMORY_LIST3__(_P1,_P2)=__TOPP_DATA_MEM_HELPER3__(__data, 0 ,  _P1 ,  _P2 )
-D__DATA_MEM0_SIZE_TYPE__=unsigned int
-D__DATA_MEM0_INDEX_TYPE__=signed int
-D__iar_fp2bits32(x)=0
-D__iar_fp2bits64(x)=0
-D__iar_fpgethi64(x)=0
-D__iar_atomic_add_fetch(x,y,z)=0
-D__iar_atomic_sub_fetch(x,y,z)=0
-D__iar_atomic_load(x,y)=0ULL
-D__iar_atomic_compare_exchange_weak(a,b,c,d,e)=0
