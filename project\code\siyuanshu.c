#include "zf_common_headfile.h"
#include "math.h"
float I_ex, I_ey, I_ez;  // 误差积分
float Q_info_q0 = 1.0,Q_info_q1 = 0.0,Q_info_q2= 0.0,Q_info_q3 = 0.0; // 四元数初始化
float eulerAngle_pitch=0.0,eulerAngle_roll=0.0,eulerAngle_yaw=0.0;
float icm_kp= 0.5;   //0.17;     // 0.5     // 加速度计的收敛速率比例增益
float icm_ki= 0.0025;    //0.004;     //0.025    // 陀螺仪收敛速率的积分增益
/*计算偏移量*/
float gx_offset = 0.0, gy_offset = 0.0, gz_offset = 0.0;  //x,y轴的角速度偏移量   
float Angle_gx=0.0;
float Angle_gy=0.0;
float Angle_gz=0.0;
float Angle_ax=0.0;
float Angle_ay=0.0;
float Angle_az=0.0;

void gyroOffsetInit(void)              //陀螺仪初始化函数
{
    gx_offset = 0;
    gy_offset = 0;
    gz_offset = 0;
    system_delay_ms(500);         //取决于小车实际情况，有的刚上电陀螺仪数值不稳定，须延时
    for (int i = 0; i < 1000; ++i)
    {
      imu660ra_get_gyro();    // 获取陀螺仪角速度
      gx_offset += imu660ra_gyro_x;
      gy_offset += imu660ra_gyro_y;
      gz_offset += imu660ra_gyro_z;   //imu660ra_gyro_x

       system_delay_ms(1); 
      //system_delay_ms(5);
    }
    gx_offset *= 0.001;
    gy_offset *= 0.001;
    gz_offset *= 0.001;
}
//快速计算  1/平方根
float invSqrt(float x)
{
	float halfx = 0.5f * x;
	float y = x;
	long i = *(long*)&y;
	i = 0x5f3759df - (i>>1);
	y = *(float*)&i;
	y = y * (1.5f - (halfx * y * y));
	return y;
}
void icmGetValues(void)         //滤波以及转换单位
{
    float alpha = 0.3;
     
    //一阶低通滤波，单位g
    Angle_ax = (((float) imu660ra_acc_x) * alpha) * 0.0002441406 + Angle_ax * (1 - alpha);
    Angle_ay = (((float) imu660ra_acc_y) * alpha) * 0.0002441406 + Angle_ay * (1 - alpha);
    Angle_az = (((float) imu660ra_acc_z) * alpha) * 0.0002441406 + Angle_az * (1 - alpha);

    //! 陀螺仪角速度必须转换为弧度制角速度: deg/s -> rad/s
    Angle_gx = ((float) imu660ra_gyro_x - gx_offset) * 0.0010641938;                 //3.1415 / 180 / 16.4f; 
    Angle_gy = ((float) imu660ra_gyro_y - gy_offset) * 0.0010641938;                //3.1415 / 180 / 16.4f;
    Angle_gz = ((float) imu660ra_gyro_z - gz_offset) * 0.0010641938;               //3.1415 / 180 / 16.4f;
}
void icmAHRSupdate(void)      //四元数计算
{
    float halfT = 0.5 * delta_T;    // 采样周期一半
    float vx, vy, vz;               // 当前姿态计算得来的重力在三轴上的分量
    float ex, ey, ez;               // 当前加速计测得的重力加速度在三轴上的分量与用当前姿态计算得来的重力在三轴上的分量的误差
    
    float q0 = Q_info_q0;  //四元数
    float q1 = Q_info_q1;
    float q2 = Q_info_q2;
    float q3 = Q_info_q3;
    
    float q0q0 = q0 * q0;  //先相乘，方便后续计算
    float q0q1 = q0 * q1;
    float q0q2 = q0 * q2;
//    float q0q3 = q0 * q3;
    float q1q1 = q1 * q1;
//    float q1q2 = q1 * q2;
    float q1q3 = q1 * q3;
    float q2q2 = q2 * q2;
    float q2q3 = q2 * q3;
    float q3q3 = q3 * q3;

    // 正常静止状态为-g 反作用力。
    if(Angle_ax * Angle_ay * Angle_az == 0) // 加计处于自由落体状态时(此时g = 0)不进行姿态解算，因为会产生分母无穷大的情况
        return;

    // 对加速度数据进行归一化 得到单位加速度 (a^b -> 载体坐标系下的加速度)
    float norm = invSqrt(Angle_ax * Angle_ax + Angle_ay * Angle_ay + Angle_az * Angle_az); 
    Angle_ax = Angle_ax * norm;
    Angle_ay = Angle_ay * norm;
    Angle_az = Angle_az * norm;

    // 载体坐标系下重力在三个轴上的分量
    vx = 2 * (q1q3 - q0q2);
    vy = 2 * (q0q1 + q2q3);
    vz = q0q0 - q1q1 - q2q2 + q3q3;

    // g^b 与 a^b 做向量叉乘，得到陀螺仪的校正补偿向量e的系数
    ex = Angle_ay * vz - Angle_az * vy;
    ey = Angle_az * vx - Angle_ax * vz;
    ez = Angle_ax * vy - Angle_ay * vx;

    // 误差累加
    I_ex += halfT * ex;  
    I_ey += halfT * ey;
    I_ez += halfT * ez;

    // 使用PI控制器消除向量积误差(陀螺仪漂移误差)
    //将误差PI后补偿到陀螺仪，即补偿积分漂移
    Angle_gx = Angle_gx + icm_kp* ex + icm_ki* I_ex;
    Angle_gy = Angle_gy + icm_kp* ey + icm_ki* I_ey;
    Angle_gz = Angle_gz + icm_kp* ez + icm_ki* I_ez;
    //这里的gz由于没有观测者进行矫正会产生漂移，表现出来的就是积分自增或自减

    // 一阶龙格库塔法求解四元数微分方程，其中halfT为测量周期的1/2，gx gy gz为b系陀螺仪角速度。
    q0 = q0 + (-q1 * Angle_gx - q2 * Angle_gy - q3 * Angle_gz) * halfT;
    q1 = q1 + ( q0 * Angle_gx + q2 * Angle_gz - q3 * Angle_gy) * halfT;
    q2 = q2 + ( q0 * Angle_gy - q1 * Angle_gz + q3 * Angle_gx) * halfT;
    q3 = q3 + ( q0 * Angle_gz + q1 * Angle_gy - q2 * Angle_gx) * halfT;

    // 单位化四元数在空间旋转时不会拉伸，仅有旋转角度，下面算法类似线性代数里的正交变换
    norm = invSqrt(q0 * q0 + q1 * q1 + q2 * q2 + q3 * q3);
    Q_info_q0 = q0 * norm;
    Q_info_q1 = q1 * norm;
    Q_info_q2 = q2 * norm;
    Q_info_q3 = q3 * norm;  // 用全局变量记录上一次计算的四元数值
    
   eulerAngle_pitch = asin(2 * q0 * q2 - 2 * q1 * q3) * 57.295779;
   eulerAngle_roll = atan2(2 * q2 * q3 + 2 * q0 * q1, -2 * q1 * q1 - 2 * q2 * q2 + 1) * 57.295779;
    eulerAngle_yaw = atan2(2 * q1 * q2 + 2 * q0 * q3, -2 * q2 * q2 - 2 * q3 * q3 + 1) * 57.295779;

}
void imu660ra_euler_show(void) {
    tft180_show_int(30, 0, eulerAngle_yaw, 4);          // 显示最终角度
    tft180_show_int(30, 16, eulerAngle_roll, 4);      // 显示原始陀螺仪数据
    tft180_show_int(30, 32, eulerAngle_pitch, 4);       // 显示滤波后的数据
    tft180_show_int(60, 0, imu660ra_gyro_x, 4);          // 显示最终角度
    tft180_show_int(60, 16, imu660ra_gyro_y, 4);      // 显示原始陀螺仪数据
    tft180_show_int(60, 32, imu660ra_gyro_z, 4);       // 显示滤波后的数据
    tft180_show_int(60, 48, imu660ra_acc_x, 4);       // 显示滤波后的数据
    tft180_show_int(60, 64, imu660ra_acc_y, 4);       // 显示滤波后的数据
    tft180_show_int(60, 80, imu660ra_acc_z, 4);       // 显示滤波后的数据
}