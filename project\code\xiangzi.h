#ifndef _XIANGZI_H_
#define _XIANGZI_H_

extern int class;
extern int class1;
extern int flag_finish;
extern float target_raw;
extern float slope_raw;
extern int classIndex;
extern int anglepia;
extern float current_angular_rate;
// 全局状态变量声明，用于锁定推箱子模式
extern bool inTextMode;
void jiaodu(void);
extern int number;
extern int classIndex;
extern float total_distance;
extern float vx;
extern float vy;
// extern int classValues[100];
#define MAX_CLASS_VALUES 100  // 最大记录条数，可根据需要调整
typedef struct {
    uint8 type;  // 0 表示显示汉字，1 表示显示数字
    uint8 value; // 存储 class 或 number 的值
    uint8 direction; // 存储 class1 值，表示推箱子方向（0表示左，1表示右）
} ClassEntry;
extern ClassEntry classValues[MAX_CLASS_VALUES];  // 替换原来的 classValues 声明
void zong(void);
void push(void);
extern int yyy;
extern int safe;
extern int left;
extern int right;
extern int time1; // box_detected触发后的冷却时间(ms)
extern int time2;  // side_detected触发后的冷却时间(ms)
extern int time3; // push操作后禁止触发side_detected的冷却时间(ms)
extern float recorded_slope;  // 记录检测到箱子时的slope值
extern float angle_slope;     // 用于phase 3阶段转回的目标角度
extern int position_stable_time;
#endif