#include "zf_common_headfile.h"
#include "math.h"

// 特殊时间段标记
#define POST_PUSH_PERIOD_MS 300     // 推箱子退出后的特殊参数使用时间（毫秒）

// 全局状态变量，控制是否进入推箱子模式
extern int g_direct_omegaRef_override; // 声明外部变量，防止未定义错误
extern bool inTextMode; // 添加此声明以便在isr.c中使用
extern unsigned long last_push_time; // 引用xiangzi.c中的last_push_time变量

// 编码器相关全局变量
int zqcount_gpio = 0, yqcount_gpio = 0, houcount_gpio = 0; 
int count_Zq = 0, count_Yq = 0, count_Hou = 0;
int32 ti=0;
int32 tim=0;
float omegaMeasured[3];
float omegaRef[3] = {0.0f, 0.0f, 0.0f}; // 三个轮子的期望速度hou, zq, yq
int number = 0;
float current_angular_rate=0.0f; // 当前角速度
// 添加速度平滑过渡需要的全局变量
static unsigned long box_detection_start_time = 0;
static bool in_speed_transition = false;
static float initial_vy = 95.0f;
// 编码器读取函数
// 根据不同索引返回对应轮的编码器计数（或速度）
// 这里返回的 count 值可以进一步转换为实际的速度，如有需要请自行转换
float GetWheelSpeed(int index)
{
    float count = 0.0f;
    switch(index)
    {
        case 1: // 例如：左轮（zq）
            if(gpio_get_level(zqencode_dir) == 0)
                count = encoder_get_count(zq_encode);
            else
                count = -encoder_get_count(zq_encode);
            break;
        case 2: // 例如：右轮（yq）
            if(gpio_get_level(yqencode_dir) == 0)
                count = -encoder_get_count(yq_encode);
            else
                count = encoder_get_count(yq_encode);
            break;
        case 0: // 例如：后轮（hou）
            if(gpio_get_level(houencode_dir) == 1)
                count = encoder_get_count(hou_encode);
            else
                count = -encoder_get_count(hou_encode);
            break;
        default:
            // 若传入索引无效，可返回0或者错误码
            break;
    }
    return count;
}

void count_get(void)
{

    
    // 遍历3个编码器，通过索引调用GetWheelSpeed函数获取值
    for(int i = 0; i < 3; i++){
        omegaMeasured[i] = GetWheelSpeed(i)*52/30.0f;
    }
    // omegaMeasured[1]=omegaMeasured[1]*52/30.0f; // 左轮
    // 清除编码器计数
    encoder_clear_count(QTIMER1_ENCODER1);
    encoder_clear_count(QTIMER2_ENCODER1);
    encoder_clear_count(QTIMER4_ENCODER1);

    // 如果后续需要将omegaMeasured传递到其他模块，可在此进行处理或返回
}


// 全局变量：车体当前位置（单位：根据实际比例，如米或其他单位）及累计行驶距离
float robot_x = 0.0f;
float robot_y = 0.0f;
float total_distance = 0.0f;

// 采样时间间隔（秒）
const float dt = 0.002f;  // 2ms
extern float omegaMeasured[3];

/*
 * 函数功能：在2ms中断中调用，用于更新机器人当前位置和累计行驶里程
 */
void UpdateRobotPosition(void)
{
    // 1. 采集编码器数值（该函数内部会遍历三个轮子并更新 omegaMeasured 数组，同时清零编码器计数）
    count_get();
    
    // 2. 提取各轮速度（若 count 值未经过单位转换，则此处得到的速度与实际比例有关）
    float v_A = omegaMeasured[0];  // 后轮
    float v_B = omegaMeasured[1];  // 左轮
    float v_C = omegaMeasured[2];  // 右轮
    
    // 3. 根据全向车的前向运动学计算机器人平移速度分量
    //    前向运动学公式： v_x = (2*v_A + v_B - v_C) / 3
    //                        v_y = (v_B + v_C) / sqrt(3)
    float vx = (2.0f * v_A + v_B - v_C) / 3.0f;
    float vy = (v_B + v_C) / 1.73205f;  // 1.73205 ≈ sqrt(3)
    
    // 4. 计算当前采样周期内机器人中心位移
    float delta_x = vx * dt;
    float delta_y = vy * dt;
    
    // 更新全局位置信息
    robot_x += delta_x;
    robot_y += delta_y;
    
    // 5. 计算当前采样周期内的运动路程（位移矢量的模长）并累加
    float ds = sqrt(delta_x * delta_x + delta_y * delta_y);
    total_distance += ds;
}


float PID_Compute(PID_TypeDef *pid, float setpoint, float measured)
{
    float error = setpoint - measured;
    
    // 根据标志位决定是否进行角度归一化处理
    if (pid->is_angle) {
        if (error > 180.0f)
            error -= 360.0f;
        else if (error < -180.0f)
            error += 360.0f;
    }
    
    // 根据PID模式选择算法
    if (pid->pid_mode == 0) {
        // 增量式PID（原有算法）
        float delta = pid->kp * (error - pid->error_last)
                      + pid->ki * error
                      + pid->kd * (error - 2.0f * pid->error_last - pid->error_second_last);
        
        // 累加输出并饱和处理
        pid->output += delta;
    } else {
        // 位置式PID
        // 计算比例项、积分项和微分项
        float pTerm = pid->kp * error;
        float iTerm = pid->ki * (pid->error_last + error) * 0.5f; // 梯形积分
        float dTerm = pid->kd * (error - pid->error_last);
        
        // 直接计算输出
        pid->output = pTerm + iTerm + dTerm;
    }
    
    // 输出限幅
    if (pid->output > 8000)
        pid->output = 8000;
    else if (pid->output < -8000)
        pid->output = -8000;
    
    // 更新历史误差
    pid->error_second_last = pid->error_last;
    pid->error_last = error;
    
    return pid->output;
}

PID_TypeDef wheelPID[3] = {
    {.kp=16.0f, .ki=2.8f, .kd=0.0f, .error_last=0.0f, .error_second_last=0.0f, .output=0.0f, .is_angle=0, .pid_mode=0},
    {.kp=16.0f, .ki=2.90f, .kd=0.0f, .error_last=0.0f, .error_second_last=0.0f, .output=0.0f, .is_angle=0, .pid_mode=0},
    {.kp=16.0f, .ki=2.94f, .kd=0.0f, .error_last=0.0f, .error_second_last=0.0f, .output=0.0f, .is_angle=0, .pid_mode=0}
};

// 推箱子退出后使用的低积分参数（ki是原来的四分之一）
PID_TypeDef wheelPID_postPush[3] = {
    {.kp=16.0f, .ki=0.7f, .kd=0.0f, .error_last=0.0f, .error_second_last=0.0f, .output=0.0f, .is_angle=0, .pid_mode=0},
    {.kp=16.0f, .ki=0.725f, .kd=0.0f, .error_last=0.0f, .error_second_last=0.0f, .output=0.0f, .is_angle=0, .pid_mode=0},
    {.kp=16.0f, .ki=0.735f, .kd=0.0f, .error_last=0.0f, .error_second_last=0.0f, .output=0.0f, .is_angle=0, .pid_mode=0}
};

void SetWheelSpeed(float *omegaRef, float *omegaMeasured)
{
    // 电机的 PWM 和 DIR 引脚数组
    uint32 motor[3] = {MOTORb_PWM, MOTORl_PWM, MOTORr_PWM};
    gpio_pin_enum dir[3] = {MOTORb_DIR, MOTORl_DIR, MOTORr_DIR};
    
    // 定义电机方向控制逻辑 (true: 正转时GPIO=1, false: 正转时GPIO=0)
    bool dir_logic[3] = {false, true, true};  // 后轮、左轮、右轮的方向逻辑
    
    // 检查是否在push退出后的500ms内
    // 重要：确保仅在非推箱子模式下，且上次退出后不超过500ms时使用特殊参数
    bool is_post_push_period = (!inTextMode && last_push_time > 0 && (tim - last_push_time < POST_PUSH_PERIOD_MS));
    
    for (int i = 0; i < 3; i++)
    {
        // 根据时间段选择使用的PID参数
        float pwm_output;
        if (is_post_push_period) {
            // 使用低ki的PID参数（仅在推箱子退出后的短时间内）
            pwm_output = PID_Compute(&wheelPID_postPush[i], omegaRef[i], omegaMeasured[i]);
        } else {
            // 使用正常的PID参数（push过程中和其他所有时间）
            pwm_output = PID_Compute(&wheelPID[i], omegaRef[i], omegaMeasured[i]);
        }
        
        // 限幅处理，防止PWM输出超过最大值
        if (pwm_output > 8000)
            pwm_output = 8000;
        else if (pwm_output < -8000)
            pwm_output = -8000;
        
        // 根据pwm_output的正负来确定电机转向
        bool is_forward = (pwm_output >= 0);
        gpio_set_level(dir[i], is_forward ? dir_logic[i] : !dir_logic[i]);
        
        // 取绝对值，确保PWM为正值
        pwm_output = fabsf(pwm_output);
        
        // 输出PWM信号到相应电机
        pwm_set_duty(motor[i], (int)pwm_output);
    }
}



// 根据你的车模实际尺寸设置
#define L 1.0f                      // 等边三角形的边长（单位：可根据实际情况调整）
#define H (L * 0.86602540378f)      // 等边三角形高度的一部分，近似为 L*√3/2

void InverseKinematics(float vx, float vy, float omega, float *speed)
{
    // 1. 定义各轮相对于车体中心的位置（单位：与 L 保持一致）
    // 后轮（A）：设在 (0, -2H/3)
    float xA = 0.0f;
    float yA = -2.0f * H / 3.0f;
    
    // 左轮（B）：设在 (-L/2, H/3)
    float xB = -L / 2.0f;
    float yB = H / 3.0f;
    
    // 右轮（C）：设在 (L/2, H/3)
    float xC = L / 2.0f;
    float yC = H / 3.0f;
    
    // 2. 定义各轮允许运动方向的单位向量
    // 后轮（A）：允许运动方向为水平向右，即 (1, 0)
    float dAx = 1.0f, dAy = 0.0f;
    
    // 左轮（B）：允许运动方向为 60° 方向，即 (cos60, sin60) = (0.5, 0.866)
    float dBx = 0.5f, dBy = 0.86602540378f;
    
    // 右轮（C）：允许运动方向为 120° 方向，即 (cos120, sin120) = (-0.5, 0.866)
    float dCx = -0.5f, dCy = 0.86602540378f;
    
    // 3. 依据运动学模型，计算每个轮子的速度命令
    // 公式： v_i = d_i · ( [vx - ω*y_i,  vy + ω*x_i] )
    speed[0]  = dAx * (vx - omega * yA) + dAy * (vy + omega * xA);
    speed[1]  = dBx * (vx - omega * yB) + dBy * (vy + omega * xB);
    speed[2] = dCx * (vx - omega * yC) + dCy * (vy + omega * xC);

    // tft180_show_float(0,   20, speed[0], 4,1);
    // tft180_show_float(0,   40, speed[1], 4,1);
    // tft180_show_float(0,   60, speed[2], 4,1);
}