<?xml version="1.0" encoding="UTF-8"?>
<project>
    <fileVersion>4</fileVersion>
    <configuration>
        <name>nor_sdram_zf_dtcm</name>
        <toolchain>
            <name>ARM</name>
        </toolchain>
        <debug>1</debug>
        <settings>
            <name>General</name>
            <archiveVersion>3</archiveVersion>
            <data>
                <version>36</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>BrowseInfoPath</name>
                    <state>nor_sdram_zf_dtcm\BrowseInfo</state>
                </option>
                <option>
                    <name>ExePath</name>
                    <state>nor_sdram_zf_dtcm\Exe</state>
                </option>
                <option>
                    <name>ObjPath</name>
                    <state>nor_sdram_zf_dtcm\Obj</state>
                </option>
                <option>
                    <name>ListPath</name>
                    <state>nor_sdram_zf_dtcm\List</state>
                </option>
                <option>
                    <name>GEndianMode</name>
                    <state>0</state>
                </option>
                <option>
                    <name>Input description</name>
                    <state>Automatic choice of formatter, without multibyte support.</state>
                </option>
                <option>
                    <name>Output description</name>
                    <state>Automatic choice of formatter, without multibyte support.</state>
                </option>
                <option>
                    <name>GOutputBinary</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGCoreOrChip</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GRuntimeLibSelect</name>
                    <version>0</version>
                    <state>2</state>
                </option>
                <option>
                    <name>GRuntimeLibSelectSlave</name>
                    <version>0</version>
                    <state>2</state>
                </option>
                <option>
                    <name>RTDescription</name>
                    <state>Use the full configuration of the C/C++ runtime library. Full locale interface, C locale, file descriptor support, multibytes in printf and scanf, and hex floats in strtod.</state>
                </option>
                <option>
                    <name>OGProductVersion</name>
                    <state>8.32.3.20186</state>
                </option>
                <option>
                    <name>OGLastSavedByProductVersion</name>
                    <state>9.40.1.63870</state>
                </option>
                <option>
                    <name>OGChipSelectEditMenu</name>
                    <state>MIMXRT1064xxx6A	NXP MIMXRT1064xxx6A</state>
                </option>
                <option>
                    <name>GenLowLevelInterface</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GEndianModeBE</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OGBufferedTerminalOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GenStdoutInterface</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RTConfigPath2</name>
                    <state>$TOOLKIT_DIR$\inc\c\DLib_Config_Full.h</state>
                </option>
                <option>
                    <name>GBECoreSlave</name>
                    <version>33</version>
                    <state>41</state>
                </option>
                <option>
                    <name>OGUseCmsis</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGUseCmsisDspLib</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GRuntimeLibThreads</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CoreVariant</name>
                    <version>33</version>
                    <state>41</state>
                </option>
                <option>
                    <name>GFPUDeviceSlave</name>
                    <state>MIMXRT1064xxx6A	NXP MIMXRT1064xxx6A</state>
                </option>
                <option>
                    <name>FPU2</name>
                    <version>0</version>
                    <state>7</state>
                </option>
                <option>
                    <name>NrRegs</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>NEON</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GFPUCoreSlave2</name>
                    <version>33</version>
                    <state>41</state>
                </option>
                <option>
                    <name>OGCMSISPackSelectDevice</name>
                </option>
                <option>
                    <name>OgLibHeap</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGLibAdditionalLocale</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGPrintfVariant</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OGPrintfMultibyteSupport</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGScanfVariant</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OGScanfMultibyteSupport</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GenLocaleTags</name>
                    <state></state>
                </option>
                <option>
                    <name>GenLocaleDisplayOnly</name>
                    <state></state>
                </option>
                <option>
                    <name>DSPExtension</name>
                    <state>1</state>
                </option>
                <option>
                    <name>TrustZone</name>
                    <state>0</state>
                </option>
                <option>
                    <name>TrustZoneModes</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OGAarch64Abi</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OG_32_64Device</name>
                    <state>0</state>
                </option>
                <option>
                    <name>BuildFilesPath</name>
                    <state>nor_sdram_zf_dtcm</state>
                </option>
                <option>
                    <name>PointerAuthentication</name>
                    <state>0</state>
                </option>
                <option>
                    <name>FPU64</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OG_32_64DeviceCoreSlave</name>
                    <version>33</version>
                    <state>41</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>ICCARM</name>
            <archiveVersion>2</archiveVersion>
            <data>
                <version>38</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>CCDefines</name>
                    <state>CPU_MIMXRT1064DVL6A</state>
                    <state>SKIP_SYSCLK_INIT</state>
                    <state>XIP_EXTERNAL_FLASH=1</state>
                    <state>XIP_BOOT_HEADER_ENABLE=1</state>
                    <state>XIP_BOOT_HEADER_DCD_ENABLE=1</state>
                    <state>PRINTF_FLOAT_ENABLE=1</state>
                    <state>SCANF_FLOAT_ENABLE=1</state>
                    <state>PRINTF_ADVANCED_ENABLE=1</state>
                    <state>SCANF_ADVANCED_ENABLE=1</state>
                    <state>FSL_DRIVER_TRANSFER_DOUBLE_WEAK_IRQ=0</state>
                    <state>USB_STACK_BM</state>
                    <state>DEBUG</state>
                    <state>MCUXPRESSO_SDK</state>
                    <state>SD_ENABLED</state>
                </option>
                <option>
                    <name>CCPreprocFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPreprocComments</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPreprocLine</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCListCFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCMnemonics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCMessages</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListAssFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListAssSource</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCDiagSuppress</name>
                    <state>Pa082,Pa050,Pa039</state>
                </option>
                <option>
                    <name>CCDiagRemark</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagWarning</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagError</name>
                    <state></state>
                </option>
                <option>
                    <name>CCObjPrefix</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCAllowList</name>
                    <version>1</version>
                    <state>00000000</state>
                </option>
                <option>
                    <name>CCDebugInfo</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IEndianMode</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IExtraOptionsCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>CCLangConformance</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCSignedPlainChar</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCRequirePrototypes</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCDiagWarnAreErr</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCCompilerRuntimeInfo</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IFpuProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OutputFile</name>
                    <state>$FILE_BNAME$.o</state>
                </option>
                <option>
                    <name>CCLibConfigHeader</name>
                    <state>1</state>
                </option>
                <option>
                    <name>PreInclude</name>
                    <state></state>
                </option>
                <option>
                    <name>CCIncludePath2</name>
                    <state>$PROJ_DIR$\..\..\user\inc</state>
                    <state>$PROJ_DIR$\..\..\code</state>
                    <state>$PROJ_DIR$\..\..\..\libraries\sdk\startup\iar</state>
                    <state>$PROJ_DIR$\..\..\..\libraries\sdk\deceive</state>
                    <state>$PROJ_DIR$\..\..\..\libraries\sdk\drives</state>
                    <state>$PROJ_DIR$\..\..\..\libraries\sdk\xip</state>
                    <state>$PROJ_DIR$\..\..\..\libraries\sdk\utilities</state>
                    <state>$PROJ_DIR$\..\..\..\libraries\sdk\utilities\debug_console</state>
                    <state>$PROJ_DIR$\..\..\..\libraries\sdk\utilities\str</state>
                    <state>$PROJ_DIR$\..\..\..\libraries\sdk\cmsis_drivers</state>
                    <state>$PROJ_DIR$\..\..\..\libraries\sdk\CMSIS\Include</state>
                    <state>$PROJ_DIR$\..\..\..\libraries\sdk\CMSIS\Driver\Include</state>
                    <state>$PROJ_DIR$\..\..\..\libraries\sdk\components\lists</state>
                    <state>$PROJ_DIR$\..\..\..\libraries\sdk\components\osa</state>
                    <state>$PROJ_DIR$\..\..\..\libraries\sdk\components\serial_manager</state>
                    <state>$PROJ_DIR$\..\..\..\libraries\sdk\components\uart</state>
                    <state>$PROJ_DIR$\..\..\..\libraries\sdk\board</state>
                    <state>$PROJ_DIR$\..\..\..\libraries\components\fatfs\source</state>
                    <state>$PROJ_DIR$\..\..\..\libraries\components\fatfs\source\fsl_sd_disk</state>
                    <state>$PROJ_DIR$\..\..\..\libraries\components\sdmmc</state>
                    <state>$PROJ_DIR$\..\..\..\libraries\components\sdmmc\common</state>
                    <state>$PROJ_DIR$\..\..\..\libraries\components\sdmmc\host\usdhc</state>
                    <state>$PROJ_DIR$\..\..\..\libraries\components\sdmmc\osa</state>
                    <state>$PROJ_DIR$\..\..\..\libraries\components\sdmmc\sd</state>
                    <state>$PROJ_DIR$\..\..\..\libraries\components\usb\device</state>
                    <state>$PROJ_DIR$\..\..\..\libraries\components\usb\include</state>
                    <state>$PROJ_DIR$\..\..\..\libraries\components\usb\phy</state>
                    <state>$PROJ_DIR$\..\..\..\libraries\components\usb\usb_cdc_adapter</state>
                    <state>$PROJ_DIR$\..\..\..\libraries\zf_common</state>
                    <state>$PROJ_DIR$\..\..\..\libraries\zf_driver</state>
                    <state>$PROJ_DIR$\..\..\..\libraries\zf_device</state>
                    <state>$PROJ_DIR$\..\..\..\libraries\zf_components</state>
                </option>
                <option>
                    <name>CCStdIncCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCCodeSection</name>
                    <state>.text</state>
                </option>
                <option>
                    <name>IProcessorMode2</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCOptLevel</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptStrategy</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptLevelSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPosIndRopi</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPosIndRwpi</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPosIndNoDynInit</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccLang</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccCDialect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccAllowVLA</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccStaticDestr</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccCppInlineSemantics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccCmsis</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccFloatSemantics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptimizationNoSizeConstraints</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCNoLiteralPool</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptStrategySlave</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CCGuardCalls</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCEncSource</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEncOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEncOutputBom</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCEncInput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccExceptions2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccRTTI2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OICompilerExtraOption</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCStackProtection</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPointerAutentiction</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCBranchTargetIdentification</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>AARM</name>
            <archiveVersion>2</archiveVersion>
            <data>
                <version>12</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>AObjPrefix</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AEndian</name>
                    <state>1</state>
                </option>
                <option>
                    <name>ACaseSensitivity</name>
                    <state>1</state>
                </option>
                <option>
                    <name>MacroChars</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnWhat</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnOne</name>
                    <state></state>
                </option>
                <option>
                    <name>AWarnRange1</name>
                    <state></state>
                </option>
                <option>
                    <name>AWarnRange2</name>
                    <state></state>
                </option>
                <option>
                    <name>ADebug</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AltRegisterNames</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ADefines</name>
                    <state>__STARTUP_INITIALIZE_NONCACHEDATA</state>
                </option>
                <option>
                    <name>AList</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AListHeader</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AListing</name>
                    <state>1</state>
                </option>
                <option>
                    <name>Includes</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MacDefs</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MacExps</name>
                    <state>1</state>
                </option>
                <option>
                    <name>MacExec</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OnlyAssed</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MultiLine</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PageLengthCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PageLength</name>
                    <state>80</state>
                </option>
                <option>
                    <name>TabSpacing</name>
                    <state>8</state>
                </option>
                <option>
                    <name>AXRef</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefDefines</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefInternal</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefDual</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AFpuProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AOutputFile</name>
                    <state>$FILE_BNAME$.o</state>
                </option>
                <option>
                    <name>ALimitErrorsCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ALimitErrorsEdit</name>
                    <state>100</state>
                </option>
                <option>
                    <name>AIgnoreStdInclude</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AUserIncludes</name>
                    <state>$PROJ_DIR$\..\..\..\Libraries\librares\startup\IAR-ARM</state>
                </option>
                <option>
                    <name>AExtraOptionsCheckV2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AExtraOptionsV2</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmNoLiteralPool</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PreInclude</name>
                    <state></state>
                </option>
                <option>
                    <name>A_32_64Device</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>OBJCOPY</name>
            <archiveVersion>0</archiveVersion>
            <data>
                <version>1</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>OOCOutputFormat</name>
                    <version>3</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OCOutputOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OOCOutputFile</name>
                    <state>RT106X.srec</state>
                </option>
                <option>
                    <name>OOCCommandLineProducer</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OOCObjCopyEnable</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>CUSTOM</name>
            <archiveVersion>3</archiveVersion>
            <data>
                <extensions></extensions>
                <cmdline></cmdline>
                <hasPrio>0</hasPrio>
                <buildSequence>inputOutputBased</buildSequence>
            </data>
        </settings>
        <settings>
            <name>ILINK</name>
            <archiveVersion>0</archiveVersion>
            <data>
                <version>27</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>IlinkLibIOConfig</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkInputFileSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOutputFile</name>
                    <state>RT106X.out</state>
                </option>
                <option>
                    <name>IlinkDebugInfoEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkKeepSymbols</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySymbol</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySegment</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryAlign</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkDefines</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkConfigDefines</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkMapFile</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLogFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogInitialization</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogModule</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogSection</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogVeneer</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfOverride</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkIcfFile</name>
                    <state>$PROJ_DIR$\..\..\IAR\icf\MIMXRT1064xxxxx_flexspi_nor.icf</state>
                </option>
                <option>
                    <name>IlinkIcfFileSlave</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkSuppressDiags</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsRem</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsWarn</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsErr</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkWarningsAreErrors</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkUseExtraOptions</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkLowLevelInterfaceSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkAutoLibEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkAdditionalLibs</name>
                    <state>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_config.a</state>
                </option>
                <option>
                    <name>IlinkOverrideProgramEntryLabel</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkProgramEntryLabelSelect</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkProgramEntryLabel</name>
                    <state>__iar_program_start</state>
                </option>
                <option>
                    <name>DoFill</name>
                    <state>0</state>
                </option>
                <option>
                    <name>FillerByte</name>
                    <state>0xFF</state>
                </option>
                <option>
                    <name>FillerStart</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>FillerEnd</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>CrcSize</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcAlign</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcPoly</name>
                    <state>0x11021</state>
                </option>
                <option>
                    <name>CrcCompl</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcBitOrder</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcInitialValue</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>DoCrc</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkBE8Slave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkBufferedTerminalOutput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkStdoutInterfaceSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcFullSize</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIElfToolPostProcess</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogAutoLibSelect</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogRedirSymbols</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogUnusedFragments</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCrcReverseByteOrder</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCrcUseAsInput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptInline</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOptExceptionsAllow</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptExceptionsForce</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCmsis</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptMergeDuplSections</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOptUseVfe</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptForceVfe</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkStackAnalysisEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkStackControlFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkStackCallGraphFile</name>
                    <state></state>
                </option>
                <option>
                    <name>CrcAlgorithm</name>
                    <version>1</version>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcUnitSize</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkThreadsSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLogCallGraph</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfFile_AltDefault</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkEncInput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkEncOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkEncOutputBom</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkHeapSelect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLocaleSelect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkTrustzoneImportLibraryOut</name>
                    <state>RT106X_import_lib.o</state>
                </option>
                <option>
                    <name>OILinkExtraOption</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkRawBinaryFile2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySymbol2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySegment2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryAlign2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkLogCrtRoutineSelection</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogFragmentInfo</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogInlining</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogMerging</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkDemangle</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkWrapperFileEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkWrapperFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkFpuProcessor</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>IARCHIVE</name>
            <archiveVersion>0</archiveVersion>
            <data>
                <version>0</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>IarchiveInputs</name>
                    <state></state>
                </option>
                <option>
                    <name>IarchiveOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IarchiveOutput</name>
                    <state>###Unitialized###</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>BUILDACTION</name>
            <archiveVersion>2</archiveVersion>
            <data />
        </settings>
    </configuration>
    <group>
        <name>code</name>
        <file>
            <name>$PROJ_DIR$\..\..\code\ce.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\code\ce.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\code\hanzi.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\code\hanzi.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\code\init.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\code\init.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\code\laoban.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\code\laoban.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\code\menu.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\code\menu.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\code\siyuanshu.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\code\siyuanshu.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\code\sxt.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\code\sxt.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\code\uart.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\code\uart.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\code\xiangzi.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\code\xiangzi.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\code\yuansu.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\code\yuansu.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\code\zhuangtaiji.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\code\zhuangtaiji.h</name>
        </file>
    </group>
    <group>
        <name>components_fatfs</name>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\components\fatfs\source\diskio.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\components\fatfs\source\diskio.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\components\fatfs\source\ff.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\components\fatfs\source\ff.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\components\fatfs\source\ffconf.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\components\fatfs\source\ffsystem.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\components\fatfs\source\ffunicode.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\components\fatfs\source\fsl_sd_disk\fsl_sd_disk.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\components\fatfs\source\fsl_sd_disk\fsl_sd_disk.h</name>
        </file>
    </group>
    <group>
        <name>components_sdmmc</name>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\components\sdmmc\sd\fsl_sd.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\components\sdmmc\sd\fsl_sd.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\components\sdmmc\common\fsl_sdmmc_common.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\components\sdmmc\common\fsl_sdmmc_common.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\components\sdmmc\host\usdhc\non_blocking\fsl_sdmmc_host.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\components\sdmmc\host\usdhc\fsl_sdmmc_host.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\components\sdmmc\osa\fsl_sdmmc_osa.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\components\sdmmc\osa\fsl_sdmmc_osa.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\components\sdmmc\common\fsl_sdmmc_spec.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\components\sdmmc\sdmmc_config.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\components\sdmmc\sdmmc_config.h</name>
        </file>
    </group>
    <group>
        <name>components_usb</name>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\components\usb\include\usb.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\components\usb\device\usb_device.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\components\usb\usb_cdc_adapter\usb_device_cdc_acm.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\components\usb\usb_cdc_adapter\usb_device_cdc_acm.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\components\usb\usb_cdc_adapter\usb_device_ch9.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\components\usb\usb_cdc_adapter\usb_device_ch9.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\components\usb\usb_cdc_adapter\usb_device_class.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\components\usb\usb_cdc_adapter\usb_device_class.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\components\usb\usb_cdc_adapter\usb_device_config.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\components\usb\device\usb_device_dci.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\components\usb\device\usb_device_dci.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\components\usb\usb_cdc_adapter\usb_device_descriptor.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\components\usb\usb_cdc_adapter\usb_device_descriptor.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\components\usb\device\usb_device_ehci.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\components\usb\device\usb_device_ehci.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\components\usb\include\usb_misc.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\components\usb\phy\usb_phy.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\components\usb\phy\usb_phy.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\components\usb\include\usb_spec.h</name>
        </file>
    </group>
    <group>
        <name>doc</name>
    </group>
    <group>
        <name>sdk_device</name>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\board\board.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\board\board.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\board\clock_config.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\board\clock_config.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\xip\evkmimxrt1064_flexspi_nor_config.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\xip\evkmimxrt1064_flexspi_nor_config.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\xip\evkmimxrt1064_sdram_ini_dcd.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\xip\evkmimxrt1064_sdram_ini_dcd.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\components\uart\fsl_adapter_lpuart.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\components\uart\fsl_adapter_uart.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\utilities\fsl_assert.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\components\lists\fsl_component_generic_list.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\components\lists\fsl_component_generic_list.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\utilities\debug_console\fsl_debug_console.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\utilities\debug_console\fsl_debug_console.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\utilities\debug_console\fsl_debug_console_conf.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\deceive\fsl_device_registers.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\xip\fsl_flexspi_nor_boot.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\xip\fsl_flexspi_nor_boot.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\components\osa\fsl_os_abstraction.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\components\osa\fsl_os_abstraction_bm.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\components\osa\fsl_os_abstraction_bm.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\components\osa\fsl_os_abstraction_config.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\deceive\MIMXRT1064.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\deceive\MIMXRT1064_features.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\startup\iar\startup_MIMXRT1064.s</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\deceive\system_MIMXRT1064.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\deceive\system_MIMXRT1064.h</name>
        </file>
    </group>
    <group>
        <name>sdk_drivers</name>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_adc.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_adc.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_adc_etc.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_adc_etc.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_aipstz.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_aipstz.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_aoi.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_aoi.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_bee.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_bee.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_cache.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_cache.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_clock.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_clock.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_cmp.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_cmp.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_common.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_common.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_common_arm.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_common_arm.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_csi.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_csi.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_dcdc.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_dcdc.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_dcp.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_dcp.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_dmamux.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_dmamux.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_edma.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_edma.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_elcdif.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_elcdif.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_enc.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_enc.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_enet.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_enet.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_ewm.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_ewm.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_flexcan.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_flexcan.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_flexcan_edma.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_flexcan_edma.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_flexio.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_flexio.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_flexio_camera.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_flexio_camera.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_flexio_camera_edma.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_flexio_camera_edma.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_flexio_i2c_master.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_flexio_i2c_master.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_flexio_i2s.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_flexio_i2s.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_flexio_i2s_edma.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_flexio_i2s_edma.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_flexio_mculcd.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_flexio_mculcd.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_flexio_mculcd_edma.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_flexio_mculcd_edma.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_flexio_spi.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_flexio_spi.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_flexio_spi_edma.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_flexio_spi_edma.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_flexio_uart.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_flexio_uart.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_flexio_uart_edma.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_flexio_uart_edma.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_flexram.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_flexram.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_flexram_allocate.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_flexram_allocate.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_flexspi.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_flexspi.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_flexspi_edma.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_flexspi_edma.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_gpc.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_gpc.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_gpio.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_gpio.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_gpt.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_gpt.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_iomuxc.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_kpp.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_kpp.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_lpi2c.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_lpi2c.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_lpi2c_edma.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_lpi2c_edma.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_lpspi.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_lpspi.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_lpspi_edma.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_lpspi_edma.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_lpuart.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_lpuart.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_lpuart_edma.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_lpuart_edma.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_ocotp.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_ocotp.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_pit.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_pit.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_pmu.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_pmu.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_pwm.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_pwm.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_pxp.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_pxp.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_qtmr.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_qtmr.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_romapi.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_romapi.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_rtwdog.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_rtwdog.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_sai.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_sai.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_sai_edma.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_sai_edma.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_semc.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_semc.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_snvs_hp.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_snvs_hp.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_snvs_lp.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_snvs_lp.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_spdif.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_spdif.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_spdif_edma.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_spdif_edma.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_src.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_src.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_tempmon.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_tempmon.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_trng.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_trng.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_tsc.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_tsc.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_usdhc.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_usdhc.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_wdog.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_wdog.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_xbara.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_xbara.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_xbarb.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\sdk\drives\fsl_xbarb.h</name>
        </file>
    </group>
    <group>
        <name>user_c</name>
        <file>
            <name>$PROJ_DIR$\..\..\user\src\isr.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\user\src\main.c</name>
        </file>
    </group>
    <group>
        <name>user_h</name>
        <file>
            <name>$PROJ_DIR$\..\..\user\inc\isr.h</name>
        </file>
    </group>
    <group>
        <name>zf_common</name>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_common\zf_common_clock.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_common\zf_common_clock.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_common\zf_common_debug.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_common\zf_common_debug.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_common\zf_common_fifo.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_common\zf_common_fifo.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_common\zf_common_font.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_common\zf_common_font.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_common\zf_common_function.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_common\zf_common_function.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_common\zf_common_headfile.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_common\zf_common_interrupt.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_common\zf_common_interrupt.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_common\zf_common_typedef.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_common\zf_common_vector.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_common\zf_common_vector.h</name>
        </file>
    </group>
    <group>
        <name>zf_components</name>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_components\seekfree_assistant.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_components\seekfree_assistant.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_components\seekfree_assistant_interface.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_components\seekfree_assistant_interface.h</name>
        </file>
    </group>
    <group>
        <name>zf_device</name>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_absolute_encoder.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_absolute_encoder.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_ble6a20.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_ble6a20.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_bluetooth_ch9141.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_bluetooth_ch9141.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_camera.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_camera.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_config.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_dl1a.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_dl1a.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_dl1b.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_dl1b.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_gnss.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_gnss.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_icm20602.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_icm20602.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_imu660ra.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_imu660ra.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_imu963ra.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_imu963ra.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_ips114.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_ips114.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_ips200.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_ips200.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_key.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_key.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_mpu6050.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_mpu6050.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_mt9v03x.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_mt9v03x.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_mt9v03x_flexio.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_mt9v03x_flexio.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_oled.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_oled.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_ov7725.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_ov7725.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_scc8660.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_scc8660.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_scc8660_flexio.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_scc8660_flexio.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_tft180.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_tft180.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_tsl1401.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_tsl1401.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_type.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_type.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_virtual_oscilloscope.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_virtual_oscilloscope.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_wifi_spi.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_wifi_spi.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_wifi_uart.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_wifi_uart.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_wireless_uart.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_device\zf_device_wireless_uart.h</name>
        </file>
    </group>
    <group>
        <name>zf_driver</name>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_driver\zf_driver_adc.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_driver\zf_driver_adc.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_driver\zf_driver_csi.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_driver\zf_driver_csi.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_driver\zf_driver_delay.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_driver\zf_driver_delay.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_driver\zf_driver_encoder.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_driver\zf_driver_encoder.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_driver\zf_driver_exti.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_driver\zf_driver_exti.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_driver\zf_driver_flash.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_driver\zf_driver_flash.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_driver\zf_driver_flexio_csi.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_driver\zf_driver_flexio_csi.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_driver\zf_driver_gpio.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_driver\zf_driver_gpio.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_driver\zf_driver_iic.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_driver\zf_driver_iic.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_driver\zf_driver_pit.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_driver\zf_driver_pit.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_driver\zf_driver_pwm.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_driver\zf_driver_pwm.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_driver\zf_driver_romapi.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_driver\zf_driver_romapi.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_driver\zf_driver_sdio.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_driver\zf_driver_sdio.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_driver\zf_driver_soft_iic.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_driver\zf_driver_soft_iic.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_driver\zf_driver_soft_spi.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_driver\zf_driver_soft_spi.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_driver\zf_driver_spi.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_driver\zf_driver_spi.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_driver\zf_driver_timer.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_driver\zf_driver_timer.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_driver\zf_driver_uart.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_driver\zf_driver_uart.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_driver\zf_driver_usb_cdc.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\..\..\libraries\zf_driver\zf_driver_usb_cdc.h</name>
        </file>
    </group>
</project>
