#ifndef _YUANSU_H_
#define _YUANSU_H_

#include "zf_common_headfile.h"

//-------------------------------------------------------------------------------------------------------------------
//  外部变量声明
//-------------------------------------------------------------------------------------------------------------------

// --- 元素状态标志 ---
extern volatile int  Island_State;      // 环岛状态标志
extern volatile int  Left_Island_Flag;  // 左环岛标志
extern volatile int  Right_Island_Flag; // 右环岛标志
extern uint8         Island_Switch;     // 环岛识别开启标志位
extern int           bridge_flag;       // 桥梁标志
extern int           barricade;         // 障碍物标志
extern int           zeber_flag;        // 斑马线标志

//-------------------------------------------------------------------------------------------------------------------
//  外部函数声明
//-------------------------------------------------------------------------------------------------------------------

// --- 主要元素检测函数 ---
void Island_Detect(void);               // 环岛检测
void Cross_Detect(void);                // 十字检测
void Unilateral_Bridge_Detect(void);    // 单边桥检测
void barricade_Detect(void);            // 障碍物检测
void zeber_detect(void);                // 斑马线检测

// --- 辅助检测函数 ---
int  Monotonicity_Change_Right(int start, int end); // 右边界单调性变化检测
int  Monotonicity_Change_Left(int start, int end);  // 左边界单调性变化检测
int  Continuity_Change_Right(int start, int end);   // 右边界连续性变化检测
int  Continuity_Change_Left(int start, int end);    // 左边界连续性变化检测
int  Find_Left_Down_Point(int start, int end);      // 寻找左下角点
int  Find_Left_Up_Point(int start, int end);        // 寻找左上角点
int  Find_Right_Down_Point(int start, int end);     // 寻找右下角点
int  Find_Right_Up_Point(int start, int end);       // 寻找右上角点

// --- 边界处理与绘图 ---
void  K_Add_Boundry_Left(float k, int startX, int startY, int endY);  // 根据斜率补充左边界
void  K_Add_Boundry_Right(float k, int startX, int startY, int endY); // 根据斜率补充右边界
void  K_Draw_Line(float k, int startX, int startY, int endY);         // 根据斜率画线
float Get_Right_K(int start_line, int end_line);                      // 获取右边界斜率
float Get_Left_K(int start_line, int end_line);                       // 获取左边界斜率
extern int fflag;
extern int is_long_straight;  // 从yuansu.c导入长直道标志变量
extern int is_long_straight;  // 从yuansu.c导入长直道标志变量
#endif
