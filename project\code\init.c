#include "zf_common_headfile.h"
void Init(void)
{
    pwm_init(MOTORl_PWM,17000,0);
    pwm_init(MOTORr_PWM,17000,0);
    pwm_init(MOTORb_PWM,17000,0);

    gpio_init(MOTORl_DIR, GPO, 1, GPO_PUSH_PULL);//0后1前
    gpio_init(MOTORr_DIR, GPO, 1, GPO_PUSH_PULL);//0后1前
    gpio_init(MOTORb_DIR, GPO, 0, GPO_PUSH_PULL);//0顺时针

    // gpio_init(B9, GPI, 0, GPI_FLOATING_IN);
    gpio_init(C25, GPI, 0, GPI_FLOATING_IN);//左
    gpio_init(C27, GPI, 0, GPI_FLOATING_IN);//右
    gpio_init(C30, GPI, 0, GPI_FLOATING_IN);
    gpio_init(C7, <PERSON><PERSON>, 0, GPO_PUSH_PULL);

    imu660ra_init();
    gyroOffsetInit();
    tft180_init();
    encoder_dir_init(yq_encode, QTIMER1_ENCODER1_CH1_C0, QTIMER1_ENCODER1_CH2_C1);
    encoder_dir_init(hou_encode, QTIMER4_ENCODER1_CH1_C9, QTIMER4_ENCODER1_CH2_C10);
    encoder_dir_init(zq_encode, QTIMER2_ENCODER1_CH1_C3, QTIMER2_ENCODER1_CH2_C4);
    mt9v03x_init(); // 初始化摄像头
    ImagePerspective_Init();
    uart_init(UART_1, 115200, UART1_TX_B12, UART1_RX_B13);
    uart_init(UART_2, 115200, UART2_TX_B18, UART2_RX_B19);
    uart_init(UART_4, 115200, UART4_TX_C16, UART4_RX_C17);
    uart_rx_interrupt(UART_1, 1); 
    uart_rx_interrupt(UART_2, 1); 
    uart_rx_interrupt(UART_4, 1); 
    interrupt_set_priority(LPUART1_IRQn, 2);
    interrupt_set_priority(LPUART2_IRQn, 1);
    interrupt_set_priority(LPUART4_IRQn, 0);
    menu_init();
    display_menu();

    pit_ms_init(PIT_CH0, 5);  // 5ms中断周期
    pit_ms_init(PIT_CH1, 2); // 2ms中断周期
    pit_ms_init(PIT_CH2, 4); // 4ms中断周期
    pit_ms_init(PIT_CH3, 8); // 8ms中断周期

    
}