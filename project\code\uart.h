#ifndef UART_H
#define UART_H

#define BUF_SIZE 32

extern int box_detected;
extern int pia;
extern int distance_mm;
extern int top_detected;
extern int pia1;
extern int slope;
extern int putbox;
extern int left_right;
void uart1_rx_interrupt_new_handler(void);
void uart1_rx_art(uint8 data);
void rt1064_storageU1(void);
void rt1064_storageU2(void);
void rt1064_storageU4(void);
void uart2_rx_interrupt_new_handler(void);
void uart2_rx_art(uint8 data);
void uart4_rx_interrupt_new_handler(void);
void uart4_rx_art(uint8 data);
// void process_uart1_rx_data(void);
void SendDataToHost();
#endif