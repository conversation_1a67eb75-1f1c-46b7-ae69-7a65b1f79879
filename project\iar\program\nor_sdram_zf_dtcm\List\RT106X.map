###############################################################################
#
# IAR ELF Linker V9.40.1.364/W64 for ARM                  06/Aug/2025  01:18:52
# Copyright 2007-2023 IAR Systems AB.
#
#    Output file  =
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Exe\RT106X.out
#    Map file     =
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\List\RT106X.map
#    Command line =
#        -f D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Exe\RT106X.out.rsp
#        (D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_device_3373403627085545761.dir\board.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\code_17896468042025673432.dir\ce.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_device_3373403627085545761.dir\clock_config.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\components_fatfs_6246014242642884545.dir\diskio.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_device_3373403627085545761.dir\evkmimxrt1064_flexspi_nor_config.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_device_3373403627085545761.dir\evkmimxrt1064_sdram_ini_dcd.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\components_fatfs_6246014242642884545.dir\ff.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\components_fatfs_6246014242642884545.dir\ffsystem.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\components_fatfs_6246014242642884545.dir\ffunicode.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_device_3373403627085545761.dir\fsl_adapter_lpuart.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_adc.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_adc_etc.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_aipstz.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_aoi.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_device_3373403627085545761.dir\fsl_assert.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_bee.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_cache.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_clock.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_cmp.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_common.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_common_arm.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_device_3373403627085545761.dir\fsl_component_generic_list.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_csi.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_dcdc.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_dcp.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_device_3373403627085545761.dir\fsl_debug_console.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_dmamux.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_edma.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_elcdif.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_enc.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_enet.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_ewm.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_flexcan.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_flexcan_edma.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_flexio.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_flexio_camera.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_flexio_camera_edma.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_flexio_i2c_master.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_flexio_i2s.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_flexio_i2s_edma.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_flexio_mculcd.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_flexio_mculcd_edma.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_flexio_spi.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_flexio_spi_edma.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_flexio_uart.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_flexio_uart_edma.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_flexram.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_flexram_allocate.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_flexspi.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_flexspi_edma.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_device_3373403627085545761.dir\fsl_flexspi_nor_boot.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_gpc.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_gpio.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_gpt.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_kpp.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_lpi2c.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_lpi2c_edma.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_lpspi.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_lpspi_edma.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_lpuart.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_lpuart_edma.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_ocotp.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_device_3373403627085545761.dir\fsl_os_abstraction_bm.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_pit.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_pmu.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_pwm.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_pxp.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_qtmr.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_romapi.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_rtwdog.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_sai.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_sai_edma.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\components_sdmmc_11335736203712880765.dir\fsl_sd.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\components_fatfs_6246014242642884545.dir\fsl_sd_disk.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\components_sdmmc_11335736203712880765.dir\fsl_sdmmc_common.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\components_sdmmc_11335736203712880765.dir\fsl_sdmmc_host.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\components_sdmmc_11335736203712880765.dir\fsl_sdmmc_osa.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_semc.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_snvs_hp.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_snvs_lp.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_spdif.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_spdif_edma.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_src.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_tempmon.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_trng.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_tsc.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_usdhc.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_wdog.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_xbara.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_xbarb.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\code_17896468042025673432.dir\hanzi.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\code_17896468042025673432.dir\init.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\user_c_11930989646151335152.dir\isr.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\code_17896468042025673432.dir\laoban.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\user_c_11930989646151335152.dir\main.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\code_17896468042025673432.dir\menu.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\components_sdmmc_11335736203712880765.dir\sdmmc_config.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_components_14936052685886095442.dir\seekfree_assistant.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_components_14936052685886095442.dir\seekfree_assistant_interface.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\code_17896468042025673432.dir\siyuanshu.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_device_3373403627085545761.dir\startup_MIMXRT1064.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\code_17896468042025673432.dir\sxt.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_device_3373403627085545761.dir\system_MIMXRT1064.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\code_17896468042025673432.dir\uart.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\components_usb_17375887477349322284.dir\usb_device_cdc_acm.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\components_usb_17375887477349322284.dir\usb_device_ch9.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\components_usb_17375887477349322284.dir\usb_device_class.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\components_usb_17375887477349322284.dir\usb_device_dci.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\components_usb_17375887477349322284.dir\usb_device_descriptor.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\components_usb_17375887477349322284.dir\usb_device_ehci.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\components_usb_17375887477349322284.dir\usb_phy.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\code_17896468042025673432.dir\xiangzi.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\code_17896468042025673432.dir\yuansu.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_common_1201165462030747823.dir\zf_common_clock.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_common_1201165462030747823.dir\zf_common_debug.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_common_1201165462030747823.dir\zf_common_fifo.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_common_1201165462030747823.dir\zf_common_font.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_common_1201165462030747823.dir\zf_common_function.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_common_1201165462030747823.dir\zf_common_interrupt.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_common_1201165462030747823.dir\zf_common_vector.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_absolute_encoder.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_ble6a20.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_bluetooth_ch9141.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_camera.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_dl1a.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_dl1b.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_gnss.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_icm20602.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_imu660ra.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_imu963ra.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_ips114.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_ips200.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_key.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_mpu6050.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_mt9v03x.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_mt9v03x_flexio.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_oled.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_ov7725.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_scc8660.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_scc8660_flexio.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_tft180.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_tsl1401.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_type.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_virtual_oscilloscope.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_wifi_spi.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_wifi_uart.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_wireless_uart.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir\zf_driver_adc.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir\zf_driver_csi.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir\zf_driver_delay.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir\zf_driver_encoder.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir\zf_driver_exti.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir\zf_driver_flash.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir\zf_driver_flexio_csi.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir\zf_driver_gpio.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir\zf_driver_iic.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir\zf_driver_pit.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir\zf_driver_pwm.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir\zf_driver_romapi.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir\zf_driver_sdio.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir\zf_driver_soft_iic.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir\zf_driver_soft_spi.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir\zf_driver_spi.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir\zf_driver_timer.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir\zf_driver_uart.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir\zf_driver_usb_cdc.o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\code_17896468042025673432.dir\zhuangtaiji.o
#        --no_out_extension -o
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Exe\RT106X.out --map
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\List\RT106X.map
#        --config
#        D:\yuanshi\project\iar\program\..\..\IAR\icf\MIMXRT1064xxxxx_flexspi_nor.icf
#        --semihosting
#        D:\yuanshi\project\iar\program\..\..\..\libraries\zf_device\zf_device_config.a
#        --entry __iar_program_start --vfe --text_out locale --cpu=Cortex-M7
#        --fpu=VFPv5_d16) --dependencies=n
#        D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Exe\RT106X.out.iar_deps
#
###############################################################################

*******************************************************************************
*** RUNTIME MODEL ATTRIBUTES
***

CppFlavor       = *
__CPP_Runtime   = 1
__Heap_Handler  = Basic
__SystemLibrary = DLib
__dlib_version  = 6


*******************************************************************************
*** HEAP SELECTION
***

The basic heap was selected because --advanced_heap
was not specified and the application did not appear to
be primarily optimized for speed.


*******************************************************************************
*** PLACEMENT SUMMARY
***

"A0":  place at address 0x7000'2000 { ro section .intvec };
"A2":  place at address 0x7000'0000 { section .boot_hdr.conf };
"A3":  place at address 0x7000'1000 { section .boot_hdr.ivt };
"A4":  place at address 0x7000'1020 { ro section .boot_hdr.boot_data };
"A5":  place at address 0x7000'1030 { ro section .boot_hdr.dcd_data };
"P1":  place in [from 0x7000'2000 to 0x7000'23ff] |
                [from 0x7000'2400 to 0x703f'ffff] { ro };
define block RW { first rw, section m_usb_dma_init_data };
"P2":  place in [from 0x8000'0000 to 0x8000'03ff] |
                [from 0x8000'0400 to 0x81df'ffff] { block RW };
define block SDRAM_VAR
   with alignment = 8 { section SDRAM_CACHE, section SDRAM_CACHE.init };
"P11": place in [from 0x8000'0000 to 0x8000'03ff] |
                [from 0x8000'0400 to 0x81df'ffff] { block SDRAM_VAR };
define block ZI
   with alignment = 32 { first zi, section m_usb_dma_noninit_data };
"P3":  place in [from 0x2000'0000 to 0x2006'efff] { block ZI };
"P4":  place in [from 0x2000'0000 to 0x2006'efff] { section .data };
"P5":  place in [from 0x2000'0000 to 0x2006'efff] { section .textrw };
define block DTCM_VAR
   with alignment = 8 { section NonCacheable, section NonCacheable.init };
"P9":  place in [from 0x2000'0000 to 0x2006'efff] { block DTCM_VAR };
define block HEAP with size = 1K, alignment = 8 { };
"P6":  place in [from 0x2000'0000 to 0x2006'efff] { last block HEAP };
define block CSTACK with size = 4K, alignment = 8 { };
"P7":  place in [from 0x2006'f000 to 0x2006'ffff] { block CSTACK };
initialize by copy { rw, section .textrw };
initialize by copy { ro }
   except {
      ro section .intvec, ro object system_MIMXRT1064.o,
      ro object startup_MIMXRT1064.o, section .boot_hdr.conf,
      section .boot_hdr.ivt, section .boot_hdr.boot_data,
      section .boot_hdr.dcd_data };
keep {
   section .boot_hdr.conf, section .boot_hdr.ivt, section .boot_hdr.boot_data,
   section .boot_hdr.dcd_data };

No sections matched the following patterns:

  section .intvec_RAM              in "A1"
  section .textrw                  in "P3-P5|P9"
  section ITCM_NonCacheable        in block ITCM_VAR
  section ITCM_NonCacheable.init   in block ITCM_VAR
  section NonCacheable.init        in block DTCM_VAR
  section OCRAM_CACHE              in block OCRAM_VAR
  section OCRAM_CACHE.init         in block OCRAM_VAR
  section SDRAM_CACHE              in block SDRAM_VAR
  section SDRAM_CACHE.init         in block SDRAM_VAR
  section SDRAM_NonCacheable       in block SDRAM_NCACHE_VAR
  section SDRAM_NonCacheable.init  in block SDRAM_NCACHE_VAR
  section m_usb_dma_init_data      in block RW
  section m_usb_dma_noninit_data   in block ZI


  Section              Kind         Address      Size  Object
  -------              ----         -------      ----  ------
"P3-P5|P9", part 1 of 2:                        0xcaa
  P3-P5|P9 s0                   0x2000'0000     0xcaa  <Init block>
    .data              inited   0x2000'0000     0x100  menu.o [1]
    .data              inited   0x2000'0100      0xe0  menu.o [1]
    .data              inited   0x2000'01e0      0x80  menu.o [1]
    .data              inited   0x2000'0260      0x80  menu.o [1]
    .data              inited   0x2000'02e0      0x80  menu.o [1]
    .data              inited   0x2000'0360      0x80  menu.o [1]
    .data              inited   0x2000'03e0      0x80  menu.o [1]
    .data              inited   0x2000'0460      0x80  menu.o [1]
    .data              inited   0x2000'04e0      0x80  menu.o [1]
    .data              inited   0x2000'0560      0x80  menu.o [1]
    .data              inited   0x2000'05e0      0x80  menu.o [1]
    .data              inited   0x2000'0660      0x80  menu.o [1]
    .data              inited   0x2000'06e0      0x80  menu.o [1]
    .data              inited   0x2000'0760      0x80  menu.o [1]
    .data              inited   0x2000'07e0      0x80  menu.o [1]
    .data              inited   0x2000'0860      0x80  menu.o [1]
    .data              inited   0x2000'08e0      0x80  menu.o [1]
    .data              inited   0x2000'0960      0x60  menu.o [1]
    .data              inited   0x2000'09c0      0x54  ce.o [1]
    .data              inited   0x2000'0a14      0x54  ce.o [1]
    .data              inited   0x2000'0a68      0x2c  zf_driver_gpio.o [11]
    .data              inited   0x2000'0a94      0x28  zf_device_mt9v03x.o [10]
    .data              inited   0x2000'0abc      0x24  sxt.o [1]
    .data              inited   0x2000'0ae0      0x24  zf_device_mt9v03x.o [10]
    .data              inited   0x2000'0b04      0x24  zf_driver_uart.o [11]
    .data              inited   0x2000'0b28      0x1c  zhuangtaiji.o [1]
    .data              inited   0x2000'0b44      0x1c  zhuangtaiji.o [1]
    .data              inited   0x2000'0b60      0x1c  zhuangtaiji.o [1]
    .data              inited   0x2000'0b7c      0x1c  zhuangtaiji.o [1]
    .data              inited   0x2000'0b98      0x1c  zhuangtaiji.o [1]
    .data              inited   0x2000'0bb4      0x1c  zhuangtaiji.o [1]
    .data              inited   0x2000'0bd0      0x1c  zhuangtaiji.o [1]
    .data              inited   0x2000'0bec      0x14  zf_driver_encoder.o [11]
    .data              inited   0x2000'0c00      0x14  zf_driver_pwm.o [11]
    .data              inited   0x2000'0c14      0x14  zf_driver_spi.o [11]
    .data              inited   0x2000'0c28       0x8  zf_device_imu660ra.o [10]
    .data              inited   0x2000'0c30       0x4  main.o [7]
    .data              inited   0x2000'0c34       0x4  menu.o [1]
    .data              inited   0x2000'0c38       0x4  menu.o [1]
    .data              inited   0x2000'0c3c       0x4  menu.o [1]
    .data              inited   0x2000'0c40       0x4  menu.o [1]
    .data              inited   0x2000'0c44       0x4  menu.o [1]
    .data              inited   0x2000'0c48       0x4  menu.o [1]
    .data              inited   0x2000'0c4c       0x4  menu.o [1]
    .data              inited   0x2000'0c50       0x4  menu.o [1]
    .data              inited   0x2000'0c54       0x4  menu.o [1]
    .data              inited   0x2000'0c58       0x4  siyuanshu.o [1]
    .data              inited   0x2000'0c5c       0x4  siyuanshu.o [1]
    .data              inited   0x2000'0c60       0x4  siyuanshu.o [1]
    .data              inited   0x2000'0c64       0x4  system_MIMXRT1064.o [5]
    .data              inited   0x2000'0c68       0x4  xiangzi.o [1]
    .data              inited   0x2000'0c6c       0x4  xiangzi.o [1]
    .data              inited   0x2000'0c70       0x4  xiangzi.o [1]
    .data              inited   0x2000'0c74       0x4  xiangzi.o [1]
    .data              inited   0x2000'0c78       0x4  xiangzi.o [1]
    .data              inited   0x2000'0c7c       0x4  xiangzi.o [1]
    .data              inited   0x2000'0c80       0x4  zf_common_clock.o [8]
    .data              inited   0x2000'0c84       0x4  zf_device_type.o [10]
    .data              inited   0x2000'0c88       0x4  zf_device_type.o [10]
    .data              inited   0x2000'0c8c       0x4  zf_device_type.o [10]
    .data              inited   0x2000'0c90       0x4  zf_device_type.o [10]
    .data              inited   0x2000'0c94       0x4  zf_device_type.o [10]
    .data              inited   0x2000'0c98       0x2  zf_device_tft180.o [10]
    .data              inited   0x2000'0c9a       0x1  main.o [7]
    .data              inited   0x2000'0c9b       0x1  main.o [7]
    .data              inited   0x2000'0c9c       0x1  main.o [7]
    .data              inited   0x2000'0c9d       0x1  main.o [7]
    .data              inited   0x2000'0c9e       0x1  sxt.o [1]
    .data              inited   0x2000'0c9f       0x1  sxt.o [1]
    .data              inited   0x2000'0ca0       0x1  sxt.o [1]
    .data              inited   0x2000'0ca1       0x1  sxt.o [1]
    .data              inited   0x2000'0ca2       0x1  xiangzi.o [1]
    .data              inited   0x2000'0ca3       0x1  xiangzi.o [1]
    .data              inited   0x2000'0ca4       0x1  zf_common_debug.o [8]
    .data              inited   0x2000'0ca5       0x1  zf_device_mt9v03x.o [10]
    .data              inited   0x2000'0ca6       0x1  zf_device_tft180.o [10]
    .data              inited   0x2000'0ca7       0x1  zf_device_tft180.o [10]
    .data              inited   0x2000'0ca8       0x1  zf_device_tft180.o [10]
    .data              inited   0x2000'0ca9       0x1  zf_device_tft180.o [10]
                              - 0x2000'0caa     0xcaa

"P3-P5|P9", part 2 of 2:                     0x2'2800
  ZI                            0x2000'0cc0  0x1'91d4  <Block>
    .bss               zero     0x2000'0cc0       0x4  ce.o [1]
    .bss               zero     0x2000'0cc4       0x4  ce.o [1]
    .bss               zero     0x2000'0cc8       0xc  ce.o [1]
    .bss               zero     0x2000'0cd4       0xc  ce.o [1]
    .bss               zero     0x2000'0ce0       0x4  ce.o [1]
    .bss               zero     0x2000'0ce4       0x4  ce.o [1]
    .bss               zero     0x2000'0ce8       0x4  ce.o [1]
    .bss               zero     0x2000'0cec       0x4  ce.o [1]
    .bss               zero     0x2000'0cf0       0x4  fsl_clock.o [6]
    .bss               zero     0x2000'0cf4       0x4  fsl_clock.o [6]
    .bss               zero     0x2000'0cf8       0x4  fsl_csi.o [6]
    .bss               zero     0x2000'0cfc       0x4  fsl_csi.o [6]
    .bss               zero     0x2000'0d00      0x14  fsl_debug_console.o [5]
    .bss               zero     0x2000'0d14      0x80  fsl_edma.o [6]
    .bss               zero     0x2000'0d94       0x8  fsl_enet.o [6]
    .bss               zero     0x2000'0d9c       0x8  fsl_enet.o [6]
    .bss               zero     0x2000'0da4       0x8  fsl_enet.o [6]
    .bss               zero     0x2000'0dac       0x8  fsl_enet.o [6]
    .bss               zero     0x2000'0db4       0x8  fsl_enet.o [6]
    .bss               zero     0x2000'0dbc       0x8  fsl_enet.o [6]
    .bss               zero     0x2000'0dc4      0x10  fsl_flexcan.o [6]
    .bss               zero     0x2000'0dd4       0x4  fsl_flexcan.o [6]
    .bss               zero     0x2000'0dd8       0x8  fsl_flexio.o [6]
    .bss               zero     0x2000'0de0       0x8  fsl_flexio.o [6]
    .bss               zero     0x2000'0de8       0x8  fsl_flexio.o [6]
    .bss               zero     0x2000'0df0       0x4  fsl_lpi2c.o [6]
    .bss               zero     0x2000'0df4      0x14  fsl_lpi2c.o [6]
    .bss               zero     0x2000'0e08       0x4  fsl_lpi2c.o [6]
    .bss               zero     0x2000'0e0c      0x14  fsl_lpi2c.o [6]
    .bss               zero     0x2000'0e20      0x14  fsl_lpspi.o [6]
    .bss               zero     0x2000'0e34       0x4  fsl_lpspi.o [6]
    .bss               zero     0x2000'0e38       0x4  fsl_lpspi.o [6]
    .bss               zero     0x2000'0e3c       0x8  fsl_lpspi.o [6]
    .bss               zero     0x2000'0e44      0x24  fsl_lpuart.o [6]
    .bss               zero     0x2000'0e68       0x4  fsl_lpuart.o [6]
    .bss               zero     0x2000'0e6c      0x20  fsl_sai.o [6]
    .bss               zero     0x2000'0e8c       0x4  fsl_sai.o [6]
    .bss               zero     0x2000'0e90       0x4  fsl_sai.o [6]
    .bss               zero     0x2000'0e94       0x8  fsl_spdif.o [6]
    .bss               zero     0x2000'0e9c       0x4  fsl_spdif.o [6]
    .bss               zero     0x2000'0ea0       0x4  fsl_spdif.o [6]
    .bss               zero     0x2000'0ea4       0xc  fsl_usdhc.o [6]
    .bss               zero     0x2000'0eb0       0x4  fsl_usdhc.o [6]
    .bss               zero     0x2000'0eb4       0x4  isr.o [7]
    .bss               zero     0x2000'0eb8       0x4  isr.o [7]
    .bss               zero     0x2000'0ebc       0x4  isr.o [7]
    .bss               zero     0x2000'0ec0       0x4  main.o [7]
    .bss               zero     0x2000'0ec4       0x4  main.o [7]
    .bss               zero     0x2000'0ec8       0x4  menu.o [1]
    .bss               zero     0x2000'0ecc      0x10  menu.o [1]
    .bss               zero     0x2000'0edc      0x10  menu.o [1]
    .bss               zero     0x2000'0eec      0x10  menu.o [1]
    .bss               zero     0x2000'0efc       0x4  menu.o [1]
    .bss               zero     0x2000'0f00       0x4  menu.o [1]
    .bss               zero     0x2000'0f04       0x4  menu.o [1]
    .bss               zero     0x2000'0f08       0x4  menu.o [1]
    .bss               zero     0x2000'0f0c       0x4  siyuanshu.o [1]
    .bss               zero     0x2000'0f10       0x4  siyuanshu.o [1]
    .bss               zero     0x2000'0f14       0x4  siyuanshu.o [1]
    .bss               zero     0x2000'0f18       0x4  siyuanshu.o [1]
    .bss               zero     0x2000'0f1c       0x4  siyuanshu.o [1]
    .bss               zero     0x2000'0f20       0x4  siyuanshu.o [1]
    .bss               zero     0x2000'0f24       0x4  siyuanshu.o [1]
    .bss               zero     0x2000'0f28       0x4  siyuanshu.o [1]
    .bss               zero     0x2000'0f2c       0x4  siyuanshu.o [1]
    .bss               zero     0x2000'0f30       0x4  siyuanshu.o [1]
    .bss               zero     0x2000'0f34       0x4  siyuanshu.o [1]
    .bss               zero     0x2000'0f38       0x4  siyuanshu.o [1]
    .bss               zero     0x2000'0f3c       0x4  siyuanshu.o [1]
    .bss               zero     0x2000'0f40       0x4  siyuanshu.o [1]
    .bss               zero     0x2000'0f44       0x4  siyuanshu.o [1]
    .bss               zero     0x2000'0f48       0x4  siyuanshu.o [1]
    .bss               zero     0x2000'0f4c       0x4  siyuanshu.o [1]
    .bss               zero     0x2000'0f50       0x4  siyuanshu.o [1]
    .bss               zero     0x2000'0f54  0x1'2c00  sxt.o [1]
    .bss               zero     0x2001'3b54    0x4b00  sxt.o [1]
    .bss               zero     0x2001'8654       0x4  sxt.o [1]
    .bss               zero     0x2001'8658     0x1e0  sxt.o [1]
    .bss               zero     0x2001'8838     0x1e0  sxt.o [1]
    .bss               zero     0x2001'8a18     0x1e0  sxt.o [1]
    .bss               zero     0x2001'8bf8     0x1e0  sxt.o [1]
    .bss               zero     0x2001'8dd8     0x280  sxt.o [1]
    .bss               zero     0x2001'9058       0x4  sxt.o [1]
    .bss               zero     0x2001'905c       0x4  sxt.o [1]
    .bss               zero     0x2001'9060       0x4  sxt.o [1]
    .bss               zero     0x2001'9064       0x4  sxt.o [1]
    .bss               zero     0x2001'9068       0x4  sxt.o [1]
    .bss               zero     0x2001'906c       0x4  sxt.o [1]
    .bss               zero     0x2001'9070       0x8  sxt.o [1]
    .bss               zero     0x2001'9078       0x8  sxt.o [1]
    .bss               zero     0x2001'9080     0x1e0  sxt.o [1]
    .bss               zero     0x2001'9260     0x1e0  sxt.o [1]
    .bss               zero     0x2001'9440       0x4  sxt.o [1]
    .bss               zero     0x2001'9444       0x4  sxt.o [1]
    .bss               zero     0x2001'9448       0x4  sxt.o [1]
    .bss               zero     0x2001'944c       0x4  sxt.o [1]
    .bss               zero     0x2001'9450       0x4  sxt.o [1]
    .bss               zero     0x2001'9454       0x4  sxt.o [1]
    .bss               zero     0x2001'9458     0x348  sxt.o [1]
    .bss               zero     0x2001'97a0     0x348  sxt.o [1]
    .bss               zero     0x2001'9ae8       0x4  sxt.o [1]
    .bss               zero     0x2001'9aec       0x4  sxt.o [1]
    .bss               zero     0x2001'9af0       0x4  uart.o [1]
    .bss               zero     0x2001'9af4       0x8  uart.o [1]
    .bss               zero     0x2001'9afc       0x4  uart.o [1]
    .bss               zero     0x2001'9b00       0x4  uart.o [1]
    .bss               zero     0x2001'9b04       0x4  uart.o [1]
    .bss               zero     0x2001'9b08       0x4  uart.o [1]
    .bss               zero     0x2001'9b0c       0x8  uart.o [1]
    .bss               zero     0x2001'9b14       0x4  uart.o [1]
    .bss               zero     0x2001'9b18       0x8  uart.o [1]
    .bss               zero     0x2001'9b20       0x4  uart.o [1]
    .bss               zero     0x2001'9b24       0x4  uart.o [1]
    .bss               zero     0x2001'9b28       0x4  uart.o [1]
    .bss               zero     0x2001'9b2c       0x4  xiangzi.o [1]
    .bss               zero     0x2001'9b30       0x4  xiangzi.o [1]
    .bss               zero     0x2001'9b34       0x4  xiangzi.o [1]
    .bss               zero     0x2001'9b38       0x4  xiangzi.o [1]
    .bss               zero     0x2001'9b3c       0x4  xiangzi.o [1]
    .bss               zero     0x2001'9b40       0x4  xiangzi.o [1]
    .bss               zero     0x2001'9b44       0x4  xiangzi.o [1]
    .bss               zero     0x2001'9b48       0x4  xiangzi.o [1]
    .bss               zero     0x2001'9b4c     0x12c  xiangzi.o [1]
    .bss               zero     0x2001'9c78       0x4  xiangzi.o [1]
    .bss               zero     0x2001'9c7c       0x4  xiangzi.o [1]
    .bss               zero     0x2001'9c80       0x4  xiangzi.o [1]
    .bss               zero     0x2001'9c84       0x4  xiangzi.o [1]
    .bss               zero     0x2001'9c88       0x4  xiangzi.o [1]
    .bss               zero     0x2001'9c8c       0x4  xiangzi.o [1]
    .bss               zero     0x2001'9c90       0x4  xiangzi.o [1]
    .bss               zero     0x2001'9c94       0x4  xiangzi.o [1]
    .bss               zero     0x2001'9c98       0x4  xiangzi.o [1]
    .bss               zero     0x2001'9c9c       0x4  xiangzi.o [1]
    .bss               zero     0x2001'9ca0       0x4  xiangzi.o [1]
    .bss               zero     0x2001'9ca4       0x4  xiangzi.o [1]
    .bss               zero     0x2001'9ca8       0x4  xiangzi.o [1]
    .bss               zero     0x2001'9cac       0x4  xiangzi.o [1]
    .bss               zero     0x2001'9cb0       0x4  xiangzi.o [1]
    .bss               zero     0x2001'9cb4       0x4  xiangzi.o [1]
    .bss               zero     0x2001'9cb8       0x4  xiangzi.o [1]
    .bss               zero     0x2001'9cbc       0x4  xiangzi.o [1]
    .bss               zero     0x2001'9cc0       0x4  xiangzi.o [1]
    .bss               zero     0x2001'9cc4       0x4  xiangzi.o [1]
    .bss               zero     0x2001'9cc8       0x4  xiangzi.o [1]
    .bss               zero     0x2001'9ccc       0x4  xiangzi.o [1]
    .bss               zero     0x2001'9cd0       0x4  xiangzi.o [1]
    .bss               zero     0x2001'9cd4       0x4  yuansu.o [1]
    .bss               zero     0x2001'9cd8       0x4  yuansu.o [1]
    .bss               zero     0x2001'9cdc       0x4  yuansu.o [1]
    .bss               zero     0x2001'9ce0       0xc  yuansu.o [1]
    .bss               zero     0x2001'9cec       0xc  yuansu.o [1]
    .bss               zero     0x2001'9cf8       0x4  yuansu.o [1]
    .bss               zero     0x2001'9cfc       0x4  yuansu.o [1]
    .bss               zero     0x2001'9d00       0x4  yuansu.o [1]
    .bss               zero     0x2001'9d04       0x4  yuansu.o [1]
    .bss               zero     0x2001'9d08       0x8  yuansu.o [1]
    .bss               zero     0x2001'9d10       0x8  yuansu.o [1]
    .bss               zero     0x2001'9d18       0x4  yuansu.o [1]
    .bss               zero     0x2001'9d1c      0x40  zf_common_debug.o [8]
    .bss               zero     0x2001'9d5c      0x18  zf_common_debug.o [8]
    .bss               zero     0x2001'9d74      0x14  zf_common_debug.o [8]
    .bss               zero     0x2001'9d88       0x4  zf_common_interrupt.o [8]
    .bss               zero     0x2001'9d8c      0x18  zf_device_camera.o [10]
    .bss               zero     0x2001'9da4       0x8  zf_device_camera.o [10]
    .bss               zero     0x2001'9dac       0x4  zf_device_mt9v03x.o [10]
    .bss               zero     0x2001'9db0      0x38  zf_driver_csi.o [11]
    .bss               zero     0x2001'9de8       0xc  zf_driver_spi.o [11]
    .bss               zero     0x2001'9df4      0x10  zf_driver_usb_cdc.o [11]
    .bss               zero     0x2001'9e04       0x4  zhuangtaiji.o [1]
    .bss               zero     0x2001'9e08       0x4  zhuangtaiji.o [1]
    .bss               zero     0x2001'9e0c       0x4  zhuangtaiji.o [1]
    .bss               zero     0x2001'9e10       0x4  zhuangtaiji.o [1]
    .bss               zero     0x2001'9e14       0x4  zhuangtaiji.o [1]
    .bss               zero     0x2001'9e18      0x14  zf_device_config.o [16]
    .bss               zero     0x2001'9e2c      0x10  zf_device_config.o [16]
    .bss               zero     0x2001'9e3c       0x4  zf_device_config.o [16]
    .bss               zero     0x2001'9e40       0x4  zf_device_config.o [16]
    .bss               zero     0x2001'9e44       0x8  heap0.o [12]
    .bss               zero     0x2001'9e4c       0x4  xgetmemchunk.o [12]
    .bss               zero     0x2001'9e50       0x2  sxt.o [1]
    .bss               zero     0x2001'9e52       0x2  sxt.o [1]
    .bss               zero     0x2001'9e54       0x2  sxt.o [1]
    .bss               zero     0x2001'9e56       0x2  sxt.o [1]
    .bss               zero     0x2001'9e58       0x2  sxt.o [1]
    .bss               zero     0x2001'9e5a       0x2  sxt.o [1]
    .bss               zero     0x2001'9e5c       0x2  sxt.o [1]
    .bss               zero     0x2001'9e5e       0x2  sxt.o [1]
    .bss               zero     0x2001'9e60       0x2  sxt.o [1]
    .bss               zero     0x2001'9e62       0x2  sxt.o [1]
    .bss               zero     0x2001'9e64       0x2  zf_device_imu660ra.o [10]
    .bss               zero     0x2001'9e66       0x2  zf_device_imu660ra.o [10]
    .bss               zero     0x2001'9e68       0x2  zf_device_imu660ra.o [10]
    .bss               zero     0x2001'9e6a       0x2  zf_device_imu660ra.o [10]
    .bss               zero     0x2001'9e6c       0x2  zf_device_imu660ra.o [10]
    .bss               zero     0x2001'9e6e       0x2  zf_device_imu660ra.o [10]
    .bss               zero     0x2001'9e70       0x2  zf_device_mt9v03x.o [10]
    .bss               zero     0x2001'9e72       0x2  zf_device_tft180.o [10]
    .bss               zero     0x2001'9e74       0x1  isr.o [7]
    .bss               zero     0x2001'9e75       0x1  isr.o [7]
    .bss               zero     0x2001'9e76       0x1  main.o [7]
    .bss               zero     0x2001'9e77       0x1  sxt.o [1]
    .bss               zero     0x2001'9e78       0x1  sxt.o [1]
    .bss               zero     0x2001'9e79       0x1  sxt.o [1]
    .bss               zero     0x2001'9e7a       0x1  sxt.o [1]
    .bss               zero     0x2001'9e7b       0x1  sxt.o [1]
    .bss               zero     0x2001'9e7c       0x1  sxt.o [1]
    .bss               zero     0x2001'9e7d       0x1  sxt.o [1]
    .bss               zero     0x2001'9e7e       0x1  sxt.o [1]
    .bss               zero     0x2001'9e7f       0x1  uart.o [1]
    .bss               zero     0x2001'9e80       0x1  uart.o [1]
    .bss               zero     0x2001'9e81       0x1  uart.o [1]
    .bss               zero     0x2001'9e82       0x1  uart.o [1]
    .bss               zero     0x2001'9e83       0x1  uart.o [1]
    .bss               zero     0x2001'9e84       0x1  uart.o [1]
    .bss               zero     0x2001'9e85       0x1  xiangzi.o [1]
    .bss               zero     0x2001'9e86       0x1  xiangzi.o [1]
    .bss               zero     0x2001'9e87       0x1  xiangzi.o [1]
    .bss               zero     0x2001'9e88       0x1  xiangzi.o [1]
    .bss               zero     0x2001'9e89       0x1  xiangzi.o [1]
    .bss               zero     0x2001'9e8a       0x1  xiangzi.o [1]
    .bss               zero     0x2001'9e8b       0x1  xiangzi.o [1]
    .bss               zero     0x2001'9e8c       0x1  xiangzi.o [1]
    .bss               zero     0x2001'9e8d       0x1  xiangzi.o [1]
    .bss               zero     0x2001'9e8e       0x1  zf_common_debug.o [8]
    .bss               zero     0x2001'9e8f       0x1  zf_common_debug.o [8]
    .bss               zero     0x2001'9e90       0x1  zf_common_debug.o [8]
    .bss               zero     0x2001'9e91       0x1  zf_device_mt9v03x.o [10]
    .bss               zero     0x2001'9e92       0x1  zf_device_type.o [10]
    .bss               zero     0x2001'9e93       0x1  zf_driver_pit.o [11]
  DTCM_VAR                      0x2001'9ec0    0x9628  <Block>
    NonCacheable       zero     0x2001'9ec0    0x4b00  zf_device_mt9v03x.o [10]
    NonCacheable       zero     0x2001'e9c0    0x4b00  zf_device_mt9v03x.o [10]
                              - 0x2002'34c0  0x2'2800

"P6":                                           0x400
  HEAP                          0x2002'34c0     0x400  <Block>
    HEAP               uninit   0x2002'34c0     0x400  <Block tail>
                              - 0x2002'38c0     0x400

"P7":                                          0x1000
  CSTACK                        0x2006'f000    0x1000  <Block>
    CSTACK             uninit   0x2006'f000    0x1000  <Block tail>
                              - 0x2007'0000    0x1000

"A2":                                           0x200
  .boot_hdr.conf       const    0x7000'0000     0x200  evkmimxrt1064_flexspi_nor_config.o [5]
                              - 0x7000'0200     0x200

"A3":                                            0x20
  .boot_hdr.ivt        const    0x7000'1000      0x20  fsl_flexspi_nor_boot.o [5]
                              - 0x7000'1020      0x20

"A4":                                            0x10
  .boot_hdr.boot_data  const    0x7000'1020      0x10  fsl_flexspi_nor_boot.o [5]
                              - 0x7000'1030      0x10

"A5":                                           0x410
  .boot_hdr.dcd_data   const    0x7000'1030     0x410  evkmimxrt1064_sdram_ini_dcd.o [5]
                              - 0x7000'1440     0x410

"A0":                                           0x440
  .intvec              const    0x7000'2000     0x400  zf_common_vector.o [8]
  .intvec              ro code  0x7000'2400      0x40  vector_table_M.o [14]
                              - 0x7000'2440     0x440

"P1":                                          0xfbe6
  .text                ro code  0x7000'2440     0x1f8  lz77_init.o [14]
  .text                ro code  0x7000'2638       0x6  abort.o [12]
  .text                ro code  0x7000'2640      0x14  exit.o [15]
  .text                ro code  0x7000'2654     0x124  system_MIMXRT1064.o [5]
  .text                ro code  0x7000'2778       0x2  system_MIMXRT1064.o [5]
  .text                ro code  0x7000'277c      0x6c  startup_MIMXRT1064.o [5]
  .text                ro code  0x7000'27e8      0x38  zero_init3.o [14]
  .text                ro code  0x7000'2820      0x28  data_init.o [14]
  .text                ro code  0x7000'2848      0x22  fpinit_M.o [13]
  .iar.init_table      const    0x7000'286c      0x50  - Linker created -
  Veneer               ro code  0x7000'28bc       0x8  - Linker created -
  Veneer               ro code  0x7000'28c4       0x8  - Linker created -
  .text                ro code  0x7000'28cc      0x1e  cmain.o [14]
  .text                ro code  0x7000'28ea       0x4  low_level_init.o [12]
  .text                ro code  0x7000'28f0      0x1c  cstartup_M.o [14]
  .rodata              const    0x7000'290c       0x0  zero_init3.o [14]
  .rodata              const    0x7000'290c       0x0  lz77_init.o [14]
  Initializer bytes    const    0x7000'290c    0xf40f  <for rw-1>
  Initializer bytes    const    0x7001'1d1b     0x30b  <for P3-P5|P9 s0>
                              - 0x7001'2026    0xfbe6

"P2|P11":                                    0x1'6ee4
  RW                            0x8000'0400  0x1'6ee4  <Block>
    rw-1                        0x8000'0400  0x1'6ee4  <Init block>
      .rodata          inited   0x8000'0400      0x48  sxt.o [1]
      .rodata          inited   0x8000'0448     0x420  pow64.o [13]
      .text            inited   0x8000'0868      0x88  asin.o [13]
      .text            inited   0x8000'08f0      0x80  acos.o [13]
      .text            inited   0x8000'0970     0x118  xatan.o [13]
      .rodata          inited   0x8000'0a88       0xc  ce.o [1]
      .rodata          inited   0x8000'0a94       0x4  ce.o [1]
      .rodata          inited   0x8000'0a98       0x4  ce.o [1]
      .rodata          inited   0x8000'0a9c       0x4  ce.o [1]
      .rodata          inited   0x8000'0aa0      0x18  clock_config.o [5]
      .rodata          inited   0x8000'0ab8      0x2c  clock_config.o [5]
      .rodata          inited   0x8000'0ae4      0x10  clock_config.o [5]
      .rodata          inited   0x8000'0af4       0x8  clock_config.o [5]
      .rodata          inited   0x8000'0afc      0x2c  fsl_assert.o [5]
      .rodata          inited   0x8000'0b28      0x10  fsl_clock.o [6]
      .rodata          inited   0x8000'0b38      0x2c  fsl_clock.o [6]
      .rodata          inited   0x8000'0b64      0x10  fsl_clock.o [6]
      .rodata          inited   0x8000'0b74      0x10  fsl_csi.o [6]
      .rodata          inited   0x8000'0b84      0x2c  fsl_csi.o [6]
      .rodata          inited   0x8000'0bb0      0x3c  fsl_csi.o [6]
      .rodata          inited   0x8000'0bec      0x2c  fsl_csi.o [6]
      .rodata          inited   0x8000'0c18       0xc  fsl_csi.o [6]
      .rodata          inited   0x8000'0c24       0xc  fsl_csi.o [6]
      .rodata          inited   0x8000'0c30       0x4  fsl_csi.o [6]
      .rodata          inited   0x8000'0c34      0x2c  fsl_edma.o [6]
      .rodata          inited   0x8000'0c60      0x1c  fsl_edma.o [6]
      .rodata          inited   0x8000'0c7c       0xc  fsl_edma.o [6]
      .rodata          inited   0x8000'0c88      0x3c  fsl_enet.o [6]
      .rodata          inited   0x8000'0cc4      0x2c  fsl_enet.o [6]
      .rodata          inited   0x8000'0cf0       0x8  fsl_enet.o [6]
      .rodata          inited   0x8000'0cf8      0x30  fsl_flexcan.o [6]
      .rodata          inited   0x8000'0d28      0x18  fsl_flexcan.o [6]
      .rodata          inited   0x8000'0d40      0x18  fsl_flexcan.o [6]
      .rodata          inited   0x8000'0d58      0x18  fsl_flexcan.o [6]
      .rodata          inited   0x8000'0d70      0x10  fsl_gpio.o [6]
      .rodata          inited   0x8000'0d80      0x2c  fsl_gpio.o [6]
      .rodata          inited   0x8000'0dac      0x3c  fsl_gpio.o [6]
      .rodata          inited   0x8000'0de8      0x2c  fsl_gpio.o [6]
      .rodata          inited   0x8000'0e14       0xc  fsl_gpio.o [6]
      .rodata          inited   0x8000'0e20      0x2c  fsl_gpio.o [6]
      .rodata          inited   0x8000'0e4c       0xc  fsl_gpio.o [6]
      .rodata          inited   0x8000'0e58      0x10  fsl_gpt.o [6]
      .rodata          inited   0x8000'0e68      0x2c  fsl_gpt.o [6]
      .rodata          inited   0x8000'0e94      0x1c  fsl_gpt.o [6]
      .rodata          inited   0x8000'0eb0      0x2c  fsl_gpt.o [6]
      .rodata          inited   0x8000'0edc      0x3c  fsl_gpt.o [6]
      .rodata          inited   0x8000'0f18      0x2c  fsl_gpt.o [6]
      .rodata          inited   0x8000'0f44      0x10  fsl_gpt.o [6]
      .rodata          inited   0x8000'0f54       0xc  fsl_gpt.o [6]
      .rodata          inited   0x8000'0f60       0xc  fsl_gpt.o [6]
      .rodata          inited   0x8000'0f6c       0x8  fsl_gpt.o [6]
      .rodata          inited   0x8000'0f74      0x10  fsl_lpspi.o [6]
      .rodata          inited   0x8000'0f84      0x2c  fsl_lpspi.o [6]
      .rodata          inited   0x8000'0fb0      0x40  fsl_lpspi.o [6]
      .rodata          inited   0x8000'0ff0      0x2c  fsl_lpspi.o [6]
      .rodata          inited   0x8000'101c      0x14  fsl_lpspi.o [6]
      .rodata          inited   0x8000'1030      0x18  fsl_lpspi.o [6]
      .rodata          inited   0x8000'1048      0x10  fsl_lpspi.o [6]
      .rodata          inited   0x8000'1058       0xc  fsl_lpspi.o [6]
      .rodata          inited   0x8000'1064       0xc  fsl_lpspi.o [6]
      .rodata          inited   0x8000'1070      0x18  fsl_lpspi.o [6]
      .rodata          inited   0x8000'1088      0x18  fsl_lpspi.o [6]
      .rodata          inited   0x8000'10a0      0x18  fsl_lpspi.o [6]
      .rodata          inited   0x8000'10b8      0x18  fsl_lpspi.o [6]
      .rodata          inited   0x8000'10d0       0x8  fsl_lpspi.o [6]
      .rodata          inited   0x8000'10d8      0x14  fsl_lpspi.o [6]
      .rodata          inited   0x8000'10ec       0xc  fsl_lpspi.o [6]
      .rodata          inited   0x8000'10f8      0x10  fsl_lpuart.o [6]
      .rodata          inited   0x8000'1108      0x2c  fsl_lpuart.o [6]
      .rodata          inited   0x8000'1134      0x40  fsl_lpuart.o [6]
      .rodata          inited   0x8000'1174      0x30  fsl_lpuart.o [6]
      .rodata          inited   0x8000'11a4       0xc  fsl_lpuart.o [6]
      .rodata          inited   0x8000'11b0       0xc  fsl_lpuart.o [6]
      .rodata          inited   0x8000'11bc      0x1c  fsl_lpuart.o [6]
      .rodata          inited   0x8000'11d8      0x28  fsl_lpuart.o [6]
      .rodata          inited   0x8000'1200      0x28  fsl_lpuart.o [6]
      .rodata          inited   0x8000'1228      0x24  fsl_lpuart.o [6]
      .rodata          inited   0x8000'124c      0x14  fsl_lpuart.o [6]
      .rodata          inited   0x8000'1260      0x10  fsl_pit.o [6]
      .rodata          inited   0x8000'1270      0x2c  fsl_pit.o [6]
      .rodata          inited   0x8000'129c      0x3c  fsl_pit.o [6]
      .rodata          inited   0x8000'12d8      0x2c  fsl_pit.o [6]
      .rodata          inited   0x8000'1304       0xc  fsl_pit.o [6]
      .rodata          inited   0x8000'1310       0x4  fsl_pit.o [6]
      .rodata          inited   0x8000'1314      0x10  fsl_pwm.o [6]
      .rodata          inited   0x8000'1324      0x2c  fsl_pwm.o [6]
      .rodata          inited   0x8000'1350      0x3c  fsl_pwm.o [6]
      .rodata          inited   0x8000'138c      0x2c  fsl_pwm.o [6]
      .rodata          inited   0x8000'13b8       0x8  fsl_pwm.o [6]
      .rodata          inited   0x8000'13c0       0xc  fsl_pwm.o [6]
      .rodata          inited   0x8000'13cc       0xc  fsl_pwm.o [6]
      .rodata          inited   0x8000'13d8       0xc  fsl_pwm.o [6]
      .rodata          inited   0x8000'13e4       0xc  fsl_pwm.o [6]
      .rodata          inited   0x8000'13f0      0x1c  fsl_pwm.o [6]
      .rodata          inited   0x8000'140c      0x14  fsl_pwm.o [6]
      .rodata          inited   0x8000'1420      0x28  fsl_pwm.o [6]
      .rodata          inited   0x8000'1448      0x10  fsl_qtmr.o [6]
      .rodata          inited   0x8000'1458      0x2c  fsl_qtmr.o [6]
      .rodata          inited   0x8000'1484      0x3c  fsl_qtmr.o [6]
      .rodata          inited   0x8000'14c0      0x2c  fsl_qtmr.o [6]
      .rodata          inited   0x8000'14ec       0xc  fsl_qtmr.o [6]
      .rodata          inited   0x8000'14f8      0x14  fsl_qtmr.o [6]
      .rodata          inited   0x8000'150c       0xc  fsl_qtmr.o [6]
      .rodata          inited   0x8000'1518      0x2c  fsl_sai.o [6]
      .rodata          inited   0x8000'1544      0x18  fsl_sai.o [6]
      .rodata          inited   0x8000'155c      0x18  fsl_sai.o [6]
      .rodata          inited   0x8000'1574       0xc  fsl_usdhc.o [6]
      .rodata          inited   0x8000'1580     0x500  hanzi.o [1]
      .rodata          inited   0x8000'1a80      0x20  hanzi.o [1]
      .rodata          inited   0x8000'1aa0       0xc  menu.o [1]
      .rodata          inited   0x8000'1aac       0x4  menu.o [1]
      .rodata          inited   0x8000'1ab0       0x8  menu.o [1]
      .rodata          inited   0x8000'1ab8       0x8  menu.o [1]
      .rodata          inited   0x8000'1ac0       0x8  menu.o [1]
      .rodata          inited   0x8000'1ac8       0x8  menu.o [1]
      .rodata          inited   0x8000'1ad0       0x8  menu.o [1]
      .rodata          inited   0x8000'1ad8       0x8  menu.o [1]
      .rodata          inited   0x8000'1ae0       0x8  menu.o [1]
      .rodata          inited   0x8000'1ae8       0x8  menu.o [1]
      .rodata          inited   0x8000'1af0       0x8  menu.o [1]
      .rodata          inited   0x8000'1af8       0x8  menu.o [1]
      .rodata          inited   0x8000'1b00       0x4  menu.o [1]
      .rodata          inited   0x8000'1b04       0x4  menu.o [1]
      .rodata          inited   0x8000'1b08       0x4  menu.o [1]
      .rodata          inited   0x8000'1b0c       0x4  menu.o [1]
      .rodata          inited   0x8000'1b10       0x8  menu.o [1]
      .rodata          inited   0x8000'1b18       0x8  menu.o [1]
      .rodata          inited   0x8000'1b20       0x8  menu.o [1]
      .rodata          inited   0x8000'1b28       0x4  menu.o [1]
      .rodata          inited   0x8000'1b2c       0x4  menu.o [1]
      .rodata          inited   0x8000'1b30       0x4  menu.o [1]
      .rodata          inited   0x8000'1b34       0xc  menu.o [1]
      .rodata          inited   0x8000'1b40       0xc  menu.o [1]
      .rodata          inited   0x8000'1b4c       0xc  menu.o [1]
      .rodata          inited   0x8000'1b58       0x8  menu.o [1]
      .rodata          inited   0x8000'1b60       0x8  menu.o [1]
      .rodata          inited   0x8000'1b68       0xc  menu.o [1]
      .rodata          inited   0x8000'1b74       0xc  menu.o [1]
      .rodata          inited   0x8000'1b80       0x4  menu.o [1]
      .rodata          inited   0x8000'1b84       0x4  menu.o [1]
      .rodata          inited   0x8000'1b88       0x4  menu.o [1]
      .rodata          inited   0x8000'1b8c       0x4  menu.o [1]
      .rodata          inited   0x8000'1b90       0x4  menu.o [1]
      .rodata          inited   0x8000'1b94       0x4  menu.o [1]
      .rodata          inited   0x8000'1b98       0xc  zf_common_debug.o [8]
      .rodata          inited   0x8000'1ba4      0x10  zf_common_debug.o [8]
      .rodata          inited   0x8000'1bb4       0xc  zf_common_debug.o [8]
      .rodata          inited   0x8000'1bc0      0x1c  zf_common_debug.o [8]
      .rodata          inited   0x8000'1bdc      0x18  zf_common_debug.o [8]
      .rodata          inited   0x8000'1bf4      0x10  zf_common_debug.o [8]
      .rodata          inited   0x8000'1c04       0xc  zf_common_debug.o [8]
      .rodata          inited   0x8000'1c10      0x30  zf_common_fifo.o [8]
      .rodata          inited   0x8000'1c40     0x5f0  zf_common_font.o [8]
      .rodata          inited   0x8000'2230     0x228  zf_common_font.o [8]
      .rodata          inited   0x8000'2458      0x34  zf_common_function.o [8]
      .rodata          inited   0x8000'248c      0x1c  zf_device_imu660ra.o [10]
      .rodata          inited   0x8000'24a8      0x34  zf_device_imu660ra.o [10]
      .rodata          inited   0x8000'24dc      0x18  zf_device_imu660ra.o [10]
      .rodata          inited   0x8000'24f4      0x1c  zf_device_mt9v03x.o [10]
      .rodata          inited   0x8000'2510      0x34  zf_device_mt9v03x.o [10]
      .rodata          inited   0x8000'2544      0x1c  zf_device_mt9v03x.o [10]
      .rodata          inited   0x8000'2560      0x34  zf_device_tft180.o [10]
      .rodata          inited   0x8000'2594      0x18  zf_driver_csi.o [11]
      .rodata          inited   0x8000'25ac      0x2c  zf_driver_csi.o [11]
      .rodata          inited   0x8000'25d8      0x30  zf_driver_csi.o [11]
      .rodata          inited   0x8000'2608      0x28  zf_driver_delay.o [11]
      .rodata          inited   0x8000'2630      0x2c  zf_driver_delay.o [11]
      .rodata          inited   0x8000'265c      0x34  zf_driver_encoder.o [11]
      .rodata          inited   0x8000'2690       0xc  zf_driver_gpio.o [11]
      .rodata          inited   0x8000'269c      0x2c  zf_driver_gpio.o [11]
      .rodata          inited   0x8000'26c8       0xc  zf_driver_pit.o [11]
      .rodata          inited   0x8000'26d4      0x2c  zf_driver_pit.o [11]
      .rodata          inited   0x8000'2700       0xc  zf_driver_pit.o [11]
      .rodata          inited   0x8000'270c      0x30  zf_driver_pwm.o [11]
      .rodata          inited   0x8000'273c      0x34  zf_driver_soft_iic.o [11]
      .rodata          inited   0x8000'2770      0x18  zf_driver_spi.o [11]
      .rodata          inited   0x8000'2788      0x2c  zf_driver_spi.o [11]
      .rodata          inited   0x8000'27b4      0x30  zf_driver_spi.o [11]
      .rodata          inited   0x8000'27e4      0x30  zf_driver_uart.o [11]
      .rodata          inited   0x8000'2814    0x2000  zf_device_config.o [16]
      .text            inited   0x8000'4814     0x2ac  board.o [5]
      .text            inited   0x8000'4ac0     0x4bc  ce.o [1]
      .text            inited   0x8000'4f7c     0x6f8  clock_config.o [5]
      .text            inited   0x8000'5674      0x1c  fsl_assert.o [5]
      .text            inited   0x8000'5690     0x820  fsl_clock.o [6]
      .text            inited   0x8000'5eb0     0x668  fsl_csi.o [6]
      .text            inited   0x8000'6518     0xb2c  fsl_debug_console.o [5]
      .text            inited   0x8000'7044     0x4d0  fsl_edma.o [6]
      .text            inited   0x8000'7514     0x150  fsl_enet.o [6]
      .text            inited   0x8000'7664      0xa4  fsl_flexcan.o [6]
      .text            inited   0x8000'7708      0x68  fsl_flexio.o [6]
      .text            inited   0x8000'7770     0x216  fsl_gpio.o [6]
      .text            inited   0x8000'7988     0x24c  fsl_gpt.o [6]
      .text            inited   0x8000'7bd4      0x94  fsl_lpi2c.o [6]
      .text            inited   0x8000'7c68     0xc38  fsl_lpspi.o [6]
      .text            inited   0x8000'88a0     0x55c  fsl_lpuart.o [6]
      .text            inited   0x8000'8dfc     0x164  fsl_pit.o [6]
      .text            inited   0x8000'8f60     0xb28  fsl_pwm.o [6]
      .text            inited   0x8000'9a88     0x1e4  fsl_qtmr.o [6]
      .text            inited   0x8000'9c6c     0x148  fsl_sai.o [6]
      .text            inited   0x8000'9db4      0x48  fsl_spdif.o [6]
      .text            inited   0x8000'9dfc      0x38  fsl_usdhc.o [6]
      .text            inited   0x8000'9e34     0x4d0  isr.o [7]
      .text            inited   0x8000'a304     0x6a4  main.o [7]
      .text            inited   0x8000'a9a8     0x660  menu.o [1]
      .text            inited   0x8000'b008     0x6fc  siyuanshu.o [1]
      .text            inited   0x8000'b704    0x1d24  sxt.o [1]
      .text            inited   0x8000'd428     0x540  uart.o [1]
      .text            inited   0x8000'd968     0x176  usb_device_dci.o [4]
      .text            inited   0x8000'dae0     0x5d0  usb_device_ehci.o [4]
      .text            inited   0x8000'e0b0     0xea8  xiangzi.o [1]
      .text            inited   0x8000'ef58     0xfec  yuansu.o [1]
      .text            inited   0x8000'ff44      0x20  zf_common_clock.o [8]
      .text            inited   0x8000'ff64     0x54c  zf_common_debug.o [8]
      .text            inited   0x8001'04b0     0x624  zf_common_fifo.o [8]
      .text            inited   0x8001'0ad4     0x230  zf_common_function.o [8]
      .text            inited   0x8001'0d04      0xec  zf_common_interrupt.o [8]
      .text            inited   0x8001'0df0      0x1c  zf_device_camera.o [10]
      .text            inited   0x8001'0e0c     0x2bc  zf_device_imu660ra.o [10]
      .text            inited   0x8001'10c8     0x3b8  zf_device_mt9v03x.o [10]
      .text            inited   0x8001'1480     0xf5c  zf_device_tft180.o [10]
      .text            inited   0x8001'23dc      0x2c  zf_device_type.o [10]
      .text            inited   0x8001'2408     0x478  zf_driver_csi.o [11]
      .text            inited   0x8001'2880      0xc8  zf_driver_delay.o [11]
      .text            inited   0x8001'2948     0x5be  zf_driver_encoder.o [11]
      .text            inited   0x8001'2f08     0x2e4  zf_driver_gpio.o [11]
      .text            inited   0x8001'31ec     0x190  zf_driver_pit.o [11]
      .text            inited   0x8001'337c     0x90c  zf_driver_pwm.o [11]
      .text            inited   0x8001'3c88     0x658  zf_driver_soft_iic.o [11]
      .text            inited   0x8001'42e0     0xafc  zf_driver_spi.o [11]
      .text            inited   0x8001'4ddc     0x5cc  zf_driver_uart.o [11]
      .text            inited   0x8001'53a8      0x20  zf_driver_usb_cdc.o [11]
      .text            inited   0x8001'53c8     0x284  zhuangtaiji.o [1]
      .text            inited   0x8001'564c     0x6ac  zf_device_config.o [16]
      .text            inited   0x8001'5cf8      0x1e  sqrt.o [13]
      .text            inited   0x8001'5d18      0xa6  ABImemcpy.o [14]
      .text            inited   0x8001'5dc0      0x66  ABImemset.o [14]
      .text            inited   0x8001'5e28     0x274  I64DivMod.o [14]
      .text            inited   0x8001'609c      0x66  DblToS64.o [13]
      .text            inited   0x8001'6104      0x54  S64ToDbl.o [13]
      .text            inited   0x8001'6158      0x9e  modf.o [13]
      .text            inited   0x8001'61f8     0x510  pow64.o [13]
      .text            inited   0x8001'6708      0x36  strlen.o [14]
      .text            inited   0x8001'6740      0x12  strcmp.o [14]
      .text            inited   0x8001'6754      0x7e  atan2.o [13]
      .text            inited   0x8001'67d4      0x40  sprintf.o [12]
      .text            inited   0x8001'6814     0x13c  heap0.o [12]
      .text            inited   0x8001'6950       0x2  I64DivZer.o [14]
      .text            inited   0x8001'6954     0x2c0  iar_Exp64.o [13]
      .text            inited   0x8001'6c14      0x7c  frexp.o [13]
      .text            inited   0x8001'6c90      0xf0  ldexp.o [13]
      .text            inited   0x8001'6d80      0x2c  xgetmemchunk.o [12]
      Veneer           inited   0x8001'6dac       0x8  - Linker created -
      .text            inited   0x8001'6db4       0xa  cexit.o [14]
      .rodata          inited   0x8001'6dbe       0x2  fsl_csi.o [6]
      .rodata          inited   0x8001'6dc0       0x2  fsl_csi.o [6]
      .rodata          inited   0x8001'6dc2       0x2  fsl_pit.o [6]
      .rodata          inited   0x8001'6dc4       0x2  menu.o [1]
      .text            inited   0x8001'6dc6     0x168  init.o [1]
      .text            inited   0x8001'6f2e      0x10  zf_common_vector.o [8]
      .text            inited   0x8001'6f3e       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'6f40       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'6f48       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'6f50       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'6f58       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'6f60       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'6f68       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'6f70       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'6f78       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'6f80       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'6f88       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'6f90       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'6f98       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'6fa0       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'6fa8       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'6fb0       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'6fb8       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'6fc0       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'6fc8       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'6fca       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'6fcc       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'6fce       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'6fd6       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'6fde       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'6fe6       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'6fee       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'6ff6       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'6ffe       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'7006       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'700e       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'7016       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'701e       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'7026       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7028       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'702a       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'702c       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'702e       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7030       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7032       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7034       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7036       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7038       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'703a       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'703c       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'703e       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7040       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7042       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7044       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7046       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7048       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'7050       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'7058       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'7060       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'7068       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'7070       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7072       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7074       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7076       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7078       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'707a       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'707c       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'707e       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7080       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7082       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7084       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7086       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7088       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'708a       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'708c       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'708e       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7090       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7092       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7094       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7096       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7098       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'709a       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'709c       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'709e       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'70a0       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'70a8       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'70b0       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'70b2       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'70b4       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'70b6       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'70b8       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'70ba       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'70bc       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'70be       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'70c0       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'70c2       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'70c4       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'70c6       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'70c8       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'70ca       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'70cc       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'70ce       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'70d6       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'70de       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'70e0       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'70e8       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'70f0       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'70f8       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'7100       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7102       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7104       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7106       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7108       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'710a       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'710c       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'710e       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7110       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7112       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7114       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7116       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7118       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'711a       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'711c       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'711e       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7120       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7122       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7124       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7126       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7128       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'712a       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'712c       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'712e       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7130       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7132       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7134       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7136       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7138       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'713a       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'713c       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'713e       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7140       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7142       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7144       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7146       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'714e       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'7156       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'715e       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7160       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'7168       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'716a       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'716c       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'716e       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'7170       0x4  heaptramp0.o [12]
      .text            inited   0x8001'7174       0x8  abs.o [12]
      Veneer           inited   0x8001'717c       0x8  - Linker created -
      .text            inited   0x8001'7184      0x16  vla_alloc.o [12]
      .text            inited   0x8001'719a       0xa  xsprout.o [12]
      .text            inited   0x8001'71a4       0x4  exit.o [12]
      .text            inited   0x8001'71a8     0x13c  xprintftiny.o [12]
                              - 0x8001'72e4  0x1'6ee4

Unused ranges:

         From           To        Size
         ----           --        ----
  0x2000'0caa  0x2000'0cbf        0x16
  0x2002'38c0  0x2006'efff    0x4'b740
  0x7001'2026  0x703f'ffff   0x3e'dfda
  0x8000'0000  0x8000'03ff       0x400
  0x8001'72e4  0x81df'ffff  0x1de'8d1c


*******************************************************************************
*** INIT TABLE
***

          Address      Size
          -------      ----
Zero (__iar_zero_init3)
    2 destination ranges, total size 0x2'27d4:
          0x2000'0cc0  0x1'91d4
          0x2001'9ec0    0x9600

Copy/lz77 (__iar_lz77_init3)
    1 source range, total size 0xf40f (66% of destination):
          0x7000'290c    0xf40f
    1 destination range, total size 0x1'6ee4:
          0x8000'0400  0x1'6ee4

Copy/lz77 (__iar_lz77_init3)
    1 source range, total size 0x30b (24% of destination):
          0x7001'1d1b     0x30b
    1 destination range, total size 0xcaa:
          0x2000'0000     0xcaa



*******************************************************************************
*** MODULE SUMMARY
***

    Module                              ro code  rw code  ro data  rw data
    ------                              -------  -------  -------  -------
command line/config:
    ----------------------------------------------------------------------
    Total:

D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\code_17896468042025673432.dir: [1]
    ce.o                                           1'212      863      240
    hanzi.o                                                   872    1'312
    init.o                                           360      240
    menu.o                                         1'632    1'859    2'850
    siyuanshu.o                                    1'788    1'193       84
    sxt.o                                          7'460    5'018  101'416
    uart.o                                         1'344      894       66
    xiangzi.o                                      3'752    2'501      459
    yuansu.o                                       4'076    2'711       72
    zhuangtaiji.o                                    644      475      216
    ----------------------------------------------------------------------
    Total:                                        22'268   16'626  106'715

D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\components_fatfs_6246014242642884545.dir: [2]
    ----------------------------------------------------------------------
    Total:

D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\components_sdmmc_11335736203712880765.dir: [3]
    ----------------------------------------------------------------------
    Total:

D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\components_usb_17375887477349322284.dir: [4]
    usb_device_dci.o                                 374      249
    usb_device_ehci.o                              1'488      991
    ----------------------------------------------------------------------
    Total:                                         1'862    1'240

D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_device_3373403627085545761.dir: [5]
    board.o                                          684      455
    clock_config.o                                 1'784    1'248       92
    evkmimxrt1064_flexspi_nor_config.o                        512
    evkmimxrt1064_sdram_ini_dcd.o                           1'040
    fsl_assert.o                                      28       47       44
    fsl_debug_console.o                            2'860    1'902       20
    fsl_flexspi_nor_boot.o                                     48
    startup_MIMXRT1064.o                    108
    system_MIMXRT1064.o                     294                 1        4
    ----------------------------------------------------------------------
    Total:                                  402    5'356    5'253      160

D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir: [6]
    fsl_clock.o                                    2'080    1'435       84
    fsl_csi.o                                      1'640    1'220      204
    fsl_edma.o                                     1'232      876      212
    fsl_enet.o                                       336      298      160
    fsl_flexcan.o                                    164      190      140
    fsl_flexio.o                                     104       69       24
    fsl_gpio.o                                       534      509      232
    fsl_gpt.o                                        588      581      284
    fsl_lpi2c.o                                      148       99       48
    fsl_lpspi.o                                    3'128    2'339      424
    fsl_lpuart.o                                   1'372    1'151      400
    fsl_pit.o                                        356      359      182
    fsl_pwm.o                                      2'856    2'105      308
    fsl_qtmr.o                                       484      460      208
    fsl_sai.o                                        328      280      132
    fsl_spdif.o                                       72       48       16
    fsl_usdhc.o                                       56       45       28
    ----------------------------------------------------------------------
    Total:                                        15'478   12'064    3'086

D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\user_c_11930989646151335152.dir: [7]
    isr.o                                          1'232      820       14
    main.o                                         1'700    1'133       17
    ----------------------------------------------------------------------
    Total:                                         2'932    1'953       31

D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_common_1201165462030747823.dir: [8]
    zf_common_clock.o                                 32       23        4
    zf_common_debug.o                              1'356      982      232
    zf_common_fifo.o                               1'572    1'077       48
    zf_common_font.o                                        1'378    2'072
    zf_common_function.o                             560      408       52
    zf_common_interrupt.o                            236      157        4
    zf_common_vector.o                               578    1'408
    ----------------------------------------------------------------------
    Total:                                         4'334    5'433    2'412

D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_components_14936052685886095442.dir: [9]
    ----------------------------------------------------------------------
    Total:

D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir: [10]
    zf_device_camera.o                                28       18       32
    zf_device_imu660ra.o                             700      537      124
    zf_device_mt9v03x.o                              952      725   38'592
    zf_device_tft180.o                             3'932    2'653       60
    zf_device_type.o                                  44       34       21
    ----------------------------------------------------------------------
    Total:                                         5'656    3'967   38'829

D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir: [11]
    zf_driver_csi.o                                1'144      838      172
    zf_driver_delay.o                                200      189       84
    zf_driver_encoder.o                            1'470    1'017       72
    zf_driver_gpio.o                                 740      542      100
    zf_driver_pit.o                                  400      311       69
    zf_driver_pwm.o                                2'316    1'576       68
    zf_driver_soft_iic.o                           1'624    1'115       52
    zf_driver_spi.o                                2'812    1'952      148
    zf_driver_uart.o                               1'484    1'028       84
    zf_driver_usb_cdc.o                               32       22       16
    ----------------------------------------------------------------------
    Total:                                        12'222    8'590      865

dl7M_tlf.a: [12]
    abort.o                                   6
    abs.o                                              8        5
    exit.o                                             4        2
    heap0.o                                          316      210        8
    heaptramp0.o                                       4        3
    low_level_init.o                          4
    sprintf.o                                         64       44
    vla_alloc.o                                       22       15
    xgetmemchunk.o                                    44       29        4
    xprintftiny.o                                    316      211
    xsprout.o                                         10        7
    ----------------------------------------------------------------------
    Total:                                   10      788      526       12

m7M_tlv.a: [13]
    DblToS64.o                                       102       68
    S64ToDbl.o                                        84       57
    acos.o                                           128       85
    asin.o                                           136       90
    atan2.o                                          126       85
    fpinit_M.o                               34
    frexp.o                                          124       83
    iar_Exp64.o                                      704      469
    ldexp.o                                          240      160
    modf.o                                           158      105
    pow64.o                                        1'296    1'567    1'056
    sqrt.o                                            30       20
    xatan.o                                          280      187
    ----------------------------------------------------------------------
    Total:                                   34    3'408    2'976    1'056

rt7M_tl.a: [14]
    ABImemcpy.o                                      166      112
    ABImemset.o                                      102       69
    I64DivMod.o                                      628      419
    I64DivZer.o                                        2        2
    cexit.o                                           10        7
    cmain.o                                  30
    cstartup_M.o                             28
    data_init.o                              40
    lz77_init.o                             504
    strcmp.o                                          18       13
    strlen.o                                          54       36
    vector_table_M.o                         64
    zero_init3.o                             56
    ----------------------------------------------------------------------
    Total:                                  722      980      658

shb_l.a: [15]
    exit.o                                   20
    ----------------------------------------------------------------------
    Total:                                   20

zf_device_config.a: [16]
    zf_device_config.o                             1'708    6'586    8'236
    ----------------------------------------------------------------------
    Total:                                         1'708    6'586    8'236

    Gaps                                      6       24        2
    Linker created                           16       16       90    5'120
--------------------------------------------------------------------------
    Grand Total:                          1'210   77'032   65'964  166'522


*******************************************************************************
*** ENTRY LIST
***

Entry                       Address      Size  Type      Object
-----                       -------      ----  ----      ------
.iar.init_table$$Base   0x7000'286c             --   Gb  - Linker created -
.iar.init_table$$Limit  0x7000'28bc             --   Gb  - Linker created -
?main                   0x7000'28cd            Code  Gb  cmain.o [14]
ACMP1_IRQHandler        0x8001'710d       0x2  Code  Wk  zf_common_vector.o [8]
ACMP2_IRQHandler        0x8001'710f       0x2  Code  Wk  zf_common_vector.o [8]
ACMP3_IRQHandler        0x8001'7111       0x2  Code  Wk  zf_common_vector.o [8]
ACMP4_IRQHandler        0x8001'7113       0x2  Code  Wk  zf_common_vector.o [8]
ADC1_IRQHandler         0x8001'707d       0x2  Code  Wk  zf_common_vector.o [8]
ADC2_IRQHandler         0x8001'707f       0x2  Code  Wk  zf_common_vector.o [8]
ADC_ETC_ERROR_IRQ_IRQHandler
                        0x8001'710b       0x2  Code  Wk  zf_common_vector.o [8]
ADC_ETC_IRQ0_IRQHandler
                        0x8001'7105       0x2  Code  Wk  zf_common_vector.o [8]
ADC_ETC_IRQ1_IRQHandler
                        0x8001'7107       0x2  Code  Wk  zf_common_vector.o [8]
ADC_ETC_IRQ2_IRQHandler
                        0x8001'7109       0x2  Code  Wk  zf_common_vector.o [8]
ARM_MPU_Disable         0x8000'4831      0x1a  Code  Lc  board.o [5]
ARM_MPU_Enable          0x8000'4815      0x1c  Code  Lc  board.o [5]
Aldata                  0x2001'9e44       0x8  Data  Lc  heap0.o [12]
Angle_ax                0x2000'0f48       0x4  Data  Gb  siyuanshu.o [1]
Angle_ay                0x2000'0f4c       0x4  Data  Gb  siyuanshu.o [1]
Angle_az                0x2000'0f50       0x4  Data  Gb  siyuanshu.o [1]
Angle_gx                0x2000'0f3c       0x4  Data  Gb  siyuanshu.o [1]
Angle_gy                0x2000'0f40       0x4  Data  Gb  siyuanshu.o [1]
Angle_gz                0x2000'0f44       0x4  Data  Gb  siyuanshu.o [1]
BEE_IRQHandler          0x8001'7047       0x2  Code  Wk  zf_common_vector.o [8]
BOARD_BootClockRUN      0x8000'510d     0x43a  Code  Gb  clock_config.o [5]
BOARD_ConfigMPU         0x8000'48eb     0x154  Code  Gb  board.o [5]
BOARD_DebugConsoleSrcFreq
                        0x8000'48b1      0x3a  Code  Gb  board.o [5]
BOARD_InitBootClocks    0x8000'5105       0x8  Code  Gb  clock_config.o [5]
Both_Lost_Time          0x2001'906c       0x4  Data  Gb  sxt.o [1]
Boundary_search_end     0x2001'9e7a       0x1  Data  Gb  sxt.o [1]
Boundry_Start_Left      0x2001'905c       0x4  Data  Gb  sxt.o [1]
Boundry_Start_Right     0x2001'9060       0x4  Data  Gb  sxt.o [1]
BusFault_Handler        0x8001'6f35       0x2  Code  Gb  zf_common_vector.o [8]
CAN1_DriverIRQHandler   0x8000'7665      0x2a  Code  Gb  fsl_flexcan.o [6]
CAN1_IRQHandler         0x8001'7017       0x8  Code  Wk  zf_common_vector.o [8]
CAN2_DriverIRQHandler   0x8000'768f      0x2a  Code  Gb  fsl_flexcan.o [6]
CAN2_IRQHandler         0x8001'701f       0x8  Code  Wk  zf_common_vector.o [8]
CAN3_DriverIRQHandler   0x8000'76b9      0x2a  Code  Gb  fsl_flexcan.o [6]
CAN3_IRQHandler         0x8001'7157       0x8  Code  Wk  zf_common_vector.o [8]
CCM_1_IRQHandler        0x8001'70b7       0x2  Code  Wk  zf_common_vector.o [8]
CCM_2_IRQHandler        0x8001'70b9       0x2  Code  Wk  zf_common_vector.o [8]
CLOCK_ControlGate       0x8000'5069      0x50  Code  Lc  clock_config.o [5]
CLOCK_ControlGate       0x8000'5ee9      0x50  Code  Lc  fsl_csi.o [6]
CLOCK_ControlGate       0x8000'7789      0x4a  Code  Lc  fsl_gpio.o [6]
CLOCK_ControlGate       0x8000'79a1      0x4a  Code  Lc  fsl_gpt.o [6]
CLOCK_ControlGate       0x8000'7c81      0x50  Code  Lc  fsl_lpspi.o [6]
CLOCK_ControlGate       0x8000'88b9      0x50  Code  Lc  fsl_lpuart.o [6]
CLOCK_ControlGate       0x8000'8e15      0x4a  Code  Lc  fsl_pit.o [6]
CLOCK_ControlGate       0x8000'8f79      0x50  Code  Lc  fsl_pwm.o [6]
CLOCK_ControlGate       0x8000'9aa1      0x4a  Code  Lc  fsl_qtmr.o [6]
CLOCK_DeinitAudioPll    0x8000'5a53       0xc  Code  Gb  fsl_clock.o [6]
CLOCK_DeinitEnetPll     0x8000'5a6b       0xc  Code  Gb  fsl_clock.o [6]
CLOCK_DeinitUsb2Pll     0x8000'5a49       0xa  Code  Gb  fsl_clock.o [6]
CLOCK_DeinitVideoPll    0x8000'5a5f       0xc  Code  Gb  fsl_clock.o [6]
CLOCK_DisableClock      0x8000'50b9      0x10  Code  Lc  clock_config.o [5]
CLOCK_DisableClock      0x8000'79fb      0x10  Code  Lc  fsl_gpt.o [6]
CLOCK_DisableClock      0x8000'8919      0x10  Code  Lc  fsl_lpuart.o [6]
CLOCK_DisableClock      0x8000'8e6f      0x10  Code  Lc  fsl_pit.o [6]
CLOCK_DisableClock      0x8000'8fd9      0x10  Code  Lc  fsl_pwm.o [6]
CLOCK_DisableClock      0x8000'9afb      0x10  Code  Lc  fsl_qtmr.o [6]
CLOCK_EnableClock       0x8000'5f39      0x10  Code  Lc  fsl_csi.o [6]
CLOCK_EnableClock       0x8000'77d3      0x10  Code  Lc  fsl_gpio.o [6]
CLOCK_EnableClock       0x8000'79eb      0x10  Code  Lc  fsl_gpt.o [6]
CLOCK_EnableClock       0x8000'7cd1      0x10  Code  Lc  fsl_lpspi.o [6]
CLOCK_EnableClock       0x8000'8909      0x10  Code  Lc  fsl_lpuart.o [6]
CLOCK_EnableClock       0x8000'8e5f      0x10  Code  Lc  fsl_pit.o [6]
CLOCK_EnableClock       0x8000'8fc9      0x10  Code  Lc  fsl_pwm.o [6]
CLOCK_EnableClock       0x8000'9aeb      0x10  Code  Lc  fsl_qtmr.o [6]
CLOCK_GetAhbFreq        0x8000'583f      0x18  Code  Gb  fsl_clock.o [6]
CLOCK_GetDiv            0x8000'4875      0x2a  Code  Lc  board.o [5]
CLOCK_GetFreq           0x8000'58c7     0x142  Code  Gb  fsl_clock.o [6]
CLOCK_GetIpgFreq        0x8000'588b      0x18  Code  Gb  fsl_clock.o [6]
CLOCK_GetMux            0x8000'484b      0x2a  Code  Lc  board.o [5]
CLOCK_GetOscFreq        0x8000'489f      0x12  Code  Lc  board.o [5]
CLOCK_GetOscFreq        0x8000'5691      0x18  Code  Lc  fsl_clock.o [6]
CLOCK_GetPerClkFreq     0x8000'58a3      0x24  Code  Gb  fsl_clock.o [6]
CLOCK_GetPeriphClkFreq  0x8000'56f7      0xaa  Code  Lc  fsl_clock.o [6]
CLOCK_GetPllBypassRefClk
                        0x8000'56d5      0x22  Code  Lc  fsl_clock.o [6]
CLOCK_GetPllFreq        0x8000'5a91     0x2b0  Code  Gb  fsl_clock.o [6]
CLOCK_GetPllFreq::enetRefClkFreq
                        0x8000'0b64      0x10  Data  Lc  fsl_clock.o [6]
CLOCK_GetPllUsb1SWFreq  0x8000'57a1      0x2a  Code  Lc  fsl_clock.o [6]
CLOCK_GetRtcFreq        0x8000'56a9       0x6  Code  Lc  fsl_clock.o [6]
CLOCK_GetSemcFreq       0x8000'5857      0x34  Code  Gb  fsl_clock.o [6]
CLOCK_GetSysPfdFreq     0x8000'5d5d      0x6a  Code  Gb  fsl_clock.o [6]
CLOCK_GetUsb1PfdFreq    0x8000'5dd1      0x64  Code  Gb  fsl_clock.o [6]
CLOCK_InitArmPll        0x8000'5a09      0x40  Code  Gb  fsl_clock.o [6]
CLOCK_InitExternalClk   0x8000'57cb      0x4a  Code  Gb  fsl_clock.o [6]
CLOCK_InitRcOsc24M      0x8000'5831       0xe  Code  Gb  fsl_clock.o [6]
CLOCK_IsPllBypassed     0x8000'56af      0x10  Code  Lc  fsl_clock.o [6]
CLOCK_IsPllEnabled      0x8000'56bf      0x16  Code  Lc  fsl_clock.o [6]
CLOCK_SetDiv            0x8000'4fff      0x6a  Code  Lc  clock_config.o [5]
CLOCK_SetDiv            0x8001'2473      0x64  Code  Lc  zf_driver_csi.o [11]
CLOCK_SetDiv            0x8001'434b      0x6a  Code  Lc  zf_driver_spi.o [11]
CLOCK_SetMux            0x8000'4f95      0x6a  Code  Lc  clock_config.o [5]
CLOCK_SetMux            0x8001'2409      0x6a  Code  Lc  zf_driver_csi.o [11]
CLOCK_SetMux            0x8001'42e1      0x6a  Code  Lc  zf_driver_spi.o [11]
CLOCK_SetPllBypass      0x8000'50d9      0x2c  Code  Lc  clock_config.o [5]
CLOCK_SetRtcXtalFreq    0x8000'50d1       0x8  Code  Lc  clock_config.o [5]
CLOCK_SetXtalFreq       0x8000'50c9       0x8  Code  Lc  clock_config.o [5]
CLOCK_SwitchOsc         0x8000'5815      0x1c  Code  Gb  fsl_clock.o [6]
CORE_IRQHandler         0x8001'6fcd       0x2  Code  Wk  zf_common_vector.o [8]
CSI_ClearFifo           0x8000'6209      0x30  Code  Gb  fsl_csi.o [6]
CSI_DriverIRQHandler    0x8000'64c7      0x14  Code  Gb  fsl_csi.o [6]
CSI_EnableFifoDmaRequest
                        0x8000'6259      0x2e  Code  Gb  fsl_csi.o [6]
CSI_EnableInterrupts    0x8000'6287      0x24  Code  Gb  fsl_csi.o [6]
CSI_GetDefaultConfig    0x8000'61a5      0x54  Code  Gb  fsl_csi.o [6]
CSI_GetInstance         0x8000'5f9d      0x34  Code  Lc  fsl_csi.o [6]
CSI_GetRxBufferAddr     0x8000'6031      0x12  Code  Lc  fsl_csi.o [6]
CSI_IRQHandler          0x8000'9e77       0xc  Code  Gb  isr.o [7]
CSI_Init                0x8000'6043     0x114  Code  Gb  fsl_csi.o [6]
CSI_ReflashFifoDma      0x8000'6239      0x20  Code  Gb  fsl_csi.o [6]
CSI_Reset               0x8000'6157      0x4e  Code  Gb  fsl_csi.o [6]
CSI_SetRxBufferAddr     0x8000'61f9      0x10  Code  Gb  fsl_csi.o [6]
CSI_Start               0x8000'5f6d      0x18  Code  Lc  fsl_csi.o [6]
CSI_Stop                0x8000'5f85      0x18  Code  Lc  fsl_csi.o [6]
CSI_TransferCreateHandle
                        0x8000'62ab      0x5e  Code  Gb  fsl_csi.o [6]
CSI_TransferGetEmptyBuffer
                        0x8000'6005      0x16  Code  Lc  fsl_csi.o [6]
CSI_TransferGetEmptyBufferCount
                        0x8000'5fff       0x6  Code  Lc  fsl_csi.o [6]
CSI_TransferGetFullBuffer
                        0x8000'6397      0x4c  Code  Gb  fsl_csi.o [6]
CSI_TransferGetQueueDelta
                        0x8000'5fd1      0x1a  Code  Lc  fsl_csi.o [6]
CSI_TransferHandleIRQ   0x8000'63e5      0xe2  Code  Gb  fsl_csi.o [6]
CSI_TransferIncreaseQueueIdx
                        0x8000'5feb      0x14  Code  Lc  fsl_csi.o [6]
CSI_TransferPutEmptyBuffer
                        0x8000'601b      0x16  Code  Lc  fsl_csi.o [6]
CSI_TransferStart       0x8000'6309      0x70  Code  Gb  fsl_csi.o [6]
CSI_TransferSubmitEmptyBuffer
                        0x8000'6379      0x1e  Code  Gb  fsl_csi.o [6]
CSTACK$$Base            0x2006'f000             --   Gb  - Linker created -
CSTACK$$Limit           0x2007'0000             --   Gb  - Linker created -
CSU_IRQHandler          0x8001'703b       0x2  Code  Wk  zf_common_vector.o [8]
CTI0_ERROR_IRQHandler   0x8001'6fc9       0x2  Code  Wk  zf_common_vector.o [8]
CTI1_ERROR_IRQHandler   0x8001'6fcb       0x2  Code  Wk  zf_common_vector.o [8]
Continuity_Change_Left  0x8000'f857      0x70  Code  Gb  yuansu.o [1]
Continuity_Change_Right
                        0x8000'f8c7      0x62  Code  Gb  yuansu.o [1]
Cross_Detect            0x8000'f6f9     0x15e  Code  Gb  yuansu.o [1]
Cross_Flag              0x2001'9440       0x4  Data  Gb  sxt.o [1]
DCDC_IRQHandler         0x8001'7081       0x2  Code  Wk  zf_common_vector.o [8]
DCP_IRQHandler          0x8001'703d       0x2  Code  Wk  zf_common_vector.o [8]
DCP_VMI_IRQHandler      0x8001'703f       0x2  Code  Wk  zf_common_vector.o [8]
DMA0_DMA16_DriverIRQHandler
                        0x8000'71db      0x34  Code  Gb  fsl_edma.o [6]
DMA0_DMA16_IRQHandler   0x8001'6f41       0x8  Code  Wk  zf_common_vector.o [8]
DMA10_DMA26_DriverIRQHandler
                        0x8000'73d1      0x32  Code  Gb  fsl_edma.o [6]
DMA10_DMA26_IRQHandler  0x8001'6f91       0x8  Code  Wk  zf_common_vector.o [8]
DMA11_DMA27_DriverIRQHandler
                        0x8000'7403      0x32  Code  Gb  fsl_edma.o [6]
DMA11_DMA27_IRQHandler  0x8001'6f99       0x8  Code  Wk  zf_common_vector.o [8]
DMA12_DMA28_DriverIRQHandler
                        0x8000'7435      0x32  Code  Gb  fsl_edma.o [6]
DMA12_DMA28_IRQHandler  0x8001'6fa1       0x8  Code  Wk  zf_common_vector.o [8]
DMA13_DMA29_DriverIRQHandler
                        0x8000'7467      0x32  Code  Gb  fsl_edma.o [6]
DMA13_DMA29_IRQHandler  0x8001'6fa9       0x8  Code  Wk  zf_common_vector.o [8]
DMA14_DMA30_DriverIRQHandler
                        0x8000'7499      0x32  Code  Gb  fsl_edma.o [6]
DMA14_DMA30_IRQHandler  0x8001'6fb1       0x8  Code  Wk  zf_common_vector.o [8]
DMA15_DMA31_DriverIRQHandler
                        0x8000'74d1      0x32  Code  Gb  fsl_edma.o [6]
DMA15_DMA31_IRQHandler  0x8001'6fb9       0x8  Code  Wk  zf_common_vector.o [8]
DMA1_DMA17_DriverIRQHandler
                        0x8000'720f      0x32  Code  Gb  fsl_edma.o [6]
DMA1_DMA17_IRQHandler   0x8001'6f49       0x8  Code  Wk  zf_common_vector.o [8]
DMA2_DMA18_DriverIRQHandler
                        0x8000'7241      0x32  Code  Gb  fsl_edma.o [6]
DMA2_DMA18_IRQHandler   0x8001'6f51       0x8  Code  Wk  zf_common_vector.o [8]
DMA3_DMA19_DriverIRQHandler
                        0x8000'7273      0x32  Code  Gb  fsl_edma.o [6]
DMA3_DMA19_IRQHandler   0x8001'6f59       0x8  Code  Wk  zf_common_vector.o [8]
DMA4_DMA20_DriverIRQHandler
                        0x8000'72a5      0x32  Code  Gb  fsl_edma.o [6]
DMA4_DMA20_IRQHandler   0x8001'6f61       0x8  Code  Wk  zf_common_vector.o [8]
DMA5_DMA21_DriverIRQHandler
                        0x8000'72d7      0x32  Code  Gb  fsl_edma.o [6]
DMA5_DMA21_IRQHandler   0x8001'6f69       0x8  Code  Wk  zf_common_vector.o [8]
DMA6_DMA22_DriverIRQHandler
                        0x8000'7309      0x32  Code  Gb  fsl_edma.o [6]
DMA6_DMA22_IRQHandler   0x8001'6f71       0x8  Code  Wk  zf_common_vector.o [8]
DMA7_DMA23_DriverIRQHandler
                        0x8000'733b      0x32  Code  Gb  fsl_edma.o [6]
DMA7_DMA23_IRQHandler   0x8001'6f79       0x8  Code  Wk  zf_common_vector.o [8]
DMA8_DMA24_DriverIRQHandler
                        0x8000'736d      0x32  Code  Gb  fsl_edma.o [6]
DMA8_DMA24_IRQHandler   0x8001'6f81       0x8  Code  Wk  zf_common_vector.o [8]
DMA9_DMA25_DriverIRQHandler
                        0x8000'739f      0x32  Code  Gb  fsl_edma.o [6]
DMA9_DMA25_IRQHandler   0x8001'6f89       0x8  Code  Wk  zf_common_vector.o [8]
DMA_ERROR_DriverIRQHandler
                        0x8001'716b       0x2  Code  Wk  zf_common_vector.o [8]
DMA_ERROR_IRQHandler    0x8001'6fc1       0x8  Code  Wk  zf_common_vector.o [8]
DTCM_VAR$$Base          0x2001'9ec0             --   Gb  - Linker created -
DTCM_VAR$$Limit         0x2002'34c0             --   Gb  - Linker created -
DbgConsole_ConvertFloatRadixNumToString
                        0x8000'6731     0x1a8  Code  Lc  fsl_debug_console.o [5]
DbgConsole_ConvertRadixNumToString
                        0x8000'65a5     0x18a  Code  Lc  fsl_debug_console.o [5]
DbgConsole_Printf       0x8000'6519      0x18  Code  Gb  fsl_debug_console.o [5]
DbgConsole_PrintfFormattedData
                        0x8000'68e5     0x760  Code  Lc  fsl_debug_console.o [5]
DbgConsole_PrintfPaddingCharacter
                        0x8000'6579      0x2c  Code  Lc  fsl_debug_console.o [5]
DbgConsole_Putchar      0x8000'6559      0x20  Code  Gb  fsl_debug_console.o [5]
DbgConsole_Vprintf      0x8000'6531      0x28  Code  Gb  fsl_debug_console.o [5]
DebugMon_Handler        0x8001'6f3b       0x2  Code  Gb  zf_common_vector.o [8]
DefaultISR              0x7000'27e5            Code  Wk  startup_MIMXRT1064.o [5]
DisableGlobalIRQ        0x8001'0d91       0x8  Code  Lc  zf_common_interrupt.o [8]
EDMA_GetChannelStatusFlags
                        0x8000'7045      0x46  Code  Gb  fsl_edma.o [6]
EDMA_HandleIRQ          0x8000'7095     0x146  Code  Gb  fsl_edma.o [6]
ENC1_IRQHandler         0x8001'7119       0x2  Code  Wk  zf_common_vector.o [8]
ENC2_IRQHandler         0x8001'711b       0x2  Code  Wk  zf_common_vector.o [8]
ENC3_IRQHandler         0x8001'711d       0x2  Code  Wk  zf_common_vector.o [8]
ENC4_IRQHandler         0x8001'711f       0x2  Code  Wk  zf_common_vector.o [8]
ENET2_1588_Timer_DriverIRQHandler
                        0x8000'7631       0xe  Code  Gb  fsl_enet.o [6]
ENET2_1588_Timer_IRQHandler
                        0x8001'714f       0x8  Code  Wk  zf_common_vector.o [8]
ENET2_DriverIRQHandler  0x8000'7623       0xe  Code  Gb  fsl_enet.o [6]
ENET2_IRQHandler        0x8001'7147       0x8  Code  Wk  zf_common_vector.o [8]
ENET_1588_Timer_DriverIRQHandler
                        0x8000'7615       0xe  Code  Gb  fsl_enet.o [6]
ENET_1588_Timer_IRQHandler
                        0x8001'70f9       0x8  Code  Wk  zf_common_vector.o [8]
ENET_CommonFrame0IRQHandler
                        0x8000'7555      0x8c  Code  Gb  fsl_enet.o [6]
ENET_DriverIRQHandler   0x8000'7607       0xe  Code  Gb  fsl_enet.o [6]
ENET_GetInstance        0x8000'7515      0x34  Code  Gb  fsl_enet.o [6]
ENET_IRQHandler         0x8001'70f1       0x8  Code  Wk  zf_common_vector.o [8]
ENET_Ptp1588IRQHandler  0x8000'75e1      0x26  Code  Gb  fsl_enet.o [6]
EWM_IRQHandler          0x8001'70b5       0x2  Code  Wk  zf_common_vector.o [8]
EnableIRQ               0x8000'5f49      0x24  Code  Lc  fsl_csi.o [6]
EnableIRQ               0x8001'0d6d      0x24  Code  Lc  zf_common_interrupt.o [8]
EnableIRQ               0x8001'320b      0x24  Code  Lc  zf_driver_pit.o [11]
FLEXIO1_DriverIRQHandler
                        0x8000'7759       0x8  Code  Gb  fsl_flexio.o [6]
FLEXIO1_IRQHandler      0x8001'70a1       0x8  Code  Wk  zf_common_vector.o [8]
FLEXIO2_DriverIRQHandler
                        0x8000'7761       0x8  Code  Gb  fsl_flexio.o [6]
FLEXIO2_IRQHandler      0x8001'70a9       0x8  Code  Wk  zf_common_vector.o [8]
FLEXIO3_DriverIRQHandler
                        0x8000'7769       0x8  Code  Gb  fsl_flexio.o [6]
FLEXIO3_IRQHandler      0x8001'7161       0x8  Code  Wk  zf_common_vector.o [8]
FLEXIO_CommonIRQHandler
                        0x8000'7709      0x42  Code  Lc  fsl_flexio.o [6]
FLEXRAM_IRQHandler      0x8001'7027       0x2  Code  Wk  zf_common_vector.o [8]
FLEXSPI2_DriverIRQHandler
                        0x8001'716d       0x2  Code  Wk  zf_common_vector.o [8]
FLEXSPI2_IRQHandler     0x8001'70cf       0x8  Code  Wk  zf_common_vector.o [8]
FLEXSPI_DriverIRQHandler
                        0x8001'716f       0x2  Code  Wk  zf_common_vector.o [8]
FLEXSPI_IRQHandler      0x8001'70d7       0x8  Code  Wk  zf_common_vector.o [8]
Find_Down_Point         0x8000'c421     0x178  Code  Gb  sxt.o [1]
Find_Left_Down_Point    0x8000'f929      0xd8  Code  Gb  yuansu.o [1]
Find_Right_Down_Point   0x8000'fa29      0xda  Code  Gb  yuansu.o [1]
Find_Up_Point           0x8000'c5ad     0x19a  Code  Gb  sxt.o [1]
GPC_IRQHandler          0x8001'70bb       0x2  Code  Wk  zf_common_vector.o [8]
GPIO1_Combined_0_15_IRQHandler
                        0x8000'a1cd      0x18  Code  Gb  isr.o [7]
GPIO1_Combined_16_31_IRQHandler
                        0x8000'a1e5      0x24  Code  Gb  isr.o [7]
GPIO1_INT0_IRQHandler   0x8001'7087       0x2  Code  Wk  zf_common_vector.o [8]
GPIO1_INT1_IRQHandler   0x8001'7089       0x2  Code  Wk  zf_common_vector.o [8]
GPIO1_INT2_IRQHandler   0x8001'708b       0x2  Code  Wk  zf_common_vector.o [8]
GPIO1_INT3_IRQHandler   0x8001'708d       0x2  Code  Wk  zf_common_vector.o [8]
GPIO1_INT4_IRQHandler   0x8001'708f       0x2  Code  Wk  zf_common_vector.o [8]
GPIO1_INT5_IRQHandler   0x8001'7091       0x2  Code  Wk  zf_common_vector.o [8]
GPIO1_INT6_IRQHandler   0x8001'7093       0x2  Code  Wk  zf_common_vector.o [8]
GPIO1_INT7_IRQHandler   0x8001'7095       0x2  Code  Wk  zf_common_vector.o [8]
GPIO2_Combined_0_15_IRQHandler
                        0x8000'a209      0x1e  Code  Gb  isr.o [7]
GPIO2_Combined_16_31_IRQHandler
                        0x8000'a227      0x24  Code  Gb  isr.o [7]
GPIO3_Combined_0_15_IRQHandler
                        0x8000'a24b      0x1c  Code  Gb  isr.o [7]
GPIO3_Combined_16_31_IRQHandler
                        0x8001'7097       0x2  Code  Wk  zf_common_vector.o [8]
GPIO4_Combined_0_15_IRQHandler
                        0x8001'7099       0x2  Code  Wk  zf_common_vector.o [8]
GPIO4_Combined_16_31_IRQHandler
                        0x8001'709b       0x2  Code  Wk  zf_common_vector.o [8]
GPIO5_Combined_0_15_IRQHandler
                        0x8001'709d       0x2  Code  Wk  zf_common_vector.o [8]
GPIO5_Combined_16_31_IRQHandler
                        0x8001'709f       0x2  Code  Wk  zf_common_vector.o [8]
GPIO6_7_8_9_IRQHandler  0x8001'7169       0x2  Code  Wk  zf_common_vector.o [8]
GPIO_ClearPinsInterruptFlags
                        0x8000'9e49      0x10  Code  Lc  isr.o [7]
GPIO_ClearPinsOutput    0x8001'0e29      0x10  Code  Lc  zf_device_imu660ra.o [10]
GPIO_ClearPinsOutput    0x8001'149d      0x10  Code  Lc  zf_device_tft180.o [10]
GPIO_ClearPinsOutput    0x8001'2f51      0x10  Code  Lc  zf_driver_gpio.o [11]
GPIO_ClearPinsOutput    0x8001'3ca5      0x10  Code  Lc  zf_driver_soft_iic.o [11]
GPIO_GetInstance        0x8000'77f9      0x2e  Code  Lc  fsl_gpio.o [6]
GPIO_GetPinsInterruptFlags
                        0x8000'9e39       0xc  Code  Lc  isr.o [7]
GPIO_PinInit            0x8000'7827      0x78  Code  Gb  fsl_gpio.o [6]
GPIO_PinReadPadStatus   0x8001'2f61      0x26  Code  Lc  zf_driver_gpio.o [11]
GPIO_PinSetInterruptConfig
                        0x8000'78f5      0x92  Code  Gb  fsl_gpio.o [6]
GPIO_PinWrite           0x8000'789f      0x36  Code  Gb  fsl_gpio.o [6]
GPIO_PortClear          0x8001'0e23       0x6  Code  Lc  zf_device_imu660ra.o [10]
GPIO_PortClear          0x8001'1497       0x6  Code  Lc  zf_device_tft180.o [10]
GPIO_PortClear          0x8001'2f4b       0x6  Code  Lc  zf_driver_gpio.o [11]
GPIO_PortClear          0x8001'3c9f       0x6  Code  Lc  zf_driver_soft_iic.o [11]
GPIO_PortClearInterruptFlags
                        0x8000'9e45       0x4  Code  Lc  isr.o [7]
GPIO_PortGetInterruptFlags
                        0x8000'9e35       0x4  Code  Lc  isr.o [7]
GPIO_PortSet            0x8001'0e0d       0x6  Code  Lc  zf_device_imu660ra.o [10]
GPIO_PortSet            0x8001'1481       0x6  Code  Lc  zf_device_tft180.o [10]
GPIO_PortSet            0x8001'2f35       0x6  Code  Lc  zf_driver_gpio.o [11]
GPIO_PortSet            0x8001'3c89       0x6  Code  Lc  zf_driver_soft_iic.o [11]
GPIO_ReadPadStatus      0x8001'2f87      0x10  Code  Lc  zf_driver_gpio.o [11]
GPIO_SetPinInterruptConfig
                        0x8000'77e3      0x16  Code  Lc  fsl_gpio.o [6]
GPIO_SetPinsOutput      0x8001'0e13      0x10  Code  Lc  zf_device_imu660ra.o [10]
GPIO_SetPinsOutput      0x8001'1487      0x10  Code  Lc  zf_device_tft180.o [10]
GPIO_SetPinsOutput      0x8001'2f3b      0x10  Code  Lc  zf_driver_gpio.o [11]
GPIO_SetPinsOutput      0x8001'3c8f      0x10  Code  Lc  zf_driver_soft_iic.o [11]
GPR_IRQ_IRQHandler      0x8001'702d       0x2  Code  Wk  zf_common_vector.o [8]
GPT1_IRQHandler         0x8001'70c1       0x2  Code  Wk  zf_common_vector.o [8]
GPT2_IRQHandler         0x8001'70c3       0x2  Code  Wk  zf_common_vector.o [8]
GPT_ClearStatusFlags    0x8001'28cb       0x8  Code  Lc  zf_driver_delay.o [11]
GPT_Deinit              0x8000'7b45      0x1a  Code  Gb  fsl_gpt.o [6]
GPT_GetDefaultConfig    0x8000'7b5f      0x48  Code  Gb  fsl_gpt.o [6]
GPT_GetInstance         0x8000'7a7b      0x2e  Code  Lc  fsl_gpt.o [6]
GPT_GetStatusFlags      0x8001'28c1       0xa  Code  Lc  zf_driver_delay.o [11]
GPT_Init                0x8000'7aa9      0x9c  Code  Gb  fsl_gpt.o [6]
GPT_SetClockDivider     0x8000'7a53      0x28  Code  Lc  fsl_gpt.o [6]
GPT_SetClockSource      0x8000'7a1b      0x38  Code  Lc  fsl_gpt.o [6]
GPT_SetOutputCompareValue
                        0x8001'2895      0x2c  Code  Lc  zf_driver_delay.o [11]
GPT_SoftwareReset       0x8000'7a0b      0x10  Code  Lc  fsl_gpt.o [6]
GPT_StartTimer          0x8001'2881       0xa  Code  Lc  zf_driver_delay.o [11]
GPT_StopTimer           0x8001'288b       0xa  Code  Lc  zf_driver_delay.o [11]
GetWheelSpeed           0x8000'4ac1      0xbc  Code  Gb  ce.o [1]
Get_Left_K              0x8000'fe4d      0x68  Code  Gb  yuansu.o [1]
Get_Right_K             0x8000'feb5      0x68  Code  Gb  yuansu.o [1]
Get_angle               0x8000'b835     0x26c  Code  Gb  sxt.o [1]
HEAP$$Base              0x2002'34c0             --   Gb  - Linker created -
HEAP$$Limit             0x2002'38c0             --   Gb  - Linker created -
HardFault_Handler       0x8001'6f31       0x2  Code  Gb  zf_common_vector.o [8]
IOMUXC_SetPinConfig     0x8001'2f25      0x10  Code  Lc  zf_driver_gpio.o [11]
IOMUXC_SetPinMux        0x8001'2f09      0x1c  Code  Lc  zf_driver_gpio.o [11]
I_ex                    0x2000'0f0c       0x4  Data  Gb  siyuanshu.o [1]
I_ey                    0x2000'0f10       0x4  Data  Gb  siyuanshu.o [1]
I_ez                    0x2000'0f14       0x4  Data  Gb  siyuanshu.o [1]
ImagePerspective_Init   0x8000'b705     0x130  Code  Gb  sxt.o [1]
ImagePerspective_Init::BlackColor
                        0x2001'9e77       0x1  Data  Lc  sxt.o [1]
InStream_Read           0x7000'2455      0x20  Code  Lc  lz77_init.o [14]
InStream_StepRegion     0x7000'2441      0x14  Code  Lc  lz77_init.o [14]
Init                    0x8001'6dc7     0x168  Code  Gb  init.o [1]
InverseKinematics       0x8000'4e8d      0x8e  Code  Gb  ce.o [1]
Island_Detect           0x8000'ef59     0x7a0  Code  Gb  yuansu.o [1]
Island_Detect::island_timer_start
                        0x2001'9d18       0x4  Data  Lc  yuansu.o [1]
Island_Detect::left_down_guai
                        0x2001'9d08       0x8  Data  Lc  yuansu.o [1]
Island_Detect::right_down_guai
                        0x2001'9d10       0x8  Data  Lc  yuansu.o [1]
Island_State            0x2001'9cd4       0x4  Data  Gb  yuansu.o [1]
KPP_IRQHandler          0x8001'7029       0x2  Code  Wk  zf_common_vector.o [8]
Kd1                     0x2000'0c34       0x4  Data  Gb  menu.o [1]
Ki1                     0x2000'0ec8       0x4  Data  Gb  menu.o [1]
LCDIF_IRQHandler        0x8001'702f       0x2  Code  Wk  zf_common_vector.o [8]
LPI2C1_DriverIRQHandler
                        0x8000'7c17       0xc  Code  Gb  fsl_lpi2c.o [6]
LPI2C1_IRQHandler       0x8001'6fd7       0x8  Code  Wk  zf_common_vector.o [8]
LPI2C2_DriverIRQHandler
                        0x8000'7c23       0xc  Code  Gb  fsl_lpi2c.o [6]
LPI2C2_IRQHandler       0x8001'6fdf       0x8  Code  Wk  zf_common_vector.o [8]
LPI2C3_DriverIRQHandler
                        0x8000'7c35       0xc  Code  Gb  fsl_lpi2c.o [6]
LPI2C3_IRQHandler       0x8001'6fe7       0x8  Code  Wk  zf_common_vector.o [8]
LPI2C4_DriverIRQHandler
                        0x8000'7c45       0xc  Code  Gb  fsl_lpi2c.o [6]
LPI2C4_IRQHandler       0x8001'6fef       0x8  Code  Wk  zf_common_vector.o [8]
LPI2C_CommonIRQHandler  0x8000'7bd5      0x42  Code  Lc  fsl_lpi2c.o [6]
LPSPI1_DriverIRQHandler
                        0x8000'87d5      0x24  Code  Gb  fsl_lpspi.o [6]
LPSPI1_IRQHandler       0x8001'6ff7       0x8  Code  Wk  zf_common_vector.o [8]
LPSPI2_DriverIRQHandler
                        0x8000'87f9      0x24  Code  Gb  fsl_lpspi.o [6]
LPSPI2_IRQHandler       0x8001'6fff       0x8  Code  Wk  zf_common_vector.o [8]
LPSPI3_DriverIRQHandler
                        0x8000'881d      0x24  Code  Gb  fsl_lpspi.o [6]
LPSPI3_IRQHandler       0x8001'7007       0x8  Code  Wk  zf_common_vector.o [8]
LPSPI4_DriverIRQHandler
                        0x8000'8841      0x24  Code  Gb  fsl_lpspi.o [6]
LPSPI4_IRQHandler       0x8001'700f       0x8  Code  Wk  zf_common_vector.o [8]
LPSPI_CheckTransferArgument
                        0x8000'81b3      0xc6  Code  Gb  fsl_lpspi.o [6]
LPSPI_ClearStatusFlags  0x8000'7d1f       0x4  Code  Lc  fsl_lpspi.o [6]
LPSPI_ClearStatusFlags  0x8001'43d1       0x4  Code  Lc  zf_driver_spi.o [11]
LPSPI_CombineWriteData  0x8000'85b5      0xf8  Code  Lc  fsl_lpspi.o [6]
LPSPI_CommonIRQHandler  0x8000'87a9      0x2c  Code  Lc  fsl_lpspi.o [6]
LPSPI_DisableInterrupts
                        0x8001'43d5       0x8  Code  Lc  zf_driver_spi.o [11]
LPSPI_Enable            0x8000'7ce1      0x1c  Code  Lc  fsl_lpspi.o [6]
LPSPI_Enable            0x8001'43b5      0x1c  Code  Lc  zf_driver_spi.o [11]
LPSPI_FlushFifo         0x8000'7d39      0x1a  Code  Lc  fsl_lpspi.o [6]
LPSPI_FlushFifo         0x8001'43dd      0x1a  Code  Lc  zf_driver_spi.o [11]
LPSPI_GetInstance       0x8000'7d6f      0x44  Code  Gb  fsl_lpspi.o [6]
LPSPI_GetRxFifoCount    0x8000'7d17       0x8  Code  Lc  fsl_lpspi.o [6]
LPSPI_GetRxFifoSize     0x8000'7d01       0xe  Code  Lc  fsl_lpspi.o [6]
LPSPI_GetStatusFlags    0x8000'7cfd       0x4  Code  Lc  fsl_lpspi.o [6]
LPSPI_GetTxFifoCount    0x8000'7d0f       0x8  Code  Lc  fsl_lpspi.o [6]
LPSPI_IsMaster          0x8000'7d31       0x8  Code  Lc  fsl_lpspi.o [6]
LPSPI_MasterGetDefaultConfig
                        0x8000'7ec1      0x7e  Code  Gb  fsl_lpspi.o [6]
LPSPI_MasterInit        0x8000'7dc7      0xfa  Code  Gb  fsl_lpspi.o [6]
LPSPI_MasterSetBaudRate
                        0x8000'7f77      0xa8  Code  Gb  fsl_lpspi.o [6]
LPSPI_MasterSetDelayScaler
                        0x8000'801f      0x4c  Code  Gb  fsl_lpspi.o [6]
LPSPI_MasterSetDelayTimes
                        0x8000'806b     0x148  Code  Gb  fsl_lpspi.o [6]
LPSPI_MasterTransferBlocking
                        0x8000'827d     0x2f8  Code  Gb  fsl_lpspi.o [6]
LPSPI_ReadData          0x8000'7d6b       0x4  Code  Lc  fsl_lpspi.o [6]
LPSPI_Reset             0x8000'7f3f      0x16  Code  Gb  fsl_lpspi.o [6]
LPSPI_SeparateReadData  0x8000'86ad      0xe8  Code  Lc  fsl_lpspi.o [6]
LPSPI_SetDummyData      0x8000'7db3      0x14  Code  Gb  fsl_lpspi.o [6]
LPSPI_SetFifoWatermarks
                        0x8000'7d53      0x14  Code  Lc  fsl_lpspi.o [6]
LPSPI_SetMasterSlaveMode
                        0x8000'7d23       0xe  Code  Lc  fsl_lpspi.o [6]
LPSPI_SetOnePcsPolarity
                        0x8000'7f55      0x22  Code  Lc  fsl_lpspi.o [6]
LPSPI_TxFifoReady       0x8000'8795      0x14  Code  Lc  fsl_lpspi.o [6]
LPSPI_WriteData         0x8000'7d67       0x4  Code  Lc  fsl_lpspi.o [6]
LPUART1_IRQHandler      0x8000'a0f5      0x22  Code  Gb  isr.o [7]
LPUART2_IRQHandler      0x8000'a117      0x1e  Code  Gb  isr.o [7]
LPUART3_IRQHandler      0x8000'a135      0x16  Code  Gb  isr.o [7]
LPUART4_IRQHandler      0x8000'a14b      0x1e  Code  Gb  isr.o [7]
LPUART5_IRQHandler      0x8000'a171      0x20  Code  Gb  isr.o [7]
LPUART6_IRQHandler      0x8000'a191      0x16  Code  Gb  isr.o [7]
LPUART7_DriverIRQHandler
                        0x8000'8dd1      0x14  Code  Gb  fsl_lpuart.o [6]
LPUART7_IRQHandler      0x8001'6fcf       0x8  Code  Wk  zf_common_vector.o [8]
LPUART8_IRQHandler      0x8000'a1ad      0x20  Code  Gb  isr.o [7]
LPUART_ClearStatusFlags
                        0x8000'8d13      0x42  Code  Gb  fsl_lpuart.o [6]
LPUART_Deinit           0x8000'8bd3      0x3e  Code  Gb  fsl_lpuart.o [6]
LPUART_DisableInterrupts
                        0x8000'8cbd      0x40  Code  Gb  fsl_lpuart.o [6]
LPUART_EnableInterrupts
                        0x8000'8c7d      0x40  Code  Gb  fsl_lpuart.o [6]
LPUART_GetDefaultConfig
                        0x8000'8c11      0x6c  Code  Gb  fsl_lpuart.o [6]
LPUART_GetInstance      0x8000'893b      0x34  Code  Gb  fsl_lpuart.o [6]
LPUART_GetStatusFlags   0x8000'8cfd      0x16  Code  Gb  fsl_lpuart.o [6]
LPUART_Init             0x8000'896f     0x264  Code  Gb  fsl_lpuart.o [6]
LPUART_ReadByte         0x8001'4de5      0x2e  Code  Lc  zf_driver_uart.o [11]
LPUART_SoftwareReset    0x8000'8929      0x12  Code  Lc  fsl_lpuart.o [6]
LPUART_WriteBlocking    0x8000'8d55      0x42  Code  Gb  fsl_lpuart.o [6]
LPUART_WriteByte        0x8001'4ddd       0x8  Code  Lc  zf_driver_uart.o [11]
L_corner_angle          0x2001'9ae8       0x4  Data  Gb  sxt.o [1]
L_corner_col            0x2001'9e5c       0x2  Data  Gb  sxt.o [1]
L_corner_flag           0x2001'9e58       0x2  Data  Gb  sxt.o [1]
L_corner_row            0x2001'9e5a       0x2  Data  Gb  sxt.o [1]
L_edge                  0x2001'9458     0x348  Data  Gb  sxt.o [1]
L_edge_count            0x2001'9e7b       0x1  Data  Gb  sxt.o [1]
L_search_amount         0x2000'0c9e       0x1  Data  Gb  sxt.o [1]
L_start_x               0x2001'9e50       0x2  Data  Gb  sxt.o [1]
L_start_y               0x2001'9e52       0x2  Data  Gb  sxt.o [1]
Left_Add_Line           0x8000'c12d      0x98  Code  Gb  sxt.o [1]
Left_Down_Find          0x2001'9444       0x4  Data  Gb  sxt.o [1]
Left_Island_Flag        0x2001'9cd8       0x4  Data  Gb  yuansu.o [1]
Left_Line               0x2001'8658     0x1e0  Data  Gb  sxt.o [1]
Left_Lost_Flag          0x2001'9080     0x1e0  Data  Gb  sxt.o [1]
Left_Lost_Time          0x2001'9064       0x4  Data  Gb  sxt.o [1]
Left_Up_Find            0x2001'9448       0x4  Data  Gb  sxt.o [1]
Lengthen_Left_Boundry   0x8000'c269      0xd8  Code  Gb  sxt.o [1]
Lengthen_Right_Boundry  0x8000'c345      0xd8  Code  Gb  sxt.o [1]
Longest_White_Column    0x8000'bdf3     0x336  Code  Gb  sxt.o [1]
Longest_White_Column_Left
                        0x2001'9070       0x8  Data  Gb  sxt.o [1]
Longest_White_Column_Right
                        0x2001'9078       0x8  Data  Gb  sxt.o [1]
MemManage_Handler       0x8001'6f33       0x2  Code  Gb  zf_common_vector.o [8]
Mid_Line                0x2001'8a18     0x1e0  Data  Gb  sxt.o [1]
Monotonicity_Change_Left
                        0x8000'fb3d     0x16c  Code  Gb  yuansu.o [1]
Monotonicity_Change_Right
                        0x8000'fccd     0x16c  Code  Gb  yuansu.o [1]
Motor_Control           0x8001'53c9     0x284  Code  Gb  zhuangtaiji.o [1]
NMI_Handler             0x8001'6f2f       0x2  Code  Gb  zf_common_vector.o [8]
OutStream_Write         0x7000'2475      0x46  Code  Lc  lz77_init.o [14]
PID_Compute             0x8000'4c55     0x100  Code  Gb  ce.o [1]
PIT_ClearStatusFlags    0x8000'9e69       0xe  Code  Lc  isr.o [7]
PIT_Deinit              0x8000'8f21      0x1e  Code  Gb  fsl_pit.o [6]
PIT_EnableInterrupts    0x8001'3293      0x20  Code  Lc  zf_driver_pit.o [11]
PIT_GetDefaultConfig    0x8001'322f      0x1c  Code  Lc  zf_driver_pit.o [11]
PIT_GetInstance         0x8000'8e7f      0x2e  Code  Lc  fsl_pit.o [6]
PIT_GetStatusFlags      0x8000'9e59      0x10  Code  Lc  isr.o [7]
PIT_IRQHandler          0x8000'9e85     0x270  Code  Gb  isr.o [7]
PIT_Init                0x8000'8ead      0x74  Code  Gb  fsl_pit.o [6]
PIT_SetTimerChainMode   0x8001'324b      0x48  Code  Lc  zf_driver_pit.o [11]
PIT_SetTimerPeriod      0x8001'32b3      0x2a  Code  Lc  zf_driver_pit.o [11]
PIT_StartTimer          0x8001'32dd      0x1e  Code  Lc  zf_driver_pit.o [11]
PMU_EVENT_IRQHandler    0x8001'7071       0x2  Code  Wk  zf_common_vector.o [8]
PORTPTR                 0x2000'0a68      0x2c  Data  Gb  zf_driver_gpio.o [11]
PWM1_0_IRQHandler       0x8001'70c5       0x2  Code  Wk  zf_common_vector.o [8]
PWM1_1_IRQHandler       0x8001'70c7       0x2  Code  Wk  zf_common_vector.o [8]
PWM1_2_IRQHandler       0x8001'70c9       0x2  Code  Wk  zf_common_vector.o [8]
PWM1_3_IRQHandler       0x8001'70cb       0x2  Code  Wk  zf_common_vector.o [8]
PWM1_FAULT_IRQHandler   0x8001'70cd       0x2  Code  Wk  zf_common_vector.o [8]
PWM2_0_IRQHandler       0x8001'7129       0x2  Code  Wk  zf_common_vector.o [8]
PWM2_1_IRQHandler       0x8001'712b       0x2  Code  Wk  zf_common_vector.o [8]
PWM2_2_IRQHandler       0x8001'712d       0x2  Code  Wk  zf_common_vector.o [8]
PWM2_3_IRQHandler       0x8001'712f       0x2  Code  Wk  zf_common_vector.o [8]
PWM2_FAULT_IRQHandler   0x8001'7131       0x2  Code  Wk  zf_common_vector.o [8]
PWM3_0_IRQHandler       0x8001'7133       0x2  Code  Wk  zf_common_vector.o [8]
PWM3_1_IRQHandler       0x8001'7135       0x2  Code  Wk  zf_common_vector.o [8]
PWM3_2_IRQHandler       0x8001'7137       0x2  Code  Wk  zf_common_vector.o [8]
PWM3_3_IRQHandler       0x8001'7139       0x2  Code  Wk  zf_common_vector.o [8]
PWM3_FAULT_IRQHandler   0x8001'713b       0x2  Code  Wk  zf_common_vector.o [8]
PWM4_0_IRQHandler       0x8001'713d       0x2  Code  Wk  zf_common_vector.o [8]
PWM4_1_IRQHandler       0x8001'713f       0x2  Code  Wk  zf_common_vector.o [8]
PWM4_2_IRQHandler       0x8001'7141       0x2  Code  Wk  zf_common_vector.o [8]
PWM4_3_IRQHandler       0x8001'7143       0x2  Code  Wk  zf_common_vector.o [8]
PWM4_FAULT_IRQHandler   0x8001'7145       0x2  Code  Wk  zf_common_vector.o [8]
PWMPTR                  0x2000'0c00      0x14  Data  Lc  zf_driver_pwm.o [11]
PWM_Deinit              0x8000'91fb      0x34  Code  Gb  fsl_pwm.o [6]
PWM_GetComplementU16    0x8000'8fe9       0xa  Code  Lc  fsl_pwm.o [6]
PWM_GetDefaultConfig    0x8000'922f      0x54  Code  Gb  fsl_pwm.o [6]
PWM_GetInstance         0x8000'8ff3      0x34  Code  Lc  fsl_pwm.o [6]
PWM_Init                0x8000'9027     0x1d4  Code  Gb  fsl_pwm.o [6]
PWM_SetPwmLdok          0x8001'3393      0x36  Code  Lc  zf_driver_pwm.o [11]
PWM_SetupPwm            0x8000'9289     0x52a  Code  Gb  fsl_pwm.o [6]
PWM_StartTimer          0x8001'337d      0x16  Code  Lc  zf_driver_pwm.o [11]
PWM_UpdatePwmDutycycleHighAccuracy
                        0x8000'97b9     0x296  Code  Gb  fsl_pwm.o [6]
PXP_IRQHandler          0x8001'7031       0x2  Code  Wk  zf_common_vector.o [8]
PendSV_Handler          0x8001'6f3d       0x2  Code  Gb  zf_common_vector.o [8]
PerImg_ip               0x2000'0f54  0x1'2c00  Data  Gb  sxt.o [1]
QTMR_Deinit             0x8000'9bd7      0x30  Code  Gb  fsl_qtmr.o [6]
QTMR_GetCurrentTimerCount
                        0x8001'2949       0xa  Code  Lc  zf_driver_encoder.o [11]
QTMR_GetDefaultConfig   0x8000'9c07      0x44  Code  Gb  fsl_qtmr.o [6]
QTMR_GetInstance        0x8000'9b0b      0x2e  Code  Lc  fsl_qtmr.o [6]
QTMR_Init               0x8000'9b39      0x9e  Code  Gb  fsl_qtmr.o [6]
QTMR_StartTimer         0x8001'2953      0x26  Code  Lc  zf_driver_encoder.o [11]
Q_info_q0               0x2000'0c58       0x4  Data  Gb  siyuanshu.o [1]
Q_info_q1               0x2000'0f18       0x4  Data  Gb  siyuanshu.o [1]
Q_info_q2               0x2000'0f1c       0x4  Data  Gb  siyuanshu.o [1]
Q_info_q3               0x2000'0f20       0x4  Data  Gb  siyuanshu.o [1]
RTWDOG_IRQHandler       0x8001'70b3       0x2  Code  Wk  zf_common_vector.o [8]
RW$$Base                0x8000'0400             --   Gb  - Linker created -
RW$$Limit               0x8001'72e4             --   Gb  - Linker created -
R_corner_angle          0x2001'9aec       0x4  Data  Gb  sxt.o [1]
R_corner_col            0x2001'9e62       0x2  Data  Gb  sxt.o [1]
R_corner_flag           0x2001'9e5e       0x2  Data  Gb  sxt.o [1]
R_corner_row            0x2001'9e60       0x2  Data  Gb  sxt.o [1]
R_edge                  0x2001'97a0     0x348  Data  Gb  sxt.o [1]
R_edge_count            0x2001'9e7c       0x1  Data  Gb  sxt.o [1]
R_search_amount         0x2000'0c9f       0x1  Data  Gb  sxt.o [1]
R_start_x               0x2001'9e54       0x2  Data  Gb  sxt.o [1]
R_start_y               0x2001'9e56       0x2  Data  Gb  sxt.o [1]
Region$$Table$$Base     0x7000'286c             --   Gb  - Linker created -
Region$$Table$$Limit    0x7000'28bc             --   Gb  - Linker created -
Reserved115_IRQHandler  0x8001'70bf       0x2  Code  Wk  zf_common_vector.o [8]
Reserved143_IRQHandler  0x8001'7115       0x2  Code  Wk  zf_common_vector.o [8]
Reserved144_IRQHandler  0x8001'7117       0x2  Code  Wk  zf_common_vector.o [8]
Reserved171_IRQHandler  0x8001'715f       0x2  Code  Wk  zf_common_vector.o [8]
Reserved68_IRQHandler   0x8001'7041       0x2  Code  Wk  zf_common_vector.o [8]
Reserved78_IRQHandler   0x8001'7073       0x2  Code  Wk  zf_common_vector.o [8]
Reserved86_IRQHandler   0x8001'7083       0x2  Code  Wk  zf_common_vector.o [8]
Reserved87_IRQHandler   0x8001'7085       0x2  Code  Wk  zf_common_vector.o [8]
Reset_Handler           0x7000'277d            Code  Gb  startup_MIMXRT1064.o [5]
Right_Add_Line          0x8000'c1c9      0x98  Code  Gb  sxt.o [1]
Right_Down_Find         0x2001'944c       0x4  Data  Gb  sxt.o [1]
Right_Island_Flag       0x2001'9cdc       0x4  Data  Gb  yuansu.o [1]
Right_Line              0x2001'8838     0x1e0  Data  Gb  sxt.o [1]
Right_Lost_Flag         0x2001'9260     0x1e0  Data  Gb  sxt.o [1]
Right_Lost_Time         0x2001'9068       0x4  Data  Gb  sxt.o [1]
Right_Up_Find           0x2001'9450       0x4  Data  Gb  sxt.o [1]
Road_Wide               0x2001'8bf8     0x1e0  Data  Gb  sxt.o [1]
SAI1_DriverIRQHandler   0x8000'9c97      0x52  Code  Gb  fsl_sai.o [6]
SAI1_IRQHandler         0x8001'7049       0x8  Code  Wk  zf_common_vector.o [8]
SAI2_DriverIRQHandler   0x8000'9ce9      0x52  Code  Gb  fsl_sai.o [6]
SAI2_IRQHandler         0x8001'7051       0x8  Code  Wk  zf_common_vector.o [8]
SAI3_RX_DriverIRQHandler
                        0x8000'9d65      0x2a  Code  Gb  fsl_sai.o [6]
SAI3_RX_IRQHandler      0x8001'7059       0x8  Code  Wk  zf_common_vector.o [8]
SAI3_TX_DriverIRQHandler
                        0x8000'9d3b      0x2a  Code  Gb  fsl_sai.o [6]
SAI3_TX_IRQHandler      0x8001'7061       0x8  Code  Wk  zf_common_vector.o [8]
SAI_RxGetEnabledInterruptStatus
                        0x8000'9c6d      0x16  Code  Lc  fsl_sai.o [6]
SAI_TxGetEnabledInterruptStatus
                        0x8000'9c83      0x14  Code  Lc  fsl_sai.o [6]
SEMC_IRQHandler         0x8001'70df       0x2  Code  Wk  zf_common_vector.o [8]
SJC_IRQHandler          0x8001'7045       0x2  Code  Wk  zf_common_vector.o [8]
SNVS_HP_WRAPPER_IRQHandler
                        0x8001'7035       0x2  Code  Wk  zf_common_vector.o [8]
SNVS_HP_WRAPPER_TZ_IRQHandler
                        0x8001'7037       0x2  Code  Wk  zf_common_vector.o [8]
SNVS_LP_WRAPPER_IRQHandler
                        0x8001'7039       0x2  Code  Wk  zf_common_vector.o [8]
SPDIF_DriverIRQHandler  0x8000'9db5      0x36  Code  Gb  fsl_spdif.o [6]
SPDIF_IRQHandler        0x8001'7069       0x8  Code  Wk  zf_common_vector.o [8]
SRC_IRQHandler          0x8001'70bd       0x2  Code  Wk  zf_common_vector.o [8]
SVC_Handler             0x8001'6f39       0x2  Code  Gb  zf_common_vector.o [8]
Search_Stop_Line        0x2001'9058       0x4  Data  Gb  sxt.o [1]
SetWheelSpeed           0x8000'4d5d     0x12c  Code  Gb  ce.o [1]
SysTick_Handler         0x8001'6f3f       0x2  Code  Wk  zf_common_vector.o [8]
SystemCoreClock         0x2000'0c64       0x4  Data  Gb  system_MIMXRT1064.o [5]
SystemInit              0x7000'2655      0xe4  Code  Gb  system_MIMXRT1064.o [5]
SystemInitHook          0x7000'2779       0x2  Code  Wk  system_MIMXRT1064.o [5]
TEMP_LOW_HIGH_IRQHandler
                        0x8001'7075       0x2  Code  Wk  zf_common_vector.o [8]
TEMP_PANIC_IRQHandler   0x8001'7077       0x2  Code  Wk  zf_common_vector.o [8]
TMR1_IRQHandler         0x8001'7121       0x2  Code  Wk  zf_common_vector.o [8]
TMR2_IRQHandler         0x8001'7123       0x2  Code  Wk  zf_common_vector.o [8]
TMR3_IRQHandler         0x8001'7125       0x2  Code  Wk  zf_common_vector.o [8]
TMR4_IRQHandler         0x8001'7127       0x2  Code  Wk  zf_common_vector.o [8]
TRNG_IRQHandler         0x8001'7043       0x2  Code  Wk  zf_common_vector.o [8]
TSC_DIG_IRQHandler      0x8001'702b       0x2  Code  Wk  zf_common_vector.o [8]
USB_DeviceControl       0x8000'd969      0x2e  Code  Lc  usb_device_dci.o [4]
USB_DeviceEhciCancelControlPipe
                        0x8000'db5d     0x112  Code  Lc  usb_device_ehci.o [4]
USB_DeviceEhciFillSetupBuffer
                        0x8000'dae1      0x7c  Code  Lc  usb_device_ehci.o [4]
USB_DeviceEhciInterruptPortChange
                        0x8000'dfad      0x52  Code  Lc  usb_device_ehci.o [4]
USB_DeviceEhciInterruptReset
                        0x8000'dfff      0x66  Code  Lc  usb_device_ehci.o [4]
USB_DeviceEhciInterruptSof
                        0x8000'e065       0x2  Code  Lc  usb_device_ehci.o [4]
USB_DeviceEhciInterruptTokenDone
                        0x8000'dc6f     0x33e  Code  Lc  usb_device_ehci.o [4]
USB_DeviceEhciIsrFunction
                        0x8000'e067      0x4a  Code  Gb  usb_device_ehci.o [4]
USB_DeviceNotification  0x8000'd9f7      0xbe  Code  Lc  usb_device_dci.o [4]
USB_DeviceNotificationTrigger
                        0x8000'dab5      0x2a  Code  Gb  usb_device_dci.o [4]
USB_DeviceResetNotification
                        0x8000'd997      0x60  Code  Lc  usb_device_dci.o [4]
USB_OTG1_IRQHandler     0x8001'53a9       0xe  Code  Gb  zf_driver_usb_cdc.o [11]
USB_OTG2_IRQHandler     0x8001'53b7       0xe  Code  Gb  zf_driver_usb_cdc.o [11]
USB_PHY1_IRQHandler     0x8001'7079       0x2  Code  Wk  zf_common_vector.o [8]
USB_PHY2_IRQHandler     0x8001'707b       0x2  Code  Wk  zf_common_vector.o [8]
USDHC1_DriverIRQHandler
                        0x8000'9dfd      0x16  Code  Gb  fsl_usdhc.o [6]
USDHC1_IRQHandler       0x8001'70e1       0x8  Code  Wk  zf_common_vector.o [8]
USDHC2_DriverIRQHandler
                        0x8000'9e13      0x16  Code  Gb  fsl_usdhc.o [6]
USDHC2_IRQHandler       0x8001'70e9       0x8  Code  Wk  zf_common_vector.o [8]
UpdateRobotPosition     0x8000'4bc1      0x94  Code  Gb  ce.o [1]
UsageFault_Handler      0x8001'6f37       0x2  Code  Gb  zf_common_vector.o [8]
WDOG1_IRQHandler        0x8001'70b1       0x2  Code  Wk  zf_common_vector.o [8]
WDOG2_IRQHandler        0x8001'7033       0x2  Code  Wk  zf_common_vector.o [8]
White_Column            0x2001'8dd8     0x280  Data  Gb  sxt.o [1]
XBAR1_IRQ_0_1_IRQHandler
                        0x8001'7101       0x2  Code  Wk  zf_common_vector.o [8]
XBAR1_IRQ_2_3_IRQHandler
                        0x8001'7103       0x2  Code  Wk  zf_common_vector.o [8]
ZI$$Base                0x2000'0cc0             --   Gb  - Linker created -
ZI$$Limit               0x2001'9e94             --   Gb  - Linker created -
_PrintfTiny             0x8001'71c1     0x124  Code  Gb  xprintftiny.o [12]
_SDK_AtomicLocalClearAndSet4Byte
                        0x8000'4f7d      0x18  Code  Lc  clock_config.o [5]
_SDK_AtomicLocalClearAndSet4Byte
                        0x8000'5ed1      0x18  Code  Lc  fsl_csi.o [6]
_SDK_AtomicLocalClearAndSet4Byte
                        0x8000'7771      0x18  Code  Lc  fsl_gpio.o [6]
_SDK_AtomicLocalClearAndSet4Byte
                        0x8000'7989      0x18  Code  Lc  fsl_gpt.o [6]
_SDK_AtomicLocalClearAndSet4Byte
                        0x8000'7c69      0x18  Code  Lc  fsl_lpspi.o [6]
_SDK_AtomicLocalClearAndSet4Byte
                        0x8000'88a1      0x18  Code  Lc  fsl_lpuart.o [6]
_SDK_AtomicLocalClearAndSet4Byte
                        0x8000'8dfd      0x18  Code  Lc  fsl_pit.o [6]
_SDK_AtomicLocalClearAndSet4Byte
                        0x8000'8f61      0x18  Code  Lc  fsl_pwm.o [6]
_SDK_AtomicLocalClearAndSet4Byte
                        0x8000'9a89      0x18  Code  Lc  fsl_qtmr.o [6]
_SProut                 0x8001'719b       0xa  Code  Gb  xsprout.o [12]
__CSTACK_ADDRESS {Abs}  0x2007'0000            Data  Gb  <internal module>
__NVIC_EnableIRQ        0x8000'5eb1      0x20  Code  Lc  fsl_csi.o [6]
__NVIC_EnableIRQ        0x8001'0d23      0x1e  Code  Lc  zf_common_interrupt.o [8]
__NVIC_EnableIRQ        0x8001'31ed      0x1e  Code  Lc  zf_driver_pit.o [11]
__NVIC_SetPriority      0x8001'0d41      0x2c  Code  Lc  zf_common_interrupt.o [8]
__NVIC_SetPriorityGrouping
                        0x8001'0d05      0x1e  Code  Lc  zf_common_interrupt.o [8]
__RAM_VECTOR_TABLE_SIZE {Abs}
                              0x400            Data  Gb  <internal module>
__VECTOR_RAM {Abs}      0x8000'0000            Data  Gb  <internal module>
__VECTOR_TABLE {Abs}    0x7000'2000            Data  Gb  <internal module>
__aeabi_assert          0x8000'5675      0x1c  Code  Gb  fsl_assert.o [5]
__aeabi_d2lz            0x8001'609d            Code  Gb  DblToS64.o [13]
__aeabi_l2d             0x8001'6105            Code  Gb  S64ToDbl.o [13]
__aeabi_ldiv0           0x8001'6951            Code  Gb  I64DivZer.o [14]
__aeabi_ldivmod         0x8001'5e29            Code  Gb  I64DivMod.o [14]
__aeabi_memcpy          0x8001'5d19            Code  Gb  ABImemcpy.o [14]
__aeabi_memcpy4         0x8001'5d39            Code  Gb  ABImemcpy.o [14]
__aeabi_memcpy8         0x8001'5d39            Code  Gb  ABImemcpy.o [14]
__aeabi_memset          0x8001'5dc1            Code  Gb  ABImemset.o [14]
__aeabi_uldivmod        0x8001'5e6d            Code  Gb  I64DivMod.o [14]
__basic_free            0x8001'68c1      0x16  Code  Gb  heap0.o [12]
__basic_free_intern     0x8001'68d7      0x74  Code  Lc  heap0.o [12]
__basic_malloc          0x8001'6815      0x18  Code  Gb  heap0.o [12]
__basic_malloc_intern   0x8001'682d      0x94  Code  Lc  heap0.o [12]
__cmain                 0x7000'28cd            Code  Gb  cmain.o [14]
__data_GetMemChunk      0x8001'6d81      0x2c  Code  Gb  xgetmemchunk.o [12]
__data_GetMemChunk::start
                        0x2001'9e4c       0x4  Data  Lc  xgetmemchunk.o [12]
__exit                  0x7000'2641      0x14  Code  Gb  exit.o [15]
__iar_Exp64             0x8001'6955     0x250  Code  Gb  iar_Exp64.o [13]
__iar_Memset            0x8001'5dc1            Code  Gb  ABImemset.o [14]
__iar_Memset_word       0x8001'5dc9            Code  Gb  ABImemset.o [14]
__iar_acos64            0x8000'08f1            Code  Gb  acos.o [13]
__iar_asin64            0x8000'0869            Code  Gb  asin.o [13]
__iar_atan2_64          0x8001'6755            Code  Gb  atan2.o [13]
__iar_data_init3        0x7000'2821      0x28  Code  Gb  data_init.o [14]
__iar_frexp             0x8001'6c2d            Code  Gb  frexp.o [13]
__iar_frexp64           0x8001'6c15            Code  Gb  frexp.o [13]
__iar_frexpl            0x8001'6c2d            Code  Gb  frexp.o [13]
__iar_init_vfp          0x7000'2849            Code  Gb  fpinit_M.o [13]
__iar_ldexp64           0x8001'6c91            Code  Gb  ldexp.o [13]
__iar_lz77_init3        0x7000'24bb     0x17e  Code  Gb  lz77_init.o [14]
__iar_modf              0x8001'6169            Code  Gb  modf.o [13]
__iar_modf64            0x8001'6159            Code  Gb  modf.o [13]
__iar_modfl             0x8001'6169            Code  Gb  modf.o [13]
__iar_pow64             0x8001'61f9     0x4c0  Code  Gb  pow64.o [13]
__iar_pow_medium        0x8001'61f9     0x4c0  Code  Gb  pow64.o [13]
__iar_pow_medium64      0x8001'61f9     0x4c0  Code  Gb  pow64.o [13]
__iar_pow_mediuml       0x8001'61f9     0x4c0  Code  Gb  pow64.o [13]
__iar_program_start     0x7000'28f1            Code  Gb  cstartup_M.o [14]
__iar_scalbln64         0x8001'6c91            Code  Gb  ldexp.o [13]
__iar_scalbn64          0x8001'6c91            Code  Gb  ldexp.o [13]
__iar_sqrt64            0x8001'5cf9            Code  Gb  sqrt.o [13]
__iar_vla_alloc2        0x8001'7185      0x12  Code  Gb  vla_alloc.o [12]
__iar_vla_dealloc2      0x8001'7197       0x4  Code  Gb  vla_alloc.o [12]
__iar_xatan             0x8000'0971            Code  Gb  xatan.o [13]
__iar_zero_init3        0x7000'27e9      0x38  Code  Gb  zero_init3.o [14]
__low_level_init        0x7000'28eb       0x4  Code  Gb  low_level_init.o [12]
__vector_table          0x7000'2400            Data  Gb  vector_table_M.o [14]
__zf_vector_table       0x7000'2000     0x400  Data  Gb  zf_common_vector.o [8]
_call_main              0x7000'28d9            Code  Gb  cmain.o [14]
_exit                   0x8001'6db5            Code  Gb  cexit.o [14]
abort                   0x7000'2639       0x6  Code  Gb  abort.o [12]
abs                     0x8001'7175       0x8  Code  Wk  abs.o [12]
acos                    0x8000'08f1            Code  Gb  acos.o [13]
acosl                   0x8000'08f1            Code  Gb  acos.o [13]
adjust_current_value    0x8000'abfb     0x140  Code  Gb  menu.o [1]
afio_init               0x8001'2f97      0x3c  Code  Gb  zf_driver_gpio.o [11]
anglePID                0x2000'0b60      0x1c  Data  Gb  zhuangtaiji.o [1]
angle_slope             0x2001'9b38       0x4  Data  Gb  xiangzi.o [1]
anglepia                0x2001'9c78       0x4  Data  Gb  xiangzi.o [1]
armPllConfig_BOARD_BootClockRUN
                        0x8000'0af4       0x8  Data  Gb  clock_config.o [5]
arrow_smooth_factor     0x2000'0c54       0x4  Data  Lc  menu.o [1]
arrow_target_y          0x2000'0f04       0x4  Data  Lc  menu.o [1]
arrow_y_position        0x2000'0f00       0x4  Data  Lc  menu.o [1]
ascii_font_6x8          0x8000'2230     0x228  Data  Gb  zf_common_font.o [8]
ascii_font_8x16         0x8000'1c40     0x5f0  Data  Gb  zf_common_font.o [8]
asin                    0x8000'0869            Code  Gb  asin.o [13]
asinl                   0x8000'0869            Code  Gb  asin.o [13]
atan2                   0x8001'6755            Code  Gb  atan2.o [13]
atan2l                  0x8001'6755            Code  Gb  atan2.o [13]
b_d1                    0x2000'0c40       0x4  Data  Gb  menu.o [1]
b_d2                    0x2000'0c44       0x4  Data  Gb  menu.o [1]
b_d3                    0x2000'0c48       0x4  Data  Gb  menu.o [1]
balinyu                 0x8000'd29b      0x82  Code  Gb  sxt.o [1]
binarizeImage           0x8000'bc39      0x40  Code  Gb  sxt.o [1]
boxAlignAnglePID        0x2000'0b7c      0x1c  Data  Gb  zhuangtaiji.o [1]
box_detected            0x2001'9b00       0x4  Data  Gb  uart.o [1]
button_handler          0x8000'ad49     0x250  Code  Gb  menu.o [1]
camera_fifo_init        0x8001'0df1      0x10  Code  Gb  zf_device_camera.o [10]
camera_receiver_buffer  0x2001'9da4       0x8  Data  Gb  zf_device_camera.o [10]
camera_receiver_fifo    0x2001'9d8c      0x18  Data  Gb  zf_device_camera.o [10]
camera_type             0x2001'9e92       0x1  Data  Gb  zf_device_type.o [10]
camera_uart_handler     0x2000'0c84       0x4  Data  Gb  zf_device_type.o [10]
chinese                 0x8000'1580     0x500  Data  Gb  hanzi.o [1]
class                   0x2001'9b2c       0x4  Data  Gb  xiangzi.o [1]
class1                  0x2001'9b30       0x4  Data  Gb  xiangzi.o [1]
classChinese            0x8000'1a80      0x20  Data  Gb  hanzi.o [1]
classIndex              0x2001'9b48       0x4  Data  Gb  xiangzi.o [1]
classValues             0x2001'9b4c     0x12c  Data  Gb  xiangzi.o [1]
clock_init              0x8000'ff45      0x20  Code  Gb  zf_common_clock.o [8]
count_get               0x8000'4b7d      0x42  Code  Gb  ce.o [1]
csi_add_empty_buffer    0x8001'262f      0x12  Code  Gb  zf_driver_csi.o [11]
csi_get_full_buffer     0x8001'2641      0x1c  Code  Gb  zf_driver_csi.o [11]
csi_handle              0x2001'9db0      0x38  Data  Gb  zf_driver_csi.o [11]
csi_init                0x8001'266b     0x15e  Code  Gb  zf_driver_csi.o [11]
csi_iomuxc              0x8001'24d7     0x158  Code  Gb  zf_driver_csi.o [11]
csi_start               0x8001'265d       0xe  Code  Gb  zf_driver_csi.o [11]
current_level           0x2000'0efc       0x4  Data  Lc  menu.o [1]
dcd_data                0x7000'1030     0x410  Data  Gb  evkmimxrt1064_sdram_ini_dcd.o [5]
debug_assert_handler    0x8001'037f      0x46  Code  Gb  zf_common_debug.o [8]
debug_assert_handler{1}{2}::assert_nest_index
                        0x2001'9e90       0x1  Data  Lc  zf_common_debug.o [8]
debug_delay             0x8000'ff65      0x38  Code  Lc  zf_common_debug.o [8]
debug_init              0x8001'0437      0x3a  Code  Gb  zf_common_debug.o [8]
debug_interrupr_handler
                        0x8001'0357      0x28  Code  Gb  zf_common_debug.o [8]
debug_log_handler       0x8001'03c5      0x28  Code  Gb  zf_common_debug.o [8]
debug_output            0x8001'004f     0x308  Code  Lc  zf_common_debug.o [8]
debug_output_info       0x2001'9d74      0x14  Data  Lc  zf_common_debug.o [8]
debug_output_init       0x8001'040d      0x2a  Code  Gb  zf_common_debug.o [8]
debug_output_struct_init
                        0x8001'03ed      0x20  Code  Gb  zf_common_debug.o [8]
debug_protective_handler
                        0x8000'ff9d      0xa4  Code  Lc  zf_common_debug.o [8]
debug_uart_buffer       0x2001'9d1c      0x40  Data  Gb  zf_common_debug.o [8]
debug_uart_data         0x2001'9e8e       0x1  Data  Gb  zf_common_debug.o [8]
debug_uart_fifo         0x2001'9d5c      0x18  Data  Gb  zf_common_debug.o [8]
debug_uart_str_output   0x8001'0041       0xe  Code  Lc  zf_common_debug.o [8]
detected_side           0x2000'0c7c       0x4  Data  Lc  xiangzi.o [1]
dire_left               0x2001'9e7d       0x1  Data  Gb  sxt.o [1]
dire_right              0x2001'9e7e       0x1  Data  Gb  sxt.o [1]
display_menu            0x8000'ab4f      0xac  Code  Gb  menu.o [1]
distancePID             0x2000'0b28      0x1c  Data  Gb  zhuangtaiji.o [1]
distance_cleared_for_phase1
                        0x2001'9e88       0x1  Data  Lc  xiangzi.o [1]
distance_mm             0x2001'9b04       0x4  Data  Gb  uart.o [1]
draw_boundaries         0x8000'bc9f      0xda  Code  Gb  sxt.o [1]
draw_eight_neighborhood_boundaries
                        0x8000'ce39     0x126  Code  Gb  sxt.o [1]
draw_mid_line           0x8000'bd79      0x7a  Code  Gb  sxt.o [1]
dt                      0x8000'0a9c       0x4  Data  Gb  ce.o [1]
enable_L_corner         0x2000'0ca0       0x1  Data  Gb  sxt.o [1]
enable_R_corner         0x2000'0ca1       0x1  Data  Gb  sxt.o [1]
encoder_clear_count     0x8001'2cfd      0x2e  Code  Gb  zf_driver_encoder.o [11]
encoder_dir_init        0x8001'2eed      0x1a  Code  Gb  zf_driver_encoder.o [11]
encoder_get_count       0x8001'2ccd      0x30  Code  Gb  zf_driver_encoder.o [11]
encoder_quad_init       0x8001'2d2b      0xfa  Code  Gb  zf_driver_encoder.o [11]
eulerAngle_pitch        0x2000'0f24       0x4  Data  Gb  siyuanshu.o [1]
eulerAngle_roll         0x2000'0f28       0x4  Data  Gb  siyuanshu.o [1]
eulerAngle_yaw          0x2000'0f2c       0x4  Data  Gb  siyuanshu.o [1]
exit                    0x8001'71a5       0x4  Code  Gb  exit.o [12]
feedbackposition        0x2001'8654       0x4  Data  Gb  sxt.o [1]
feedbackposition1       0x2001'9454       0x4  Data  Gb  sxt.o [1]
fflag                   0x2001'9cf8       0x4  Data  Gb  yuansu.o [1]
fifo_clear              0x8001'0501      0x9e  Code  Gb  zf_common_fifo.o [8]
fifo_end_offset         0x8001'04d9      0x28  Code  Lc  zf_common_fifo.o [8]
fifo_head_offset        0x8001'04b1      0x28  Code  Lc  zf_common_fifo.o [8]
fifo_init               0x8001'0a8b      0x42  Code  Gb  zf_common_fifo.o [8]
fifo_read_buffer        0x8001'0863     0x228  Code  Gb  zf_common_fifo.o [8]
fifo_used               0x8001'059f      0x26  Code  Gb  zf_common_fifo.o [8]
fifo_write_buffer       0x8001'065d     0x206  Code  Gb  zf_common_fifo.o [8]
fifo_write_element      0x8001'05c5      0x98  Code  Gb  zf_common_fifo.o [8]
flag_finish             0x2001'9c9c       0x4  Data  Gb  xiangzi.o [1]
flexio_camera_vsync_handler
                        0x2000'0c88       0x4  Data  Gb  zf_device_type.o [10]
free                    0x8001'7171       0x4  Code  Gb  heaptramp0.o [12]
frexp                   0x8001'6c15            Code  Gb  frexp.o [13]
frexpl                  0x8001'6c15            Code  Gb  frexp.o [13]
func_double_to_str      0x8001'0b5d     0x19a  Code  Gb  zf_common_function.o [8]
func_int_to_str         0x8001'0ad5      0x88  Code  Gb  zf_common_function.o [8]
g_boot_data             0x7000'1020      0x10  Data  Gb  fsl_flexspi_nor_boot.o [5]
g_current_angular_rate  0x2000'0eb4       0x4  Data  Gb  isr.o [7]
g_lpspiDummyData        0x2000'0e3c       0x8  Data  Gb  fsl_lpspi.o [6]
g_rtcXtalFreq           0x2000'0cf4       0x4  Data  Gb  fsl_clock.o [6]
g_target_angular_velocity_setpoint
                        0x2001'9e04       0x4  Data  Gb  zhuangtaiji.o [1]
g_xtalFreq              0x2000'0cf0       0x4  Data  Gb  fsl_clock.o [6]
g_zebra_detected_flag   0x2001'9e10       0x4  Data  Gb  zhuangtaiji.o [1]
g_zebra_start_time      0x2001'9e14       0x4  Data  Gb  zhuangtaiji.o [1]
get_turning_point       0x8000'cf75     0x326  Code  Gb  sxt.o [1]
gpio_get_level          0x8001'30d3      0x1e  Code  Gb  zf_driver_gpio.o [11]
gpio_init               0x8001'3157      0x6a  Code  Gb  zf_driver_gpio.o [11]
gpio_iomuxc             0x8001'2fd3      0xbe  Code  Gb  zf_driver_gpio.o [11]
gpio_set_dir            0x8001'30f1      0x66  Code  Gb  zf_driver_gpio.o [11]
gpio_set_level          0x8001'3091      0x42  Code  Gb  zf_driver_gpio.o [11]
gx_offset               0x2000'0f30       0x4  Data  Gb  siyuanshu.o [1]
gy_offset               0x2000'0f34       0x4  Data  Gb  siyuanshu.o [1]
gyroOffsetInit          0x8000'b009      0xce  Code  Gb  siyuanshu.o [1]
gz_offset               0x2000'0f38       0x4  Data  Gb  siyuanshu.o [1]
hasReceivedFeedback     0x2001'9e8c       0x1  Data  Lc  xiangzi.o [1]
hasRecordedResult       0x2001'9e86       0x1  Data  Lc  xiangzi.o [1]
hd                      0x2000'0abc      0x24  Data  Gb  sxt.o [1]
icmAHRSupdate           0x8000'b27d     0x35e  Code  Gb  siyuanshu.o [1]
icmGetValues            0x8000'b10d     0x152  Code  Gb  siyuanshu.o [1]
icm_ki                  0x2000'0c60       0x4  Data  Gb  siyuanshu.o [1]
icm_kp                  0x2000'0c5c       0x4  Data  Gb  siyuanshu.o [1]
image_draw_rectan       0x8000'c791      0x6e  Code  Gb  sxt.o [1]
image_vector_table      0x7000'1000      0x20  Data  Gb  fsl_flexspi_nor_boot.o [5]
imu660ra_acc_x          0x2001'9e6a       0x2  Data  Gb  zf_device_imu660ra.o [10]
imu660ra_acc_y          0x2001'9e6c       0x2  Data  Gb  zf_device_imu660ra.o [10]
imu660ra_acc_z          0x2001'9e6e       0x2  Data  Gb  zf_device_imu660ra.o [10]
imu660ra_config_file    0x8000'2814    0x2000  Data  Gb  zf_device_config.o [16]
imu660ra_euler_show     0x8000'b5e1      0xac  Code  Gb  siyuanshu.o [1]
imu660ra_get_acc        0x8001'0f3d      0x3e  Code  Gb  zf_device_imu660ra.o [10]
imu660ra_get_gyro       0x8001'0f7b      0x3e  Code  Gb  zf_device_imu660ra.o [10]
imu660ra_gyro_x         0x2001'9e64       0x2  Data  Gb  zf_device_imu660ra.o [10]
imu660ra_gyro_y         0x2001'9e66       0x2  Data  Gb  zf_device_imu660ra.o [10]
imu660ra_gyro_z         0x2001'9e68       0x2  Data  Gb  zf_device_imu660ra.o [10]
imu660ra_init           0x8001'0fb9      0xd6  Code  Gb  zf_device_imu660ra.o [10]
imu660ra_read_register  0x8001'0e93      0x30  Code  Lc  zf_device_imu660ra.o [10]
imu660ra_read_registers
                        0x8001'0ec3      0x44  Code  Lc  zf_device_imu660ra.o [10]
imu660ra_self_check     0x8001'0f07      0x36  Code  Lc  zf_device_imu660ra.o [10]
imu660ra_transition_factor
                        0x2000'0c28       0x8  Data  Gb  zf_device_imu660ra.o [10]
imu660ra_write_register
                        0x8001'0e39      0x2c  Code  Lc  zf_device_imu660ra.o [10]
imu660ra_write_registers
                        0x8001'0e65      0x2e  Code  Lc  zf_device_imu660ra.o [10]
inTextMode              0x2001'9e85       0x1  Data  Gb  xiangzi.o [1]
init_other_menus        0x8000'aa29      0x4e  Code  Gb  menu.o [1]
init_pid_menu           0x8000'a9a9      0x80  Code  Gb  menu.o [1]
interrupt_enable        0x8001'0dc5       0xe  Code  Gb  zf_common_interrupt.o [8]
interrupt_global_disable
                        0x8001'0d99      0x10  Code  Gb  zf_common_interrupt.o [8]
interrupt_init          0x8001'0de7       0xa  Code  Gb  zf_common_interrupt.o [8]
interrupt_nest_count    0x2001'9d88       0x4  Data  Lc  zf_common_interrupt.o [8]
interrupt_set_priority  0x8001'0dd3      0x14  Code  Gb  zf_common_interrupt.o [8]
invSqrt                 0x8000'b0d7      0x36  Code  Gb  siyuanshu.o [1]
isBoxDetectedEntry      0x2000'0ca3       0x1  Data  Lc  xiangzi.o [1]
isInFineTuning          0x2001'9e87       0x1  Data  Lc  xiangzi.o [1]
is_long_straight        0x2001'9d04       0x4  Data  Gb  yuansu.o [1]
l_cir                   0x2001'9ce0       0xc  Data  Gb  yuansu.o [1]
last_push_time          0x2001'9b40       0x4  Data  Gb  xiangzi.o [1]
last_side_detect_time   0x2001'9b44       0x4  Data  Gb  xiangzi.o [1]
ldexp                   0x8001'6c91            Code  Gb  ldexp.o [13]
ldexpl                  0x8001'6c91            Code  Gb  ldexp.o [13]
left                    0x2001'9cb0       0x4  Data  Gb  xiangzi.o [1]
left_findflag           0x2001'9e78       0x1  Data  Gb  sxt.o [1]
lft_raw                 0x2001'9cfc       0x4  Data  Gb  yuansu.o [1]
lnbias                  0x8000'0448     0x420  Data  Lc  pow64.o [13]
low                     0x2000'0c3c       0x4  Data  Gb  menu.o [1]
low1                    0x2000'0c50       0x4  Data  Gb  menu.o [1]
m_boot_hdr_conf_start {Abs}
                        0x7000'0000            Data  Gb  <internal module>
main                    0x8000'a305     0x6a4  Code  Gb  main.o [7]
main::prev_b16_level    0x2000'0c9c       0x1  Data  Lc  main.o [7]
main::prev_b17_level    0x2000'0c9d       0x1  Data  Lc  main.o [7]
main::prev_c18_level    0x2000'0c9a       0x1  Data  Lc  main.o [7]
main::prev_c19_level    0x2000'0c9b       0x1  Data  Lc  main.o [7]
main::prev_special_mode
                        0x2000'0c30       0x4  Data  Lc  main.o [7]
main{1}{2}::turn_off_time
                        0x2000'0ec4       0x4  Data  Lc  main.o [7]
main{1}{2}{4}{5}{6}::current_page
                        0x2001'9e76       0x1  Data  Lc  main.o [7]
main{1}{2}{4}{5}{6}::page_switch_time
                        0x2000'0ec0       0x4  Data  Lc  main.o [7]
menu_init               0x8000'aa77      0x42  Code  Gb  menu.o [1]
menu_length_stack       0x2000'0edc      0x10  Data  Lc  menu.o [1]
menu_level1             0x2000'0100      0xe0  Data  Gb  menu.o [1]
menu_level1_length      0x8000'1b94       0x4  Data  Gb  menu.o [1]
menu_level2_fifth       0x2000'02e0      0x80  Data  Gb  menu.o [1]
menu_level2_first       0x2000'01e0      0x80  Data  Gb  menu.o [1]
menu_level2_fourth      0x2000'0960      0x60  Data  Gb  menu.o [1]
menu_level2_seventh     0x2000'03e0      0x80  Data  Gb  menu.o [1]
menu_level2_sixth       0x2000'0360      0x80  Data  Gb  menu.o [1]
menu_level2_third       0x2000'0260      0x80  Data  Gb  menu.o [1]
menu_pid_angle_params   0x2000'06e0      0x80  Data  Gb  menu.o [1]
menu_pid_distance_params
                        0x2000'0660      0x80  Data  Gb  menu.o [1]
menu_pid_level2         0x2000'0000     0x100  Data  Gb  menu.o [1]
menu_pid_qh_params      0x2000'07e0      0x80  Data  Gb  menu.o [1]
menu_pid_saoxian_params
                        0x2000'0860      0x80  Data  Gb  menu.o [1]
menu_pid_saoxian_params_h
                        0x2000'08e0      0x80  Data  Gb  menu.o [1]
menu_pid_wheel0_params  0x2000'04e0      0x80  Data  Gb  menu.o [1]
menu_pid_wheel1_params  0x2000'0560      0x80  Data  Gb  menu.o [1]
menu_pid_wheel2_params  0x2000'05e0      0x80  Data  Gb  menu.o [1]
menu_pid_wheel_level3   0x2000'0460      0x80  Data  Gb  menu.o [1]
menu_pid_zy_params      0x2000'0760      0x80  Data  Gb  menu.o [1]
menu_stack              0x2000'0ecc      0x10  Data  Lc  menu.o [1]
modf                    0x8001'6159            Code  Gb  modf.o [13]
modfl                   0x8001'6159            Code  Gb  modf.o [13]
mt9v03x_finish_flag     0x2001'9e91       0x1  Data  Gb  zf_device_mt9v03x.o [10]
mt9v03x_finished_callback
                        0x8001'12e5      0x4e  Code  Gb  zf_device_mt9v03x.o [10]
mt9v03x_get_config      0x8001'1177      0xce  Code  Lc  zf_device_mt9v03x.o [10]
mt9v03x_get_confing_buffer
                        0x2000'0ae0      0x24  Data  Lc  zf_device_mt9v03x.o [10]
mt9v03x_get_version     0x8001'1245      0x76  Code  Gb  zf_device_mt9v03x.o [10]
mt9v03x_h_blank         0x2001'9e40       0x4  Data  Lc  zf_device_config.o [16]
mt9v03x_iic_inf_struct  0x2001'9e2c      0x10  Data  Lc  zf_device_config.o [16]
mt9v03x_image           0x2001'9dac       0x4  Data  Gb  zf_device_mt9v03x.o [10]
mt9v03x_image1          0x2001'9ec0    0x4b00  Data  Gb  zf_device_mt9v03x.o [10]
mt9v03x_image2          0x2001'e9c0    0x4b00  Data  Gb  zf_device_mt9v03x.o [10]
mt9v03x_image_2         0x2001'3b54    0x4b00  Data  Gb  sxt.o [1]
mt9v03x_init            0x8001'1333     0x10e  Code  Gb  zf_device_mt9v03x.o [10]
mt9v03x_init_config     0x2001'9e18      0x14  Data  Lc  zf_device_config.o [16]
mt9v03x_read_word_sccb  0x8001'5677      0x28  Code  Lc  zf_device_config.o [16]
mt9v03x_set_config      0x8001'10c9      0xae  Code  Lc  zf_device_mt9v03x.o [10]
mt9v03x_set_config_sccb
                        0x8001'569f     0x646  Code  Gb  zf_device_config.o [16]
mt9v03x_set_confing_buffer
                        0x2000'0a94      0x28  Data  Lc  zf_device_mt9v03x.o [10]
mt9v03x_type            0x2000'0ca5       0x1  Data  Lc  zf_device_mt9v03x.o [10]
mt9v03x_uart_callback   0x8001'12bb      0x2a  Code  Lc  zf_device_mt9v03x.o [10]
mt9v03x_v_blank         0x2001'9e3c       0x4  Data  Lc  zf_device_config.o [16]
mt9v03x_version         0x2001'9e70       0x2  Data  Lc  zf_device_mt9v03x.o [10]
mt9v03x_write_word_sccb
                        0x8001'564d      0x2a  Code  Lc  zf_device_config.o [16]
number                  0x2000'0ce0       0x4  Data  Gb  ce.o [1]
omegaMeasured           0x2000'0cc8       0xc  Data  Gb  ce.o [1]
omegaRef                0x2000'0cd4       0xc  Data  Gb  ce.o [1]
otsuThreshold           0x8000'baad     0x176  Code  Gb  sxt.o [1]
otsu_erzhihua           0x8000'bc79      0x26  Code  Gb  sxt.o [1]
out                     0x8001'71a9      0x18  Code  Lc  xprintftiny.o [12]
phase                   0x2001'9c7c       0x4  Data  Gb  xiangzi.o [1]
phase_transition_angle  0x2001'9b3c       0x4  Data  Gb  xiangzi.o [1]
pia                     0x2001'9afc       0x4  Data  Gb  uart.o [1]
pia1                    0x2001'9b24       0x4  Data  Gb  uart.o [1]
pit_init                0x8001'32fb      0x68  Code  Gb  zf_driver_pit.o [11]
pit_init::init_flag     0x2001'9e93       0x1  Data  Lc  zf_driver_pit.o [11]
position_stable_time    0x2000'0c68       0x4  Data  Gb  xiangzi.o [1]
pow                     0x8001'61f9     0x4c0  Code  Gb  pow64.o [13]
powl                    0x8001'61f9     0x4c0  Code  Gb  pow64.o [13]
push                    0x8000'e149     0x7cc  Code  Gb  xiangzi.o [1]
push::angle_condition_start_time
                        0x2001'9c94       0x4  Data  Lc  xiangzi.o [1]
push::cleared_ti        0x2001'9e89       0x1  Data  Lc  xiangzi.o [1]
push::distance_at_yyy_1
                        0x2001'9c98       0x4  Data  Lc  xiangzi.o [1]
push::first_call        0x2000'0ca2       0x1  Data  Lc  xiangzi.o [1]
push::in_transition     0x2001'9e8a       0x1  Data  Lc  xiangzi.o [1]
push::phase3_align_start_time
                        0x2001'9c88       0x4  Data  Lc  xiangzi.o [1]
push::phase3_timeout_active
                        0x2001'9e8b       0x1  Data  Lc  xiangzi.o [1]
push::phase_transition_start_time
                        0x2001'9c84       0x4  Data  Lc  xiangzi.o [1]
push::saved_class1      0x2001'9c80       0x4  Data  Lc  xiangzi.o [1]
push::yyy_0_start_time  0x2001'9c90       0x4  Data  Lc  xiangzi.o [1]
push::yyy_1_start_time  0x2001'9c8c       0x4  Data  Lc  xiangzi.o [1]
pwm_init                0x8001'3925     0x222  Code  Gb  zf_driver_pwm.o [11]
pwm_iomuxc              0x8001'33c9     0x49a  Code  Gb  zf_driver_pwm.o [11]
pwm_set_duty            0x8001'3863      0xc2  Code  Gb  zf_driver_pwm.o [11]
qhPID                   0x2000'0b98      0x1c  Data  Gb  zhuangtaiji.o [1]
qspiflash_config        0x7000'0000     0x200  Data  Gb  evkmimxrt1064_flexspi_nor_config.o [5]
qtimer_index            0x2000'0bec      0x14  Data  Gb  zf_driver_encoder.o [11]
qtimer_iomuxc           0x8001'2979     0x354  Code  Gb  zf_driver_encoder.o [11]
r_cir                   0x2001'9cec       0xc  Data  Gb  yuansu.o [1]
record_detection_result
                        0x8000'e0b1      0x98  Code  Gb  xiangzi.o [1]
recorded_slope          0x2001'9b34       0x4  Data  Gb  xiangzi.o [1]
rgt_raw                 0x2001'9d00       0x4  Data  Gb  yuansu.o [1]
right                   0x2001'9cb4       0x4  Data  Gb  xiangzi.o [1]
right_findflag          0x2001'9e79       0x1  Data  Gb  sxt.o [1]
robot_x                 0x2000'0ce4       0x4  Data  Gb  ce.o [1]
robot_y                 0x2000'0ce8       0x4  Data  Gb  ce.o [1]
rt1064_storageU1        0x8000'd58f      0x44  Code  Gb  uart.o [1]
rt1064_storageU2        0x8000'd725      0x82  Code  Gb  uart.o [1]
rt1064_storageU4        0x8000'd8ef      0x26  Code  Gb  uart.o [1]
rx_art1                 0x2001'9af4       0x8  Data  Gb  uart.o [1]
rx_art2                 0x2001'9b0c       0x8  Data  Gb  uart.o [1]
rx_art4                 0x2001'9b18       0x8  Data  Gb  uart.o [1]
rx_data1                0x2001'9e7f       0x1  Data  Gb  uart.o [1]
rx_data2                0x2001'9e81       0x1  Data  Gb  uart.o [1]
rx_data4                0x2001'9e83       0x1  Data  Gb  uart.o [1]
s_EDMAHandle            0x2000'0d14      0x80  Data  Lc  fsl_edma.o [6]
s_ENETHandle            0x2000'0d94       0x8  Data  Lc  fsl_enet.o [6]
s_baudratePrescaler     0x8000'10d0       0x8  Data  Lc  fsl_lpspi.o [6]
s_cdcVcom               0x2001'9df4      0x10  Data  Gb  zf_driver_usb_cdc.o [11]
s_csiBases              0x8000'0c30       0x4  Data  Lc  fsl_csi.o [6]
s_csiClocks             0x8001'6dbe       0x2  Data  Lc  fsl_csi.o [6]
s_csiHandle             0x2000'0cf8       0x4  Data  Lc  fsl_csi.o [6]
s_csiIRQ                0x8001'6dc0       0x2  Data  Lc  fsl_csi.o [6]
s_csiIsr                0x2000'0cfc       0x4  Data  Lc  fsl_csi.o [6]
s_debugConsole          0x2000'0d00      0x14  Data  Lc  fsl_debug_console.o [5]
s_enet1588TimerIsr      0x2000'0dbc       0x8  Data  Lc  fsl_enet.o [6]
s_enetBases             0x8000'0cf0       0x8  Data  Lc  fsl_enet.o [6]
s_enetErrIsr            0x2000'0dac       0x8  Data  Lc  fsl_enet.o [6]
s_enetRxIsr             0x2000'0da4       0x8  Data  Lc  fsl_enet.o [6]
s_enetTsIsr             0x2000'0db4       0x8  Data  Lc  fsl_enet.o [6]
s_enetTxIsr             0x2000'0d9c       0x8  Data  Lc  fsl_enet.o [6]
s_flexcanHandle         0x2000'0dc4      0x10  Data  Lc  fsl_flexcan.o [6]
s_flexcanIsr            0x2000'0dd4       0x4  Data  Lc  fsl_flexcan.o [6]
s_flexioHandle          0x2000'0dd8       0x8  Data  Lc  fsl_flexio.o [6]
s_flexioIsr             0x2000'0de8       0x8  Data  Lc  fsl_flexio.o [6]
s_flexioType            0x2000'0de0       0x8  Data  Lc  fsl_flexio.o [6]
s_gpioBases             0x8000'0e20      0x2c  Data  Lc  fsl_gpio.o [6]
s_gpioClock             0x8000'0e4c       0xc  Data  Lc  fsl_gpio.o [6]
s_gptBases              0x8000'0f60       0xc  Data  Lc  fsl_gpt.o [6]
s_gptClocks             0x8000'0f6c       0x8  Data  Lc  fsl_gpt.o [6]
s_lpi2cMasterHandle     0x2000'0df4      0x14  Data  Gb  fsl_lpi2c.o [6]
s_lpi2cMasterIsr        0x2000'0df0       0x4  Data  Gb  fsl_lpi2c.o [6]
s_lpi2cSlaveHandle      0x2000'0e0c      0x14  Data  Lc  fsl_lpi2c.o [6]
s_lpi2cSlaveIsr         0x2000'0e08       0x4  Data  Lc  fsl_lpi2c.o [6]
s_lpspiBases            0x8000'10d8      0x14  Data  Lc  fsl_lpspi.o [6]
s_lpspiClocks           0x8000'10ec       0xc  Data  Lc  fsl_lpspi.o [6]
s_lpspiHandle           0x2000'0e20      0x14  Data  Lc  fsl_lpspi.o [6]
s_lpspiMasterIsr        0x2000'0e34       0x4  Data  Lc  fsl_lpspi.o [6]
s_lpspiSlaveIsr         0x2000'0e38       0x4  Data  Lc  fsl_lpspi.o [6]
s_lpuartBases           0x8000'1228      0x24  Data  Lc  fsl_lpuart.o [6]
s_lpuartClock           0x8000'124c      0x14  Data  Lc  fsl_lpuart.o [6]
s_lpuartHandle          0x2000'0e44      0x24  Data  Gb  fsl_lpuart.o [6]
s_lpuartIsr             0x2000'0e68       0x4  Data  Gb  fsl_lpuart.o [6]
s_pitBases              0x8000'1310       0x4  Data  Lc  fsl_pit.o [6]
s_pitClocks             0x8001'6dc2       0x2  Data  Lc  fsl_pit.o [6]
s_pwmBases              0x8000'140c      0x14  Data  Lc  fsl_pwm.o [6]
s_pwmClocks             0x8000'1420      0x28  Data  Lc  fsl_pwm.o [6]
s_qtmrBases             0x8000'14f8      0x14  Data  Lc  fsl_qtmr.o [6]
s_qtmrClocks            0x8000'150c       0xc  Data  Lc  fsl_qtmr.o [6]
s_saiHandle             0x2000'0e6c      0x20  Data  Lc  fsl_sai.o [6]
s_saiRxIsr              0x2000'0e90       0x4  Data  Lc  fsl_sai.o [6]
s_saiTxIsr              0x2000'0e8c       0x4  Data  Lc  fsl_sai.o [6]
s_spdifHandle           0x2000'0e94       0x8  Data  Lc  fsl_spdif.o [6]
s_spdifRxIsr            0x2000'0ea0       0x4  Data  Lc  fsl_spdif.o [6]
s_spdifTxIsr            0x2000'0e9c       0x4  Data  Lc  fsl_spdif.o [6]
s_usdhcBase             0x8000'1574       0xc  Data  Lc  fsl_usdhc.o [6]
s_usdhcHandle           0x2000'0ea4       0xc  Data  Lc  fsl_usdhc.o [6]
s_usdhcIsr              0x2000'0eb0       0x4  Data  Lc  fsl_usdhc.o [6]
safe                    0x2000'0c78       0x4  Data  Gb  xiangzi.o [1]
saoxianPID              0x2000'0bb4      0x1c  Data  Gb  zhuangtaiji.o [1]
saoxianPIDh             0x2000'0bd0      0x1c  Data  Gb  zhuangtaiji.o [1]
scalbln                 0x8001'6c91            Code  Gb  ldexp.o [13]
scalblnl                0x8001'6c91            Code  Gb  ldexp.o [13]
scalbn                  0x8001'6c91            Code  Gb  ldexp.o [13]
scalbnl                 0x8001'6c91            Code  Gb  ldexp.o [13]
search_neighborhood     0x8000'c7ff     0x63a  Code  Gb  sxt.o [1]
selected_index_stack    0x2000'0eec      0x10  Data  Lc  menu.o [1]
set_camera_type         0x8001'23df      0x1c  Code  Gb  zf_device_type.o [10]
side_detected           0x2001'9e8d       0x1  Data  Lc  xiangzi.o [1]
side_detection_start_time
                        0x2001'9cb8       0x4  Data  Lc  xiangzi.o [1]
slope                   0x2001'9b28       0x4  Data  Gb  uart.o [1]
slope_raw               0x2001'9ca0       0x4  Data  Gb  xiangzi.o [1]
soft_iic_init           0x8001'4261      0x7a  Code  Gb  zf_driver_soft_iic.o [11]
soft_iic_read_data      0x8001'4085     0x11c  Code  Lc  zf_driver_soft_iic.o [11]
soft_iic_sccb_read_register
                        0x8001'41f5      0x6c  Code  Gb  zf_driver_soft_iic.o [11]
soft_iic_sccb_write_register
                        0x8001'41a9      0x4c  Code  Gb  zf_driver_soft_iic.o [11]
soft_iic_send_ack       0x8001'3dfd      0xce  Code  Lc  zf_driver_soft_iic.o [11]
soft_iic_send_data      0x8001'3f9f      0xe6  Code  Lc  zf_driver_soft_iic.o [11]
soft_iic_start          0x8001'3cb5      0xa4  Code  Lc  zf_driver_soft_iic.o [11]
soft_iic_stop           0x8001'3d59      0xa4  Code  Lc  zf_driver_soft_iic.o [11]
soft_iic_wait_ack       0x8001'3ecb      0xd4  Code  Lc  zf_driver_soft_iic.o [11]
special_mode            0x2000'0f08       0x4  Data  Gb  menu.o [1]
special_mode_4_angle    0x2000'0ebc       0x4  Data  Lc  isr.o [7]
special_mode_4_entry    0x2001'9e74       0x1  Data  Lc  isr.o [7]
special_mode_4_initialized
                        0x2001'9e75       0x1  Data  Lc  isr.o [7]
special_mode_4_timer    0x2000'0eb8       0x4  Data  Lc  isr.o [7]
spi_cs_index            0x2001'9de8       0xc  Data  Lc  zf_driver_spi.o [11]
spi_index               0x2000'0c14      0x14  Data  Lc  zf_driver_spi.o [11]
spi_init                0x8001'4a89     0x23c  Code  Gb  zf_driver_spi.o [11]
spi_iomuxc              0x8001'43f7     0x430  Code  Gb  zf_driver_spi.o [11]
spi_read_8bit_registers
                        0x8001'4a2d      0x50  Code  Gb  zf_driver_spi.o [11]
spi_write               0x8001'4827      0xfc  Code  Gb  zf_driver_spi.o [11]
spi_write_16bit         0x8001'4955      0x1e  Code  Gb  zf_driver_spi.o [11]
spi_write_16bit_array   0x8001'4973      0x36  Code  Gb  zf_driver_spi.o [11]
spi_write_8bit          0x8001'4943      0x12  Code  Gb  zf_driver_spi.o [11]
spi_write_8bit_array    0x8001'4923      0x20  Code  Gb  zf_driver_spi.o [11]
spi_write_8bit_register
                        0x8001'49a9      0x34  Code  Gb  zf_driver_spi.o [11]
spi_write_8bit_registers
                        0x8001'49dd      0x50  Code  Gb  zf_driver_spi.o [11]
sprintf                 0x8001'67d5      0x40  Code  Gb  sprintf.o [12]
sqrt                    0x8001'5cf9            Code  Gb  sqrt.o [13]
sqrtl                   0x8001'5cf9            Code  Gb  sqrt.o [13]
storageU1               0x2001'9af0       0x4  Data  Gb  uart.o [1]
storageU2               0x2001'9b08       0x4  Data  Gb  uart.o [1]
storageU4               0x2001'9b14       0x4  Data  Gb  uart.o [1]
strcmp                  0x8001'6741            Code  Gb  strcmp.o [14]
strlen                  0x8001'6709            Code  Gb  strlen.o [14]
system_clock            0x2000'0c80       0x4  Data  Gb  zf_common_clock.o [8]
system_delay_init       0x8001'2911      0x2c  Code  Gb  zf_driver_delay.o [11]
system_delay_ms         0x8001'28d3      0x3e  Code  Gb  zf_driver_delay.o [11]
target_lr               0x2001'9ca8       0x4  Data  Gb  xiangzi.o [1]
target_qh               0x2001'9ca4       0x4  Data  Gb  xiangzi.o [1]
tft180_bgcolor          0x2001'9e72       0x2  Data  Lc  zf_device_tft180.o [10]
tft180_clear            0x8001'1695      0x9a  Code  Gb  zf_device_tft180.o [10]
tft180_debug_init       0x8001'162b      0x6a  Code  Lc  zf_device_tft180.o [10]
tft180_display_dir      0x2000'0ca6       0x1  Data  Lc  zf_device_tft180.o [10]
tft180_display_font     0x2000'0ca7       0x1  Data  Lc  zf_device_tft180.o [10]
tft180_draw_point       0x8001'1779      0x88  Code  Gb  zf_device_tft180.o [10]
tft180_init             0x8001'2055     0x368  Code  Gb  zf_device_tft180.o [10]
tft180_pencolor         0x2000'0c98       0x2  Data  Lc  zf_device_tft180.o [10]
tft180_set_color        0x8001'176b       0xe  Code  Gb  zf_device_tft180.o [10]
tft180_set_dir          0x8001'172f      0x3c  Code  Gb  zf_device_tft180.o [10]
tft180_set_region       0x8001'14d1     0x15a  Code  Lc  zf_device_tft180.o [10]
tft180_show_char        0x8001'1803     0x228  Code  Gb  zf_device_tft180.o [10]
tft180_show_chinese     0x8001'1ecd     0x178  Code  Gb  zf_device_tft180.o [10]
tft180_show_float       0x8001'1bd7     0x15a  Code  Gb  zf_device_tft180.o [10]
tft180_show_gray_image  0x8001'1d4d     0x178  Code  Gb  zf_device_tft180.o [10]
tft180_show_int         0x8001'1add      0xfa  Code  Gb  zf_device_tft180.o [10]
tft180_show_string      0x8001'1a2d      0xb0  Code  Gb  zf_device_tft180.o [10]
tft180_write_index      0x8001'14ad      0x24  Code  Lc  zf_device_tft180.o [10]
tft180_x_max            0x2000'0ca8       0x1  Data  Lc  zf_device_tft180.o [10]
tft180_y_max            0x2000'0ca9       0x1  Data  Lc  zf_device_tft180.o [10]
ti                      0x2000'0cc0       0x4  Data  Gb  ce.o [1]
tim                     0x2000'0cc4       0x4  Data  Gb  ce.o [1]
time1                   0x2000'0c6c       0x4  Data  Gb  xiangzi.o [1]
time2                   0x2000'0c70       0x4  Data  Gb  xiangzi.o [1]
time3                   0x2000'0c74       0x4  Data  Gb  xiangzi.o [1]
tof_module_exti_handler
                        0x2000'0c94       0x4  Data  Gb  zf_device_type.o [10]
top                     0x2000'0c38       0x4  Data  Gb  menu.o [1]
top1                    0x2000'0c4c       0x4  Data  Gb  menu.o [1]
top_detected            0x2001'9b20       0x4  Data  Gb  uart.o [1]
total_distance          0x2000'0cec       0x4  Data  Gb  ce.o [1]
type_default_callback   0x8001'23dd       0x2  Code  Gb  zf_device_type.o [10]
uart1_rx_art            0x8000'd441     0x14e  Code  Gb  uart.o [1]
uart1_rx_art::stage     0x2001'9e80       0x1  Data  Lc  uart.o [1]
uart1_rx_interrupt_new_handler
                        0x8000'd429      0x18  Code  Gb  uart.o [1]
uart2_rx_art            0x8000'd5eb     0x13a  Code  Gb  uart.o [1]
uart2_rx_art::stage     0x2001'9e82       0x1  Data  Lc  uart.o [1]
uart2_rx_interrupt_new_handler
                        0x8000'd5d3      0x18  Code  Gb  uart.o [1]
uart4_rx_art            0x8000'd7bd     0x132  Code  Gb  uart.o [1]
uart4_rx_art::stage     0x2001'9e84       0x1  Data  Lc  uart.o [1]
uart4_rx_interrupt_new_handler
                        0x8000'd7a7      0x16  Code  Gb  uart.o [1]
uart_index              0x2000'0b04      0x24  Data  Lc  zf_driver_uart.o [11]
uart_init               0x8001'5243      0x8e  Code  Gb  zf_driver_uart.o [11]
uart_iomuxc             0x8001'4e13     0x34a  Code  Gb  zf_driver_uart.o [11]
uart_query_byte         0x8001'51d1      0x34  Code  Gb  zf_driver_uart.o [11]
uart_rx_interrupt       0x8001'5205      0x3e  Code  Gb  zf_driver_uart.o [11]
uart_write_buffer       0x8001'5185      0x2a  Code  Gb  zf_driver_uart.o [11]
uart_write_byte         0x8001'515d      0x28  Code  Gb  zf_driver_uart.o [11]
uart_write_string       0x8001'51af      0x22  Code  Gb  zf_driver_uart.o [11]
update_arrow_animation  0x8000'aab9      0x46  Code  Gb  menu.o [1]
update_menu_animation   0x8000'ab01      0x4e  Code  Gb  menu.o [1]
vx                      0x2001'9e0c       0x4  Data  Gb  zhuangtaiji.o [1]
vy                      0x2001'9e08       0x4  Data  Gb  zhuangtaiji.o [1]
wheelPID                0x2000'09c0      0x54  Data  Gb  ce.o [1]
wheelPID_postPush       0x2000'0a14      0x54  Data  Gb  ce.o [1]
wireless_module_spi_handler
                        0x2000'0c90       0x4  Data  Gb  zf_device_type.o [10]
wireless_module_uart_handler
                        0x2000'0c8c       0x4  Data  Gb  zf_device_type.o [10]
yyy                     0x2001'9cac       0x4  Data  Gb  xiangzi.o [1]
zf_debug_assert_enable  0x2000'0ca4       0x1  Data  Lc  zf_common_debug.o [8]
zf_debug_init_flag      0x2001'9e8f       0x1  Data  Lc  zf_common_debug.o [8]
zong                    0x8000'e991     0x4f0  Code  Gb  xiangzi.o [1]
zong::initialPhaseStartTime
                        0x2001'9cd0       0x4  Data  Lc  xiangzi.o [1]
zong::positionStable    0x2001'9cc0       0x4  Data  Lc  xiangzi.o [1]
zong::positionStableStartTime
                        0x2001'9cbc       0x4  Data  Lc  xiangzi.o [1]
zong::sentA5            0x2001'9ccc       0x4  Data  Lc  xiangzi.o [1]
zong::textModeStartTime
                        0x2001'9cc4       0x4  Data  Lc  xiangzi.o [1]
zong::waitingForFeedback
                        0x2001'9cc8       0x4  Data  Lc  xiangzi.o [1]
zongsaoxian             0x8000'd35d      0x88  Code  Gb  sxt.o [1]
zyPID                   0x2000'0b44      0x1c  Data  Gb  zhuangtaiji.o [1]


[1] = D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\code_17896468042025673432.dir
[2] = D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\components_fatfs_6246014242642884545.dir
[3] = D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\components_sdmmc_11335736203712880765.dir
[4] = D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\components_usb_17375887477349322284.dir
[5] = D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_device_3373403627085545761.dir
[6] = D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir
[7] = D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\user_c_11930989646151335152.dir
[8] = D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_common_1201165462030747823.dir
[9] = D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_components_14936052685886095442.dir
[10] = D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir
[11] = D:\yuanshi\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir
[12] = dl7M_tlf.a
[13] = m7M_tlv.a
[14] = rt7M_tl.a
[15] = shb_l.a
[16] = zf_device_config.a

    1'210 bytes of readonly  code memory
   77'032 bytes of readwrite code memory
   65'964 bytes of readonly  data memory
  166'522 bytes of readwrite data memory

Errors: none
Warnings: none
