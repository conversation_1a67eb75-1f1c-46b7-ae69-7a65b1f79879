; 
; NXP  Communication Device Class driver installation file
; Copyright 2016 NXP
;

[Version]
Signature="$Windows NT$"
Class=Ports
ClassGuid={4D36E978-E325-11CE-BFC1-08002BE10318}
Provider=%MFGNAME%
CatalogFile=%MFGFILENAME%.cat
DriverVer=02/16/2011,1.0

[Manufacturer]
%MFGNAME%=DeviceList, NTamd64

[DestinationDirs]
DefaultDestDir=12


;------------------------------------------------------------------------------
; Windows 2000/XP/Vista-32bit Sections
;------------------------------------------------------------------------------

[DriverInstall.nt]
include=mdmcpq.inf
AddReg=DriverInstall.nt.AddReg

[DriverInstall.nt.AddReg]
HKR,,<PERSON>Loader,,*ntkern
HKR,,NTMPDriver,,%DRIVERFILENAME%.sys
HKR,,EnumPropPages32,,"MsPorts.dll,SerialPortPropPageProvider"

[DriverInstall.nt.Services]
AddService=usbser, 0x00000002, DriverService.nt

[DriverService.nt]
DisplayName=%SERVICE%
ServiceType=1
StartType=3
ErrorControl=1
ServiceBinary=%12%\%DRIVERFILENAME%.sys

;------------------------------------------------------------------------------
; Vista-64bit Sections
;------------------------------------------------------------------------------

[DriverInstall.NTamd64]
include=mdmcpq.inf
AddReg=DriverInstall.NTamd64.AddReg

[DriverInstall.NTamd64.AddReg]
HKR,,DevLoader,,*ntkern
HKR,,NTMPDriver,,%DRIVERFILENAME%.sys
HKR,,EnumPropPages32,,"MsPorts.dll,SerialPortPropPageProvider"

[DriverInstall.NTamd64.Services]
AddService=usbser, 0x00000002, DriverService.NTamd64

[DriverService.NTamd64]
DisplayName=%SERVICE%
ServiceType=1
StartType=3
ErrorControl=1
ServiceBinary=%12%\%DRIVERFILENAME%.sys


;------------------------------------------------------------------------------
; Vendor and Product ID Definitions
;------------------------------------------------------------------------------
; When developing your USB device, the VID and PID used in the PC side
; application program and the firmware on the microcontroller must match.
; Modify the below line to use your VID and PID. Use the format as shown below.
; Note: One INF file can be used for multiple devices with different VID and PIDs.
; For each supported device, append ",USB\VID_xxxx&PID_yyyy" to the end of the line.
;------------------------------------------------------------------------------
[SourceDisksFiles]
[SourceDisksNames]
[DeviceList]
%DESCRIPTION%=DriverInstall, USB\VID_1FC9&PID_0094
%DESCRIPTION%=DriverInstall, USB\VID_1FC9&PID_009E&MI_00
%DESCRIPTION%=DriverInstall, USB\VID_1FC9&PID_009F&MI_00
%DESCRIPTION%=DriverInstall, USB\VID_1FC9&PID_00A3&MI_00

[DeviceList.NTamd64]
%DESCRIPTION% = DriverInstall, USB\VID_1FC9&PID_0094
%DESCRIPTION% = DriverInstall, USB\VID_1FC9&PID_009E&MI_00
%DESCRIPTION% = DriverInstall, USB\VID_1FC9&PID_009F&MI_00
%DESCRIPTION% = DriverInstall, USB\VID_1FC9&PID_00A3&MI_00

;------------------------------------------------------------------------------
; String Definitions
;------------------------------------------------------------------------------
;Modify these strings to customize your device
;------------------------------------------------------------------------------
[Strings]
MFGFILENAME="CDC"
DRIVERFILENAME ="usbser"
MFGNAME="NXP"
INSTDISK="NXP CDC Driver Installer"
DESCRIPTION="Virtual Com Port"
SERVICE="NXP Virtual COM Driver"

