#include "zf_common_headfile.h"
#include "zf_common_headfile.h"

// 外部变量声明



float g_target_angular_velocity_setpoint = 0.0f;
float vy = 0.0f; // 假设vy为0，表示不需要前后移动
float vx = 0.0f; // 假设vx为0，表示不需要左右移动
int g_zebra_detected_flag = 0;
unsigned long g_zebra_start_time = 0;
int g_direct_omegaRef_override = 0; // 1: omegaRef由Motor_Control直接设置, 0: omegaRef由PIT_CH2内环设置
PID_TypeDef g_angular_velocity_pid;      // 内环角速度PID控制器 (需要初始化)
PID_TypeDef distancePID[1] = {
    {.kp = 1.0f, .ki = 0.0f, .kd = 0.0f, .error_last = 0.0f, .error_second_last = 0.0f, .output = 0.0f, .is_angle=0, .pid_mode=0}
};
PID_TypeDef zyPID[1] = {
    {.kp = 0.35f, .ki = 0.0f, .kd = 0.05f, .error_last = 0.0f, .error_second_last = 0.0f, .output = 0.0f, .is_angle=0, .pid_mode=1}
};
PID_TypeDef anglePID[1] = {
    {.kp = 2.1f, .ki = 0.0f, .kd = 0.0f, .error_last = 0.0f, .error_second_last = 0.0f, .output = 0.0f, .is_angle=1, .pid_mode=0}
};
// 赛道对准用角度PID
PID_TypeDef boxAlignAnglePID[1] = {
    {.kp = 1.5f, .ki = 0.0f, .kd = 0.0f, .error_last = 0.0f, .error_second_last = 0.0f, .output = 0.0f, .is_angle=1, .pid_mode=1}
};
// 推箱子用角度PID
PID_TypeDef textModeAnglePID[1] = {
    {.kp = 5.5f, .ki = 0.0f, .kd = 0.0f, .error_last = 0.0f, .error_second_last = 0.0f, .output = 0.0f, .is_angle=1, .pid_mode=0}
};
PID_TypeDef qhPID[1] = {
    {.kp = 0.4f, .ki = 0.0f, .kd = 0.11f, .error_last = 0.0f, .error_second_last = 0.0f, .output = 0.0f, .is_angle=0, .pid_mode=1}
};
PID_TypeDef saoxianPID[1] = {
    {.kp = 1.0f, .ki = 0.0f, .kd = 0.0f, .error_last = 0.0f, .error_second_last = 0.0f, .output = 0.0f, .is_angle=0, .pid_mode=1}
};
PID_TypeDef saoxianPIDh[1] = {
    {.kp = 0.79f, .ki = 0.0f, .kd = 0.0f, .error_last = 0.0f, .error_second_last = 0.0f, .output = 0.0f, .is_angle=0, .pid_mode=1}
};
// 角速度环PID控制器，调整参数适应Y轴陀螺仪数据
PID_TypeDef angularRatePID[1] = {
    {.kp = 2.0f, .ki = 0.1f, .kd = 0.05f, .error_last = 0.0f, .error_second_last = 0.0f, .output = 0.0f, .is_angle=0, .pid_mode=1}
};
void Motor_Control(void) {
    // 定义变量用于检测斑马线
    int target_row = 50;
    int transitions = 0;
    int white_count = 0;
    float speed[3]; // 局部变量，主要用于斑马线直接速度设定

    // 检测斑马线
    if (!g_zebra_detected_flag) {
        for (int col = 40; col < 120; col++) {
            if (mt9v03x_image_2[target_row][col] != mt9v03x_image_2[target_row][col - 1]) {
                transitions++;
            }
            if (mt9v03x_image_2[target_row][col] == 255) {
                white_count++;
            }
        }
        if (transitions > 10 && white_count > 30) {
            g_zebra_detected_flag = 1;
            g_zebra_start_time = tim;
        }
    }

    if (g_zebra_detected_flag) {
        if (tim - g_zebra_start_time < 1200) {
            if (tim - g_zebra_start_time < 1200) {
                vx= 0.0f; // 假设vx为0，表示不需要左右移动
                vy = 80.0f; // 假设vy为60，表示需要前后移动
                g_target_angular_velocity_setpoint=0.0f; // 假设目标角速度为0
            } else {
                vx= 0.0f; // 假设vx为0，表示不需要左右移动
                vy = 60.0f; // 假设vy为60，表示需要前后移动
                g_target_angular_velocity_setpoint = PID_Compute(&saoxianPIDh[0], 80.0f, feedbackposition);
            }

        } else {
                vx= 0.0f; // 假设vx为0，表示不需要左右移动
                vy = 0.0f; // 假设vy为60，表示需要前后移动
                g_target_angular_velocity_setpoint = 0.0f;
                special_mode = 5;
                // tft180_clear();
                g_zebra_detected_flag=0; // 重置斑马线检测标志

        }
    } else {
        float desired_omega_z;
        if (Island_State == 0 && Cross_Flag == 0) {
            top = top1;
            low = low1;
            // 判断是否为长直道，是则使用b_d3速度，否则使用b_d1速度
            if (is_long_straight) {
                vy = b_d3; // 长直道使用更高速度
            } else {
                vy = b_d1; // 一般情况速度
            }
            float target_heading = 80.0f;
            desired_omega_z = PID_Compute(&saoxianPIDh[0], target_heading, feedbackposition1);
            vx= -desired_omega_z*0.75f; // 假设vx为0，表示不需要左右移动
            //vx=0;
        } else if (Island_State != 0 && Cross_Flag == 0 && Island_State!=2) {
            top = 50;
            low = 80;
            vx= 0.0f; // 假设vx为0，表示不需要左右移动
            vy = b_d2; // 假设vy为60，表示需要前后移动
            // 环岛巡线控制逻辑，输出期望角速度
            desired_omega_z = PID_Compute(&saoxianPID[0], 80.0f, feedbackposition1);
        }else if(Island_State == 0 && Cross_Flag == 1) {
            top = top1;
            low = low1;
            // 判断是否为长直道，是则使用b_d3速度，否则使用b_d1速度
            if (is_long_straight) {
                vy = b_d3; // 长直道使用更高速度
            } else {
                vy = b_d1; // 一般情况速度
            }
            vx= 0.0f; // 假设vx为0，表示不需要左右移动
            // 环岛巡线控制逻辑，输出期望角速度
            desired_omega_z = PID_Compute(&saoxianPID[0], 80.0f, feedbackposition1)*0.5;
        }
        g_target_angular_velocity_setpoint = desired_omega_z;
    }
}


