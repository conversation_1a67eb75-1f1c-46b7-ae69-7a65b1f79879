#include "zf_common_headfile.h"
#include <string.h>

// 枚举类型，定义值的类型
typedef enum {
    VALUE_TYPE_INT,
    VALUE_TYPE_FLOAT
} ValueType;

// 扩展的菜单项结构体，支持子菜单（submenu）及子菜单长度（submenu_length）
typedef struct MenuItem {
    const char *title;               // 菜单项标题
    void *value;                     // 指向变量的指针（若无值则为 NULL）
    ValueType type;                  // 值的类型
    float min_value;                 // 最小值
    float max_value;                 // 最大值
    float step;                      // 步进值
    const struct MenuItem *submenu;  // 指向子菜单数组，若无子菜单则为 NULL
    int submenu_length;              // 子菜单项数量
} MenuItem;

// -------------------- 全局变量与 PID 定义 --------------------

// 原有变量
float Kp = 15;
float Ki = 0;
float Kd = 30;

float Kp1 = 20;
float Ki1 = 0;
float Kd1 = 30;

int top = 40;
int low = 100;
int b_d1 = 95;
int b_d2 = 85;
int b_d3 = 105;  // 注意：原代码中 b_d3 为 1100，此处可根据需要调整

int top1 = 50;  // 非环岛巡线时的上限
int low1 = 82;  // 非环岛巡线时的下限

// -------------------- 菜单项定义 --------------------

// 一级菜单项
MenuItem menu_level1[] = {
    {"Baceduty", NULL, VALUE_TYPE_INT, 0, 0, 0, NULL, 0},
    {"PID", NULL, VALUE_TYPE_INT, 0, 0, 0, NULL, 0},    // 该项的子菜单将在 init_pid_menu() 中关联
    {"CANSHU", NULL, VALUE_TYPE_INT, 0, 0, 0, NULL, 0},
    {"Fanwei", NULL, VALUE_TYPE_INT, 0, 0, 0, NULL, 0},
    {"Fifth", NULL, VALUE_TYPE_INT, 0, 0, 0, NULL, 0},
    {"Sixth", NULL, VALUE_TYPE_INT, 0, 0, 0, NULL, 0},
    {"Seventh", NULL, VALUE_TYPE_INT, 0, 0, 0, NULL, 0},
};
const int menu_level1_length = sizeof(menu_level1) / sizeof(menu_level1[0]);

// 二级菜单 for "Baceduty"
MenuItem menu_level2_first[] = {
    {"b_d1", &b_d1, VALUE_TYPE_INT, 0, 6000, 5, NULL, 0},
    {"b_d2", &b_d2, VALUE_TYPE_INT, 0, 6000, 5, NULL, 0},
    {"b_d3", &b_d3, VALUE_TYPE_INT, 0, 6000, 5, NULL, 0},
    {"Back", NULL, VALUE_TYPE_INT, 0, 0, 0, NULL, 0}
};

// 二级菜单 for "CANSHU"
MenuItem menu_level2_third[] = {
    {"wentim", &position_stable_time, VALUE_TYPE_INT, 0, 50000, 100, NULL, 0},
    {"Ki1", &Ki1, VALUE_TYPE_FLOAT, 0.0, 100.0, 0.01, NULL, 0},
    {"Kd1", &Kd1, VALUE_TYPE_FLOAT, 0.0, 500.0, 0.5, NULL, 0},
    {"Back", NULL, VALUE_TYPE_INT, 0, 0, 0, NULL, 0}
};

// 二级菜单 for "Fanwei"
MenuItem menu_level2_fourth[] = {
    {"top", &top1, VALUE_TYPE_INT, 0, 128, 5, NULL, 0},
    {"low", &low1, VALUE_TYPE_INT, 0, 128, 5, NULL, 0},
    {"Back", NULL, VALUE_TYPE_INT, 0, 0, 0, NULL, 0}
};

// 二级菜单 for "Fifth"
MenuItem menu_level2_fifth[] = {
    {"time1", &time1, VALUE_TYPE_INT, 0, 70000, 100, NULL, 0},
    {"time2", &time2, VALUE_TYPE_INT, 0, 70000, 100, NULL, 0},
    {"time3", &time3, VALUE_TYPE_INT, 0, 70000, 100, NULL, 0},
    {"Back", NULL, VALUE_TYPE_INT, 0, 0, 0, NULL, 0}
};

// 二级菜单 for "Sixth"
MenuItem menu_level2_sixth[] = {
    {"min", NULL, VALUE_TYPE_INT, 0, 7000, 100, NULL, 0},
    {"max", NULL, VALUE_TYPE_INT, 0, 7000, 100, NULL, 0},
    {"tim", NULL, VALUE_TYPE_INT, 0, 2000, 5, NULL, 0},
    {"Back", NULL, VALUE_TYPE_INT, 0, 0, 0, NULL, 0}
};

// 二级菜单 for "Seventh"
MenuItem menu_level2_seventh[] = {
    {"min", NULL, VALUE_TYPE_INT, 0, 7000, 100, NULL, 0},
    {"max", NULL, VALUE_TYPE_INT, 0, 7000, 100, NULL, 0},
    {"tim", NULL, VALUE_TYPE_INT, 0, 2000, 5, NULL, 0},
    {"Back", NULL, VALUE_TYPE_INT, 0, 0, 0, NULL, 0}
};

// -------------------- PID 多级菜单定义 --------------------

// PID 二级菜单：包含 wheelPID, distancePID, anglePID, Back
MenuItem menu_pid_level2[] = {
    {"wheelPID",    NULL, VALUE_TYPE_INT, 0, 0, 0, NULL, 0},
    {"distancePID", NULL, VALUE_TYPE_INT, 0, 0, 0, NULL, 0},
    {"anglePID",    NULL, VALUE_TYPE_INT, 0, 0, 0, NULL, 0},
    {"zyPID",       NULL, VALUE_TYPE_INT, 0, 0, 0, NULL, 0},  // 新增 zyPID 选项
    {"qhPID",       NULL, VALUE_TYPE_INT, 0, 0, 0, NULL, 0},
    {"saoxianPID",  NULL, VALUE_TYPE_INT, 0, 0, 0, NULL, 0},
    {"saoxianPIDh",  NULL, VALUE_TYPE_INT, 0, 0, 0, NULL, 0},
    {"Back",        NULL, VALUE_TYPE_INT, 0, 0, 0, NULL, 0}
};


// PID 三级菜单（仅 wheelPID 分支）：显示 ZQ, YQ, H, Back（分别对应 wheelPID 数组的第 0、1、2 项）
MenuItem menu_pid_wheel_level3[] = {
    {"ZQ", NULL, VALUE_TYPE_INT, 0, 0, 0, NULL, 0},
    {"YQ", NULL, VALUE_TYPE_INT, 0, 0, 0, NULL, 0},
    {"H",  NULL, VALUE_TYPE_INT, 0, 0, 0, NULL, 0},
    {"Back", NULL, VALUE_TYPE_INT, 0, 0, 0, NULL, 0}
};

// PID 四级菜单：参数调整界面（kp, ki, kd, Back）
MenuItem menu_pid_wheel0_params[] = {
    {"kp", &wheelPID[0].kp, VALUE_TYPE_FLOAT, 0, 300, 0.5, NULL, 0},
    {"ki", &wheelPID[0].ki, VALUE_TYPE_FLOAT, 0, 100, 0.01, NULL, 0},
    {"kd", &wheelPID[0].kd, VALUE_TYPE_FLOAT, 0, 500, 0.01, NULL, 0},
    {"Back", NULL, VALUE_TYPE_INT, 0, 0, 0, NULL, 0}
};
MenuItem menu_pid_wheel1_params[] = {
    {"kp", &wheelPID[1].kp, VALUE_TYPE_FLOAT, 0, 300, 0.5, NULL, 0},
    {"ki", &wheelPID[1].ki, VALUE_TYPE_FLOAT, 0, 100, 0.01, NULL, 0},
    {"kd", &wheelPID[1].kd, VALUE_TYPE_FLOAT, 0, 500, 0.01, NULL, 0},
    {"Back", NULL, VALUE_TYPE_INT, 0, 0, 0, NULL, 0}
};
MenuItem menu_pid_wheel2_params[] = {
    {"kp", &wheelPID[2].kp, VALUE_TYPE_FLOAT, 0, 300, 0.5, NULL, 0},
    {"ki", &wheelPID[2].ki, VALUE_TYPE_FLOAT, 0, 100, 0.01, NULL, 0},
    {"kd", &wheelPID[2].kd, VALUE_TYPE_FLOAT, 0, 500, 0.01, NULL, 0},
    {"Back", NULL, VALUE_TYPE_INT, 0, 0, 0, NULL, 0}
};
MenuItem menu_pid_distance_params[] = {
    {"kp", &distancePID[0].kp, VALUE_TYPE_FLOAT, 0, 300, 0.5, NULL, 0},
    {"ki", &distancePID[0].ki, VALUE_TYPE_FLOAT, 0, 100, 0.01, NULL, 0},
    {"kd", &distancePID[0].kd, VALUE_TYPE_FLOAT, 0, 500, 0.01, NULL, 0},
    {"Back", NULL, VALUE_TYPE_INT, 0, 0, 0, NULL, 0}
};
MenuItem menu_pid_angle_params[] = {
    {"kp", &anglePID[0].kp, VALUE_TYPE_FLOAT, 0, 300, 0.1, NULL, 0},
    {"ki", &anglePID[0].ki, VALUE_TYPE_FLOAT, 0, 100, 0.01, NULL, 0},
    {"kd", &anglePID[0].kd, VALUE_TYPE_FLOAT, 0, 500, 0.01, NULL, 0},
    {"Back", NULL, VALUE_TYPE_INT, 0, 0, 0, NULL, 0}
};
// PID 四级菜单：zyPID 参数调整界面（kp, ki, kd, Back）
MenuItem menu_pid_zy_params[] = {
    {"kp", &zyPID[0].kp, VALUE_TYPE_FLOAT, 0, 300, 0.05, NULL, 0},
    {"ki", &zyPID[0].ki, VALUE_TYPE_FLOAT, 0, 100, 0.01, NULL, 0},
    {"kd", &zyPID[0].kd, VALUE_TYPE_FLOAT, 0, 500, 0.01, NULL, 0},
    {"Back", NULL, VALUE_TYPE_INT, 0, 0, 0, NULL, 0}
};
MenuItem menu_pid_qh_params[] = {
    {"kp", &qhPID[0].kp, VALUE_TYPE_FLOAT, 0, 300, 0.05, NULL, 0},
    {"ki", &qhPID[0].ki, VALUE_TYPE_FLOAT, 0, 100, 0.01, NULL, 0},
    {"kd", &qhPID[0].kd, VALUE_TYPE_FLOAT, 0, 500, 0.01, NULL, 0},
    {"Back", NULL, VALUE_TYPE_INT, 0, 0, 0, NULL, 0}
};
MenuItem menu_pid_saoxian_params[] = {
    {"kp", &saoxianPID[0].kp, VALUE_TYPE_FLOAT, 0, 300, 0.05, NULL, 0},
    {"ki", &saoxianPID[0].ki, VALUE_TYPE_FLOAT, -100, 100, 0.01, NULL, 0},
    {"kd", &saoxianPID[0].kd, VALUE_TYPE_FLOAT, -500, 500, 0.0001, NULL, 0},
    {"Back", NULL, VALUE_TYPE_INT, 0, 0, 0, NULL, 0}
};
MenuItem menu_pid_saoxian_params_h[] = {
    {"kp", &saoxianPIDh[0].kp, VALUE_TYPE_FLOAT, 0, 300, 0.05, NULL, 0},
    {"ki", &saoxianPIDh[0].ki, VALUE_TYPE_FLOAT, -100, 100, 0.01, NULL, 0},
    {"kd", &saoxianPIDh[0].kd, VALUE_TYPE_FLOAT, -500, 500, 0.0001, NULL, 0},
    {"Back", NULL, VALUE_TYPE_INT, 0, 0, 0, NULL, 0}
};
// 关联 PID 菜单各级关系（在初始化时调用）
void init_pid_menu(void) {
    // 关联一级菜单 "PID"
    menu_level1[1].submenu = menu_pid_level2;
    menu_level1[1].submenu_length = sizeof(menu_pid_level2) / sizeof(menu_pid_level2[0]);

    // 关联 PID 二级菜单各项子菜单：
    menu_pid_level2[0].submenu = menu_pid_wheel_level3;      // wheelPID
    menu_pid_level2[0].submenu_length = sizeof(menu_pid_wheel_level3) / sizeof(menu_pid_wheel_level3[0]);

    menu_pid_level2[1].submenu = menu_pid_distance_params;     // distancePID
    menu_pid_level2[1].submenu_length = sizeof(menu_pid_distance_params) / sizeof(menu_pid_distance_params[0]);

    menu_pid_level2[2].submenu = menu_pid_angle_params;        // anglePID
    menu_pid_level2[2].submenu_length = sizeof(menu_pid_angle_params) / sizeof(menu_pid_angle_params[0]);

    // 新增 zyPID 关联 zyPID 参数调整菜单
    menu_pid_level2[3].submenu = menu_pid_zy_params;
    menu_pid_level2[3].submenu_length = sizeof(menu_pid_zy_params) / sizeof(menu_pid_zy_params[0]);

    // 新增 qhPID 关联 qhPID 参数调整菜单
    menu_pid_level2[4].submenu = menu_pid_qh_params;
    menu_pid_level2[4].submenu_length = sizeof(menu_pid_qh_params) / sizeof(menu_pid_qh_params[0]);

    // 新增 saoxianPID 关联 saoxianPID 参数调整菜单
    menu_pid_level2[5].submenu = menu_pid_saoxian_params;
    menu_pid_level2[5].submenu_length = sizeof(menu_pid_saoxian_params) / sizeof(menu_pid_saoxian_params[0]);

    // 新增 saoxianPIDh 关联 saoxianPIDh 参数调整菜单
    menu_pid_level2[6].submenu = menu_pid_saoxian_params_h;
    menu_pid_level2[6].submenu_length = sizeof(menu_pid_saoxian_params_h) / sizeof(menu_pid_saoxian_params_h[0]);

    // "Back" 项保持无子菜单

    // 在 wheelPID 的三级菜单中，关联各项到四级参数调整菜单
    menu_pid_wheel_level3[0].submenu = menu_pid_wheel0_params; // ZQ 对应 wheelPID[0]
    menu_pid_wheel_level3[0].submenu_length = sizeof(menu_pid_wheel0_params) / sizeof(menu_pid_wheel0_params[0]);

    menu_pid_wheel_level3[1].submenu = menu_pid_wheel1_params; // YQ 对应 wheelPID[1]
    menu_pid_wheel_level3[1].submenu_length = sizeof(menu_pid_wheel1_params) / sizeof(menu_pid_wheel1_params[0]);

    menu_pid_wheel_level3[2].submenu = menu_pid_wheel2_params; // H 对应 wheelPID[2]
    menu_pid_wheel_level3[2].submenu_length = sizeof(menu_pid_wheel2_params) / sizeof(menu_pid_wheel2_params[0]);
}


// 为其他一级菜单项关联对应的二级菜单
void init_other_menus(void) {
    // "Baceduty" (索引 0)
    menu_level1[0].submenu = menu_level2_first;
    menu_level1[0].submenu_length = sizeof(menu_level2_first) / sizeof(menu_level2_first[0]);

    // "PID2" (索引 2)
    menu_level1[2].submenu = menu_level2_third;
    menu_level1[2].submenu_length = sizeof(menu_level2_third) / sizeof(menu_level2_third[0]);

    // "Fanwei" (索引 3)
    menu_level1[3].submenu = menu_level2_fourth;
    menu_level1[3].submenu_length = sizeof(menu_level2_fourth) / sizeof(menu_level2_fourth[0]);

    // "Fifth" (索引 4)
    menu_level1[4].submenu = menu_level2_fifth;
    menu_level1[4].submenu_length = sizeof(menu_level2_fifth) / sizeof(menu_level2_fifth[0]);

    // "Sixth" (索引 5)
    menu_level1[5].submenu = menu_level2_sixth;
    menu_level1[5].submenu_length = sizeof(menu_level2_sixth) / sizeof(menu_level2_sixth[0]);

    // "Seventh" (索引 6)
    menu_level1[6].submenu = menu_level2_seventh;
    menu_level1[6].submenu_length = sizeof(menu_level2_seventh) / sizeof(menu_level2_seventh[0]);
}

// -------------------- 多级菜单管理（基于栈结构） --------------------

#define MAX_MENU_LEVEL 4
static const MenuItem *menu_stack[MAX_MENU_LEVEL];
static int menu_length_stack[MAX_MENU_LEVEL];
static int selected_index_stack[MAX_MENU_LEVEL];
static int current_level = 0; // 当前菜单层级：0 表示一级菜单

// 箭头动画相关变量
static float arrow_y_position = 0;     // 箭头当前Y坐标位置
static float arrow_target_y = 0;       // 箭头目标Y坐标位置
static float arrow_smooth_factor = 0.3f; // 平滑系数，值越小移动越平滑但也越慢

// 初始化菜单系统：初始化各菜单并设置根菜单为一级菜单
void menu_init(void) {
    init_pid_menu();
    init_other_menus();
    current_level = 0;
    menu_stack[0] = menu_level1;
    menu_length_stack[0] = menu_level1_length;
    selected_index_stack[0] = 0;
    
    // 初始化箭头位置
    arrow_y_position = 0;
    arrow_target_y = 0;
}

// 更新箭头动画
void update_arrow_animation(void) {
    // 计算目标位置
    arrow_target_y = selected_index_stack[current_level] * 16;
    
    // 平滑过渡：当前位置向目标位置靠近
    arrow_y_position = arrow_y_position + (arrow_target_y - arrow_y_position) * arrow_smooth_factor;
}

// 更新菜单动画效果
void update_menu_animation(void) {
    // 只在菜单模式并且箭头未到达目标位置时更新动画
    if (special_mode == 0) {
        // 检查是否需要更新箭头位置
        float diff = arrow_target_y - arrow_y_position;
        if (fabs(diff) > 0.1) {  // 如果差距足够大
            update_arrow_animation();
            display_menu();
            system_delay_ms(10);  // 短暂延时以控制动画速度
        }
    }
}

// 显示当前菜单界面
void display_menu(void) {
    tft180_clear();
    const MenuItem *current_menu = menu_stack[current_level];
    int menu_length = menu_length_stack[current_level];
    
    // 更新箭头动画
    update_arrow_animation();
    
    // 绘制移动箭头
    tft180_show_string(20, (int)arrow_y_position, "> ");
    
    // 显示菜单项
    for (int i = 0; i < menu_length; i++) {
        tft180_show_string(36, i * 16, current_menu[i].title);
        // 若当前菜单项有关联变量则显示其数值
        if (current_menu[i].value != NULL) {
            if (current_menu[i].type == VALUE_TYPE_INT)
                tft180_show_int(90, i * 16, *(int*)current_menu[i].value, 4);
            else if (current_menu[i].type == VALUE_TYPE_FLOAT)
                tft180_show_float(90, i * 16, *(float*)current_menu[i].value, 3, 3);
        }
    }
}

// 调整当前菜单项的数值（增加或减少）
void adjust_current_value(bool increase) {
    const MenuItem *current_menu = menu_stack[current_level];
    int idx = selected_index_stack[current_level];
    if (current_menu[idx].value == NULL)
        return;
    if (current_menu[idx].type == VALUE_TYPE_INT) {
        int *v = (int*)current_menu[idx].value;
        if (increase) {
            *v += (int)current_menu[idx].step;
            if (*v > (int)current_menu[idx].max_value)
                *v = (int)current_menu[idx].max_value;
        } else {
            *v -= (int)current_menu[idx].step;
            if (*v < (int)current_menu[idx].min_value)
                *v = (int)current_menu[idx].min_value;
        }
    } else if (current_menu[idx].type == VALUE_TYPE_FLOAT) {
        float *v = (float*)current_menu[idx].value;
        if (increase) {
            *v += current_menu[idx].step;
            if (*v > current_menu[idx].max_value)
                *v = current_menu[idx].max_value;
        } else {
            *v -= current_menu[idx].step;
            if (*v < current_menu[idx].min_value)
                *v = current_menu[idx].min_value;
        }
    }
}

// 全局变量，用于记录当前显示模式，0：菜单模式；1~3：特殊显示模式
int special_mode = 0;

// 按钮处理函数
void button_handler(void) {
    // 优先检测 C26 键：无论当前处于哪一层级都有效
    if (gpio_get_level(C6) == 0) {
        tft180_clear();  // 切换模式前先清屏
        special_mode++;
        if (special_mode > 5)
            special_mode = 0;  // 循环切换：0→1→2→3→4→0
        
        // 切换到菜单模式时刷新菜单显示
        if (special_mode == 0) {
            // 重置箭头位置与当前选择项保持一致
            arrow_y_position = selected_index_stack[current_level] * 16;
            arrow_target_y = arrow_y_position;
            display_menu();
        }
        system_delay_ms(200);  // 防抖延时
        return;  // 切换模式后不处理其他按键
    }
    
    // 仅在菜单模式（special_mode == 0）下处理其他按键
    if (special_mode == 0) {
        const MenuItem *current_menu = menu_stack[current_level];
        int menu_length = menu_length_stack[current_level];
        bool menu_changed = false;
        
        if (gpio_get_level(C26) == 0) {  // 上按钮
            if (selected_index_stack[current_level] > 0)
                selected_index_stack[current_level]--;
            else
                selected_index_stack[current_level] = menu_length - 1;
            menu_changed = true;
            system_delay_ms(200);
        } else if (gpio_get_level(C12) == 0) {  // 下按钮
            if (selected_index_stack[current_level] < menu_length - 1)
                selected_index_stack[current_level]++;
            else
                selected_index_stack[current_level] = 0;
            menu_changed = true;
            system_delay_ms(200);
        } else if (gpio_get_level(C24) == 0) {  // 确认按钮（C6）
            int idx = selected_index_stack[current_level];
            // 当选中 "Back" 项时直接返回上一级菜单
            if (strcmp(current_menu[idx].title, "Back") == 0) {
                if (current_level > 0) {
                    current_level--;
                    // 重置箭头位置与新菜单的当前选择项保持一致
                    arrow_y_position = selected_index_stack[current_level] * 16;
                    arrow_target_y = arrow_y_position;
                }
                menu_changed = true;
            }
            // 当选中可更改项时，确认键用于增加数值
            else if (current_menu[idx].value != NULL) {
                adjust_current_value(true);
                menu_changed = true;
            }
            // 当选中不可更改且存在子菜单时，进入子菜单
            else if (current_menu[idx].submenu != NULL && current_menu[idx].submenu_length > 0) {
                if (current_level < MAX_MENU_LEVEL - 1) {
                    current_level++;
                    menu_stack[current_level] = current_menu[idx].submenu;
                    menu_length_stack[current_level] = current_menu[idx].submenu_length;
                    selected_index_stack[current_level] = 0;
                    // 重置箭头位置与新菜单的当前选择项保持一致
                    arrow_y_position = 0;
                    arrow_target_y = 0;
                }
                menu_changed = true;
            }
            system_delay_ms(200);
        } else if (gpio_get_level(C14) == 0) {  // 返回/减少按钮（C2）
            int idx = selected_index_stack[current_level];
            // 当选中不可更改项（包括 "Back"）时返回上一级菜单
            if (current_menu[idx].value == NULL) {
                if (current_level > 0) {
                    current_level--;
                    // 重置箭头位置与新菜单的当前选择项保持一致
                    arrow_y_position = selected_index_stack[current_level] * 16;
                    arrow_target_y = arrow_y_position;
                }
                menu_changed = true;
            }
            // 当选中可更改项时，返回键用于减少数值
            else {
                adjust_current_value(false);
                menu_changed = true;
            }
            system_delay_ms(200);
        }
        
        // 如果菜单状态发生变化，重新显示菜单
        if (menu_changed) {
            tft180_clear();
            display_menu();
        }
    }
}



// // 主循环示例
// void main_loop(void) {
//     menu_init();
//     display_menu();
//     while (1) {
//         button_handler();
//         update_menu_animation();  // 更新菜单动画效果
//         // 可在此添加其他逻辑……
//     }
// }

// // 如需提供入口函数
// int main(void) {
//     // 系统及硬件初始化代码……
//     main_loop();
//     return 0;
// }
