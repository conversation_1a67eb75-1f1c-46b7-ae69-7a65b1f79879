# Slope偏移量旋转功能实现说明

## 功能概述
根据您的需求，我已经实现了在推箱子过程中使用slope偏移量进行旋转补偿的功能。

## 实现的功能

### 1. 记录slope值
- 在`waitingForFeedback == 1 && flag_finish == 1 && !hasReceivedFeedback`条件满足时
- 调用`record_detection_result()`函数记录识别结果
- 该函数会记录当前的`slope`值到`classValues[classIndex].slope_value`中

### 2. 在push过程中的phase 0阶段应用slope偏移
- 在`push()`函数的phase 0阶段，获取最近记录的slope值
- 根据推箱子方向（class1值）计算slope偏移量：
  - `class1 = 0`（向左推）：slope值直接作为偏移量
  - `class1 = 1`（向右推）：slope值取反作为偏移量
- 将偏移量加到基础旋转速度上

## 代码修改详情

### 1. 修改了`push()`函数
```c
// 获取记录的slope值用于phase 0的偏移旋转
int recorded_slope = 0;
if (classIndex > 0) {
    recorded_slope = classValues[classIndex - 1].slope_value;
}
```

### 2. 在phase 0中添加slope偏移计算
```c
// 在phase 0中加上slope偏移量的旋转
// class1 = 0时是向左推（车模面对箱子绕到箱子右侧后向前推）
// slope > 0代表车模在向右推箱子时需要向左转更多slope角度
float slope_offset = 0.0f;
if (saved_class1 == 0) {
    // 向左推时，slope值直接作为偏移量（slope > 0表示需要向左转更多）
    slope_offset = recorded_slope * 0.1f; // 可以调整系数来控制偏移强度
} else {
    // 向右推时，slope值取反作为偏移量
    slope_offset = -recorded_slope * 0.1f; // 可以调整系数来控制偏移强度
}

g_target_angular_velocity_setpoint = wz_dir * 30.0f + slope_offset;
```

### 3. 添加了调试函数
- `get_latest_recorded_slope()`：获取最近记录的slope值，用于调试

## 工作原理

1. **记录阶段**：当车模检测到箱子并收到上位机反馈时，记录当前的slope值
2. **应用阶段**：在推箱子的phase 0阶段，根据推箱子方向应用slope偏移
3. **偏移逻辑**：
   - 向左推（class1=0）：slope > 0时增加左转角度
   - 向右推（class1=1）：slope > 0时减少右转角度（相当于增加左转）

## 参数调整
- 偏移强度系数：当前设置为0.1f，可以根据实际效果调整
- 该系数控制slope值对旋转速度的影响程度

## 注意事项
- slope值在`record_detection_result()`函数中被记录到`ClassEntry`结构体中
- 偏移量只在phase 0阶段生效，确保不影响其他推箱子阶段
- 使用`saved_class1`而不是全局`class1`，避免在推箱子过程中被外部改变

## 测试建议
1. 可以使用`get_latest_recorded_slope()`函数查看记录的slope值
2. 观察phase 0阶段的旋转行为是否符合预期
3. 根据实际效果调整偏移强度系数（0.1f）
