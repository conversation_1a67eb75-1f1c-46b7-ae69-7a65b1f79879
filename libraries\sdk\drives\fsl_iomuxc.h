/*
 * Copyright 2016 Freescale Semiconductor, Inc.
 * Copyright 2016-2022 NXP
 * All rights reserved.
 *
 * SPDX-License-Identifier: BSD-3-Clause
 */

#ifndef _FSL_IOMUXC_H_
#define _FSL_IOMUXC_H_

#include "fsl_common.h"

/*!
 * @addtogroup iomuxc_driver
 * @{
 */

/*! @file */

/*******************************************************************************
 * Definitions
 ******************************************************************************/
/* Component ID definition, used by tools. */
#ifndef FSL_COMPONENT_ID
#define FSL_COMPONENT_ID "platform.drivers.iomuxc"
#endif

/*! @name Driver version */
/*@{*/
/*! @brief IOMUXC driver version 2.0.3. */
#define FSL_IOMUXC_DRIVER_VERSION (MAKE_VERSION(2, 0, 4))
/*@}*/

/*!
 * @name Pin function ID
 * The pin function ID is a tuple of \<muxRegister muxMode inputRegister inputDaisy configRegister\>
 *
 * @{
 */
#define IOMUXC_GPIO_EMC_00_SEMC_DATA00 0x401F8014U, 0x0U, 0, 0, 0x401F8204U
#define IOMUXC_GPIO_EMC_00_FLEXPWM4_PWMA00 0x401F8014U, 0x1U, 0x401F8494U, 0x0U, 0x401F8204U
#define IOMUXC_GPIO_EMC_00_LPSPI2_SCK 0x401F8014U, 0x2U, 0x401F8500U, 0x1U, 0x401F8204U
#define IOMUXC_GPIO_EMC_00_XBAR1_XBAR_IN02 0x401F8014U, 0x3U, 0x401F860CU, 0x0U, 0x401F8204U
#define IOMUXC_GPIO_EMC_00_FLEXIO1_FLEXIO00 0x401F8014U, 0x4U, 0, 0, 0x401F8204U
#define IOMUXC_GPIO_EMC_00_GPIO4_IO00 0x401F8014U, 0x5U, 0, 0, 0x401F8204U

#define IOMUXC_GPIO_EMC_01_SEMC_DATA01 0x401F8018U, 0x0U, 0, 0, 0x401F8208U
#define IOMUXC_GPIO_EMC_01_FLEXPWM4_PWMB00 0x401F8018U, 0x1U, 0, 0, 0x401F8208U
#define IOMUXC_GPIO_EMC_01_LPSPI2_PCS0 0x401F8018U, 0x2U, 0x401F84FCU, 0x1U, 0x401F8208U
#define IOMUXC_GPIO_EMC_01_XBAR1_IN03 0x401F8018U, 0x3U, 0x401F8610U, 0x0U, 0x401F8208U
#define IOMUXC_GPIO_EMC_01_FLEXIO1_FLEXIO01 0x401F8018U, 0x4U, 0, 0, 0x401F8208U
#define IOMUXC_GPIO_EMC_01_GPIO4_IO01 0x401F8018U, 0x5U, 0, 0, 0x401F8208U

#define IOMUXC_GPIO_EMC_02_SEMC_DATA02 0x401F801CU, 0x0U, 0, 0, 0x401F820CU
#define IOMUXC_GPIO_EMC_02_FLEXPWM4_PWMA01 0x401F801CU, 0x1U, 0x401F8498U, 0x0U, 0x401F820CU
#define IOMUXC_GPIO_EMC_02_LPSPI2_SDO 0x401F801CU, 0x2U, 0x401F8508U, 0x1U, 0x401F820CU
#define IOMUXC_GPIO_EMC_02_XBAR1_INOUT04 0x401F801CU, 0x3U, 0x401F8614U, 0x0U, 0x401F820CU
#define IOMUXC_GPIO_EMC_02_FLEXIO1_FLEXIO02 0x401F801CU, 0x4U, 0, 0, 0x401F820CU
#define IOMUXC_GPIO_EMC_02_GPIO4_IO02 0x401F801CU, 0x5U, 0, 0, 0x401F820CU

#define IOMUXC_GPIO_EMC_03_SEMC_DATA03 0x401F8020U, 0x0U, 0, 0, 0x401F8210U
#define IOMUXC_GPIO_EMC_03_FLEXPWM4_PWMB01 0x401F8020U, 0x1U, 0, 0, 0x401F8210U
#define IOMUXC_GPIO_EMC_03_LPSPI2_SDI 0x401F8020U, 0x2U, 0x401F8504U, 0x1U, 0x401F8210U
#define IOMUXC_GPIO_EMC_03_XBAR1_INOUT05 0x401F8020U, 0x3U, 0x401F8618U, 0x0U, 0x401F8210U
#define IOMUXC_GPIO_EMC_03_FLEXIO1_FLEXIO03 0x401F8020U, 0x4U, 0, 0, 0x401F8210U
#define IOMUXC_GPIO_EMC_03_GPIO4_IO03 0x401F8020U, 0x5U, 0, 0, 0x401F8210U

#define IOMUXC_GPIO_EMC_04_SEMC_DATA04 0x401F8024U, 0x0U, 0, 0, 0x401F8214U
#define IOMUXC_GPIO_EMC_04_FLEXPWM4_PWMA02 0x401F8024U, 0x1U, 0x401F849CU, 0x0U, 0x401F8214U
#define IOMUXC_GPIO_EMC_04_SAI2_TX_DATA 0x401F8024U, 0x2U, 0, 0, 0x401F8214U
#define IOMUXC_GPIO_EMC_04_XBAR1_INOUT06 0x401F8024U, 0x3U, 0x401F861CU, 0x0U, 0x401F8214U
#define IOMUXC_GPIO_EMC_04_FLEXIO1_FLEXIO04 0x401F8024U, 0x4U, 0, 0, 0x401F8214U
#define IOMUXC_GPIO_EMC_04_GPIO4_IO04 0x401F8024U, 0x5U, 0, 0, 0x401F8214U

#define IOMUXC_GPIO_EMC_05_SEMC_DATA05 0x401F8028U, 0x0U, 0, 0, 0x401F8218U
#define IOMUXC_GPIO_EMC_05_FLEXPWM4_PWMB02 0x401F8028U, 0x1U, 0, 0, 0x401F8218U
#define IOMUXC_GPIO_EMC_05_SAI2_TX_SYNC 0x401F8028U, 0x2U, 0x401F85C4U, 0x0U, 0x401F8218U
#define IOMUXC_GPIO_EMC_05_XBAR1_INOUT07 0x401F8028U, 0x3U, 0x401F8620U, 0x0U, 0x401F8218U
#define IOMUXC_GPIO_EMC_05_FLEXIO1_FLEXIO05 0x401F8028U, 0x4U, 0, 0, 0x401F8218U
#define IOMUXC_GPIO_EMC_05_GPIO4_IO05 0x401F8028U, 0x5U, 0, 0, 0x401F8218U

#define IOMUXC_GPIO_EMC_06_SEMC_DATA06 0x401F802CU, 0x0U, 0, 0, 0x401F821CU
#define IOMUXC_GPIO_EMC_06_FLEXPWM2_PWMA00 0x401F802CU, 0x1U, 0x401F8478U, 0x0U, 0x401F821CU
#define IOMUXC_GPIO_EMC_06_SAI2_TX_BCLK 0x401F802CU, 0x2U, 0x401F85C0U, 0x0U, 0x401F821CU
#define IOMUXC_GPIO_EMC_06_XBAR1_INOUT08 0x401F802CU, 0x3U, 0x401F8624U, 0x0U, 0x401F821CU
#define IOMUXC_GPIO_EMC_06_FLEXIO1_FLEXIO06 0x401F802CU, 0x4U, 0, 0, 0x401F821CU
#define IOMUXC_GPIO_EMC_06_GPIO4_IO06 0x401F802CU, 0x5U, 0, 0, 0x401F821CU

#define IOMUXC_GPIO_EMC_07_SEMC_DATA07 0x401F8030U, 0x0U, 0, 0, 0x401F8220U
#define IOMUXC_GPIO_EMC_07_FLEXPWM2_PWMB00 0x401F8030U, 0x1U, 0x401F8488U, 0x0U, 0x401F8220U
#define IOMUXC_GPIO_EMC_07_SAI2_MCLK 0x401F8030U, 0x2U, 0x401F85B0U, 0x0U, 0x401F8220U
#define IOMUXC_GPIO_EMC_07_XBAR1_INOUT09 0x401F8030U, 0x3U, 0x401F8628U, 0x0U, 0x401F8220U
#define IOMUXC_GPIO_EMC_07_FLEXIO1_FLEXIO07 0x401F8030U, 0x4U, 0, 0, 0x401F8220U
#define IOMUXC_GPIO_EMC_07_GPIO4_IO07 0x401F8030U, 0x5U, 0, 0, 0x401F8220U

#define IOMUXC_GPIO_EMC_08_SEMC_DM00 0x401F8034U, 0x0U, 0, 0, 0x401F8224U
#define IOMUXC_GPIO_EMC_08_FLEXPWM2_PWMA01 0x401F8034U, 0x1U, 0x401F847CU, 0x0U, 0x401F8224U
#define IOMUXC_GPIO_EMC_08_SAI2_RX_DATA 0x401F8034U, 0x2U, 0x401F85B8U, 0x0U, 0x401F8224U
#define IOMUXC_GPIO_EMC_08_XBAR1_INOUT17 0x401F8034U, 0x3U, 0x401F862CU, 0x0U, 0x401F8224U
#define IOMUXC_GPIO_EMC_08_FLEXIO1_FLEXIO08 0x401F8034U, 0x4U, 0, 0, 0x401F8224U
#define IOMUXC_GPIO_EMC_08_GPIO4_IO08 0x401F8034U, 0x5U, 0, 0, 0x401F8224U

#define IOMUXC_GPIO_EMC_09_SEMC_ADDR00 0x401F8038U, 0x0U, 0, 0, 0x401F8228U
#define IOMUXC_GPIO_EMC_09_FLEXPWM2_PWMB01 0x401F8038U, 0x1U, 0x401F848CU, 0x0U, 0x401F8228U
#define IOMUXC_GPIO_EMC_09_SAI2_RX_SYNC 0x401F8038U, 0x2U, 0x401F85BCU, 0x0U, 0x401F8228U
#define IOMUXC_GPIO_EMC_09_FLEXCAN2_TX 0x401F8038U, 0x3U, 0, 0, 0x401F8228U
#define IOMUXC_GPIO_EMC_09_FLEXIO1_FLEXIO09 0x401F8038U, 0x4U, 0, 0, 0x401F8228U
#define IOMUXC_GPIO_EMC_09_GPIO4_IO09 0x401F8038U, 0x5U, 0, 0, 0x401F8228U

#define IOMUXC_GPIO_EMC_10_SEMC_ADDR01 0x401F803CU, 0x0U, 0, 0, 0x401F822CU
#define IOMUXC_GPIO_EMC_10_FLEXPWM2_PWMA02 0x401F803CU, 0x1U, 0x401F8480U, 0x0U, 0x401F822CU
#define IOMUXC_GPIO_EMC_10_SAI2_RX_BCLK 0x401F803CU, 0x2U, 0x401F85B4U, 0x0U, 0x401F822CU
#define IOMUXC_GPIO_EMC_10_FLEXCAN2_RX 0x401F803CU, 0x3U, 0x401F8450U, 0x0U, 0x401F822CU
#define IOMUXC_GPIO_EMC_10_FLEXIO1_FLEXIO10 0x401F803CU, 0x4U, 0, 0, 0x401F822CU
#define IOMUXC_GPIO_EMC_10_GPIO4_IO10 0x401F803CU, 0x5U, 0, 0, 0x401F822CU

#define IOMUXC_GPIO_EMC_11_SEMC_ADDR02 0x401F8040U, 0x0U, 0, 0, 0x401F8230U
#define IOMUXC_GPIO_EMC_11_FLEXPWM2_PWMB02 0x401F8040U, 0x1U, 0x401F8490U, 0x0U, 0x401F8230U
#define IOMUXC_GPIO_EMC_11_LPI2C4_SDA 0x401F8040U, 0x2U, 0x401F84E8U, 0x0U, 0x401F8230U
#define IOMUXC_GPIO_EMC_11_USDHC2_RESET_B 0x401F8040U, 0x3U, 0, 0, 0x401F8230U
#define IOMUXC_GPIO_EMC_11_FLEXIO1_FLEXIO11 0x401F8040U, 0x4U, 0, 0, 0x401F8230U
#define IOMUXC_GPIO_EMC_11_GPIO4_IO11 0x401F8040U, 0x5U, 0, 0, 0x401F8230U

#define IOMUXC_GPIO_EMC_12_SEMC_ADDR03 0x401F8044U, 0x0U, 0, 0, 0x401F8234U
#define IOMUXC_GPIO_EMC_12_XBAR1_IN24 0x401F8044U, 0x1U, 0x401F8640U, 0x0U, 0x401F8234U
#define IOMUXC_GPIO_EMC_12_LPI2C4_SCL 0x401F8044U, 0x2U, 0x401F84E4U, 0x0U, 0x401F8234U
#define IOMUXC_GPIO_EMC_12_USDHC1_WP 0x401F8044U, 0x3U, 0x401F85D8U, 0x0U, 0x401F8234U
#define IOMUXC_GPIO_EMC_12_FLEXPWM1_PWMA03 0x401F8044U, 0x4U, 0x401F8454U, 0x1U, 0x401F8234U
#define IOMUXC_GPIO_EMC_12_GPIO4_IO12 0x401F8044U, 0x5U, 0, 0, 0x401F8234U

#define IOMUXC_GPIO_EMC_13_SEMC_ADDR04 0x401F8048U, 0x0U, 0, 0, 0x401F8238U
#define IOMUXC_GPIO_EMC_13_XBAR1_IN25 0x401F8048U, 0x1U, 0x401F8650U, 0x1U, 0x401F8238U
#define IOMUXC_GPIO_EMC_13_LPUART3_TX 0x401F8048U, 0x2U, 0x401F853CU, 0x1U, 0x401F8238U
#define IOMUXC_GPIO_EMC_13_MQS_RIGHT 0x401F8048U, 0x3U, 0, 0, 0x401F8238U
#define IOMUXC_GPIO_EMC_13_FLEXPWM1_PWMB03 0x401F8048U, 0x4U, 0x401F8464U, 0x1U, 0x401F8238U
#define IOMUXC_GPIO_EMC_13_GPIO4_IO13 0x401F8048U, 0x5U, 0, 0, 0x401F8238U

#define IOMUXC_GPIO_EMC_14_SEMC_ADDR05 0x401F804CU, 0x0U, 0, 0, 0x401F823CU
#define IOMUXC_GPIO_EMC_14_XBAR1_INOUT19 0x401F804CU, 0x1U, 0x401F8654U, 0x0U, 0x401F823CU
#define IOMUXC_GPIO_EMC_14_LPUART3_RX 0x401F804CU, 0x2U, 0x401F8538U, 0x1U, 0x401F823CU
#define IOMUXC_GPIO_EMC_14_MQS_LEFT 0x401F804CU, 0x3U, 0, 0, 0x401F823CU
#define IOMUXC_GPIO_EMC_14_LPSPI2_PCS1 0x401F804CU, 0x4U, 0, 0, 0x401F823CU
#define IOMUXC_GPIO_EMC_14_GPIO4_IO14 0x401F804CU, 0x5U, 0, 0, 0x401F823CU

#define IOMUXC_GPIO_EMC_15_SEMC_ADDR06 0x401F8050U, 0x0U, 0, 0, 0x401F8240U
#define IOMUXC_GPIO_EMC_15_XBAR1_IN20 0x401F8050U, 0x1U, 0x401F8634U, 0x0U, 0x401F8240U
#define IOMUXC_GPIO_EMC_15_LPUART3_CTS_B 0x401F8050U, 0x2U, 0x401F8534U, 0x0U, 0x401F8240U
#define IOMUXC_GPIO_EMC_15_SPDIF_OUT 0x401F8050U, 0x3U, 0, 0, 0x401F8240U
#define IOMUXC_GPIO_EMC_15_QTIMER3_TIMER0 0x401F8050U, 0x4U, 0x401F857CU, 0x0U, 0x401F8240U
#define IOMUXC_GPIO_EMC_15_GPIO4_IO15 0x401F8050U, 0x5U, 0, 0, 0x401F8240U

#define IOMUXC_GPIO_EMC_16_SEMC_ADDR07 0x401F8054U, 0x0U, 0, 0, 0x401F8244U
#define IOMUXC_GPIO_EMC_16_XBAR1_IN21 0x401F8054U, 0x1U, 0x401F8658U, 0x0U, 0x401F8244U
#define IOMUXC_GPIO_EMC_16_LPUART3_RTS_B 0x401F8054U, 0x2U, 0, 0, 0x401F8244U
#define IOMUXC_GPIO_EMC_16_SPDIF_IN 0x401F8054U, 0x3U, 0x401F85C8U, 0x1U, 0x401F8244U
#define IOMUXC_GPIO_EMC_16_QTIMER3_TIMER1 0x401F8054U, 0x4U, 0x401F8580U, 0x1U, 0x401F8244U
#define IOMUXC_GPIO_EMC_16_GPIO4_IO16 0x401F8054U, 0x5U, 0, 0, 0x401F8244U

#define IOMUXC_GPIO_EMC_17_SEMC_ADDR08 0x401F8058U, 0x0U, 0, 0, 0x401F8248U
#define IOMUXC_GPIO_EMC_17_FLEXPWM4_PWMA03 0x401F8058U, 0x1U, 0x401F84A0U, 0x0U, 0x401F8248U
#define IOMUXC_GPIO_EMC_17_LPUART4_CTS_B 0x401F8058U, 0x2U, 0, 0, 0x401F8248U
#define IOMUXC_GPIO_EMC_17_FLEXCAN1_TX 0x401F8058U, 0x3U, 0, 0, 0x401F8248U
#define IOMUXC_GPIO_EMC_17_QTIMER3_TIMER2 0x401F8058U, 0x4U, 0x401F8584U, 0x0U, 0x401F8248U
#define IOMUXC_GPIO_EMC_17_GPIO4_IO17 0x401F8058U, 0x5U, 0, 0, 0x401F8248U

#define IOMUXC_GPIO_EMC_18_SEMC_ADDR09 0x401F805CU, 0x0U, 0, 0, 0x401F824CU
#define IOMUXC_GPIO_EMC_18_FLEXPWM4_PWMB03 0x401F805CU, 0x1U, 0, 0, 0x401F824CU
#define IOMUXC_GPIO_EMC_18_LPUART4_RTS_B 0x401F805CU, 0x2U, 0, 0, 0x401F824CU
#define IOMUXC_GPIO_EMC_18_FLEXCAN1_RX 0x401F805CU, 0x3U, 0x401F844CU, 0x1U, 0x401F824CU
#define IOMUXC_GPIO_EMC_18_QTIMER3_TIMER3 0x401F805CU, 0x4U, 0x401F8588U, 0x0U, 0x401F824CU
#define IOMUXC_GPIO_EMC_18_GPIO4_IO18 0x401F805CU, 0x5U, 0, 0, 0x401F824CU
#define IOMUXC_GPIO_EMC_18_SNVS_VIO_5_CTL 0x401F805CU, 0x6U, 0, 0, 0x401F824CU

#define IOMUXC_GPIO_EMC_19_SEMC_ADDR11 0x401F8060U, 0x0U, 0, 0, 0x401F8250U
#define IOMUXC_GPIO_EMC_19_FLEXPWM2_PWMA03 0x401F8060U, 0x1U, 0x401F8474U, 0x1U, 0x401F8250U
#define IOMUXC_GPIO_EMC_19_LPUART4_TX 0x401F8060U, 0x2U, 0x401F8544U, 0x1U, 0x401F8250U
#define IOMUXC_GPIO_EMC_19_ENET_RDATA01 0x401F8060U, 0x3U, 0x401F8438U, 0x0U, 0x401F8250U
#define IOMUXC_GPIO_EMC_19_QTIMER2_TIMER0 0x401F8060U, 0x4U, 0x401F856CU, 0x0U, 0x401F8250U
#define IOMUXC_GPIO_EMC_19_GPIO4_IO19 0x401F8060U, 0x5U, 0, 0, 0x401F8250U
#define IOMUXC_GPIO_EMC_19_SNVS_VIO_5 0x401F8060U, 0x6U, 0, 0, 0x401F8250U

#define IOMUXC_GPIO_EMC_20_SEMC_ADDR12 0x401F8064U, 0x0U, 0, 0, 0x401F8254U
#define IOMUXC_GPIO_EMC_20_FLEXPWM2_PWMB03 0x401F8064U, 0x1U, 0x401F8484U, 0x1U, 0x401F8254U
#define IOMUXC_GPIO_EMC_20_LPUART4_RX 0x401F8064U, 0x2U, 0x401F8540U, 0x1U, 0x401F8254U
#define IOMUXC_GPIO_EMC_20_ENET_RDATA00 0x401F8064U, 0x3U, 0x401F8434U, 0x0U, 0x401F8254U
#define IOMUXC_GPIO_EMC_20_QTIMER2_TIMER1 0x401F8064U, 0x4U, 0x401F8570U, 0x0U, 0x401F8254U
#define IOMUXC_GPIO_EMC_20_GPIO4_IO20 0x401F8064U, 0x5U, 0, 0, 0x401F8254U

#define IOMUXC_GPIO_EMC_21_SEMC_BA0 0x401F8068U, 0x0U, 0, 0, 0x401F8258U
#define IOMUXC_GPIO_EMC_21_FLEXPWM3_PWMA03 0x401F8068U, 0x1U, 0, 0, 0x401F8258U
#define IOMUXC_GPIO_EMC_21_LPI2C3_SDA 0x401F8068U, 0x2U, 0x401F84E0U, 0x0U, 0x401F8258U
#define IOMUXC_GPIO_EMC_21_ENET_TDATA01 0x401F8068U, 0x3U, 0, 0, 0x401F8258U
#define IOMUXC_GPIO_EMC_21_QTIMER2_TIMER2 0x401F8068U, 0x4U, 0x401F8574U, 0x0U, 0x401F8258U
#define IOMUXC_GPIO_EMC_21_GPIO4_IO21 0x401F8068U, 0x5U, 0, 0, 0x401F8258U

#define IOMUXC_GPIO_EMC_22_SEMC_BA1 0x401F806CU, 0x0U, 0, 0, 0x401F825CU
#define IOMUXC_GPIO_EMC_22_FLEXPWM3_PWMB03 0x401F806CU, 0x1U, 0, 0, 0x401F825CU
#define IOMUXC_GPIO_EMC_22_LPI2C3_SCL 0x401F806CU, 0x2U, 0x401F84DCU, 0x0U, 0x401F825CU
#define IOMUXC_GPIO_EMC_22_ENET_TDATA00 0x401F806CU, 0x3U, 0, 0, 0x401F825CU
#define IOMUXC_GPIO_EMC_22_QTIMER2_TIMER3 0x401F806CU, 0x4U, 0x401F8578U, 0x0U, 0x401F825CU
#define IOMUXC_GPIO_EMC_22_GPIO4_IO22 0x401F806CU, 0x5U, 0, 0, 0x401F825CU

#define IOMUXC_GPIO_EMC_23_SEMC_ADDR10 0x401F8070U, 0x0U, 0, 0, 0x401F8260U
#define IOMUXC_GPIO_EMC_23_FLEXPWM1_PWMA00 0x401F8070U, 0x1U, 0x401F8458U, 0x0U, 0x401F8260U
#define IOMUXC_GPIO_EMC_23_LPUART5_TX 0x401F8070U, 0x2U, 0x401F854CU, 0x0U, 0x401F8260U
#define IOMUXC_GPIO_EMC_23_ENET_RX_EN 0x401F8070U, 0x3U, 0x401F843CU, 0x0U, 0x401F8260U
#define IOMUXC_GPIO_EMC_23_GPT1_CAPTURE2 0x401F8070U, 0x4U, 0x401F875CU, 0x0U, 0x401F8260U
#define IOMUXC_GPIO_EMC_23_GPIO4_IO23 0x401F8070U, 0x5U, 0, 0, 0x401F8260U

#define IOMUXC_GPIO_EMC_24_SEMC_CAS 0x401F8074U, 0x0U, 0, 0, 0x401F8264U
#define IOMUXC_GPIO_EMC_24_FLEXPWM1_PWMB00 0x401F8074U, 0x1U, 0x401F8468U, 0x0U, 0x401F8264U
#define IOMUXC_GPIO_EMC_24_LPUART5_RX 0x401F8074U, 0x2U, 0x401F8548U, 0x0U, 0x401F8264U
#define IOMUXC_GPIO_EMC_24_ENET_TX_EN 0x401F8074U, 0x3U, 0, 0, 0x401F8264U
#define IOMUXC_GPIO_EMC_24_GPT1_CAPTURE1 0x401F8074U, 0x4U, 0x401F8758U, 0x0U, 0x401F8264U
#define IOMUXC_GPIO_EMC_24_GPIO4_IO24 0x401F8074U, 0x5U, 0, 0, 0x401F8264U

#define IOMUXC_GPIO_EMC_25_SEMC_RAS 0x401F8078U, 0x0U, 0, 0, 0x401F8268U
#define IOMUXC_GPIO_EMC_25_FLEXPWM1_PWMA01 0x401F8078U, 0x1U, 0x401F845CU, 0x0U, 0x401F8268U
#define IOMUXC_GPIO_EMC_25_LPUART6_TX 0x401F8078U, 0x2U, 0x401F8554U, 0x0U, 0x401F8268U
#define IOMUXC_GPIO_EMC_25_ENET_TX_CLK 0x401F8078U, 0x3U, 0x401F8448U, 0x0U, 0x401F8268U
#define IOMUXC_GPIO_EMC_25_ENET_REF_CLK 0x401F8078U, 0x4U, 0x401F842CU, 0x0U, 0x401F8268U
#define IOMUXC_GPIO_EMC_25_GPIO4_IO25 0x401F8078U, 0x5U, 0, 0, 0x401F8268U

#define IOMUXC_GPIO_EMC_26_SEMC_CLK 0x401F807CU, 0x0U, 0, 0, 0x401F826CU
#define IOMUXC_GPIO_EMC_26_FLEXPWM1_PWMB01 0x401F807CU, 0x1U, 0x401F846CU, 0x0U, 0x401F826CU
#define IOMUXC_GPIO_EMC_26_LPUART6_RX 0x401F807CU, 0x2U, 0x401F8550U, 0x0U, 0x401F826CU
#define IOMUXC_GPIO_EMC_26_ENET_RX_ER 0x401F807CU, 0x3U, 0x401F8440U, 0x0U, 0x401F826CU
#define IOMUXC_GPIO_EMC_26_FLEXIO1_FLEXIO12 0x401F807CU, 0x4U, 0, 0, 0x401F826CU
#define IOMUXC_GPIO_EMC_26_GPIO4_IO26 0x401F807CU, 0x5U, 0, 0, 0x401F826CU

#define IOMUXC_GPIO_EMC_27_SEMC_CKE 0x401F8080U, 0x0U, 0, 0, 0x401F8270U
#define IOMUXC_GPIO_EMC_27_FLEXPWM1_PWMA02 0x401F8080U, 0x1U, 0x401F8460U, 0x0U, 0x401F8270U
#define IOMUXC_GPIO_EMC_27_LPUART5_RTS_B 0x401F8080U, 0x2U, 0, 0, 0x401F8270U
#define IOMUXC_GPIO_EMC_27_LPSPI1_SCK 0x401F8080U, 0x3U, 0x401F84F0U, 0x0U, 0x401F8270U
#define IOMUXC_GPIO_EMC_27_FLEXIO1_FLEXIO13 0x401F8080U, 0x4U, 0, 0, 0x401F8270U
#define IOMUXC_GPIO_EMC_27_GPIO4_IO27 0x401F8080U, 0x5U, 0, 0, 0x401F8270U

#define IOMUXC_GPIO_EMC_28_SEMC_WE 0x401F8084U, 0x0U, 0, 0, 0x401F8274U
#define IOMUXC_GPIO_EMC_28_FLEXPWM1_PWMB02 0x401F8084U, 0x1U, 0x401F8470U, 0x0U, 0x401F8274U
#define IOMUXC_GPIO_EMC_28_LPUART5_CTS_B 0x401F8084U, 0x2U, 0, 0, 0x401F8274U
#define IOMUXC_GPIO_EMC_28_LPSPI1_SDO 0x401F8084U, 0x3U, 0x401F84F8U, 0x0U, 0x401F8274U
#define IOMUXC_GPIO_EMC_28_FLEXIO1_FLEXIO14 0x401F8084U, 0x4U, 0, 0, 0x401F8274U
#define IOMUXC_GPIO_EMC_28_GPIO4_IO28 0x401F8084U, 0x5U, 0, 0, 0x401F8274U

#define IOMUXC_GPIO_EMC_29_SEMC_CS0 0x401F8088U, 0x0U, 0, 0, 0x401F8278U
#define IOMUXC_GPIO_EMC_29_FLEXPWM3_PWMA00 0x401F8088U, 0x1U, 0, 0, 0x401F8278U
#define IOMUXC_GPIO_EMC_29_LPUART6_RTS_B 0x401F8088U, 0x2U, 0, 0, 0x401F8278U
#define IOMUXC_GPIO_EMC_29_LPSPI1_SDI 0x401F8088U, 0x3U, 0x401F84F4U, 0x0U, 0x401F8278U
#define IOMUXC_GPIO_EMC_29_FLEXIO1_FLEXIO15 0x401F8088U, 0x4U, 0, 0, 0x401F8278U
#define IOMUXC_GPIO_EMC_29_GPIO4_IO29 0x401F8088U, 0x5U, 0, 0, 0x401F8278U

#define IOMUXC_GPIO_EMC_30_SEMC_DATA08 0x401F808CU, 0x0U, 0, 0, 0x401F827CU
#define IOMUXC_GPIO_EMC_30_FLEXPWM3_PWMB00 0x401F808CU, 0x1U, 0, 0, 0x401F827CU
#define IOMUXC_GPIO_EMC_30_LPUART6_CTS_B 0x401F808CU, 0x2U, 0, 0, 0x401F827CU
#define IOMUXC_GPIO_EMC_30_LPSPI1_PCS0 0x401F808CU, 0x3U, 0x401F84ECU, 0x1U, 0x401F827CU
#define IOMUXC_GPIO_EMC_30_CSI_DATA23 0x401F808CU, 0x4U, 0, 0, 0x401F827CU
#define IOMUXC_GPIO_EMC_30_GPIO4_IO30 0x401F808CU, 0x5U, 0, 0, 0x401F827CU
#define IOMUXC_GPIO_EMC_30_ENET2_TDATA00 0x401F808CU, 0x8U, 0, 0, 0x401F827CU

#define IOMUXC_GPIO_EMC_31_SEMC_DATA09 0x401F8090U, 0x0U, 0, 0, 0x401F8280U
#define IOMUXC_GPIO_EMC_31_FLEXPWM3_PWMA01 0x401F8090U, 0x1U, 0, 0, 0x401F8280U
#define IOMUXC_GPIO_EMC_31_LPUART7_TX 0x401F8090U, 0x2U, 0x401F855CU, 0x1U, 0x401F8280U
#define IOMUXC_GPIO_EMC_31_LPSPI1_PCS1 0x401F8090U, 0x3U, 0, 0, 0x401F8280U
#define IOMUXC_GPIO_EMC_31_CSI_DATA22 0x401F8090U, 0x4U, 0, 0, 0x401F8280U
#define IOMUXC_GPIO_EMC_31_GPIO4_IO31 0x401F8090U, 0x5U, 0, 0, 0x401F8280U
#define IOMUXC_GPIO_EMC_31_ENET2_TDATA01 0x401F8090U, 0x8U, 0, 0, 0x401F8280U

#define IOMUXC_GPIO_EMC_32_SEMC_DATA10 0x401F8094U, 0x0U, 0, 0, 0x401F8284U
#define IOMUXC_GPIO_EMC_32_FLEXPWM3_PWMB01 0x401F8094U, 0x1U, 0, 0, 0x401F8284U
#define IOMUXC_GPIO_EMC_32_LPUART7_RX 0x401F8094U, 0x2U, 0x401F8558U, 0x1U, 0x401F8284U
#define IOMUXC_GPIO_EMC_32_CCM_PMIC_RDY 0x401F8094U, 0x3U, 0x401F83FCU, 0x4U, 0x401F8284U
#define IOMUXC_GPIO_EMC_32_CSI_DATA21 0x401F8094U, 0x4U, 0, 0, 0x401F8284U
#define IOMUXC_GPIO_EMC_32_GPIO3_IO18 0x401F8094U, 0x5U, 0, 0, 0x401F8284U
#define IOMUXC_GPIO_EMC_32_ENET2_TX_EN 0x401F8094U, 0x8U, 0, 0, 0x401F8284U

#define IOMUXC_GPIO_EMC_33_SEMC_DATA11 0x401F8098U, 0x0U, 0, 0, 0x401F8288U
#define IOMUXC_GPIO_EMC_33_FLEXPWM3_PWMA02 0x401F8098U, 0x1U, 0, 0, 0x401F8288U
#define IOMUXC_GPIO_EMC_33_USDHC1_RESET_B 0x401F8098U, 0x2U, 0, 0, 0x401F8288U
#define IOMUXC_GPIO_EMC_33_SAI3_RX_DATA 0x401F8098U, 0x3U, 0x401F8778U, 0x0U, 0x401F8288U
#define IOMUXC_GPIO_EMC_33_CSI_DATA20 0x401F8098U, 0x4U, 0, 0, 0x401F8288U
#define IOMUXC_GPIO_EMC_33_GPIO3_IO19 0x401F8098U, 0x5U, 0, 0, 0x401F8288U
#define IOMUXC_GPIO_EMC_33_ENET2_TX_CLK 0x401F8098U, 0x8U, 0x401F8728U, 0x0U, 0x401F8288U
#define IOMUXC_GPIO_EMC_33_ENET2_REF_CLK2 0x401F8098U, 0x9U, 0x401F870CU, 0x0U, 0x401F8288U

#define IOMUXC_GPIO_EMC_34_SEMC_DATA12 0x401F809CU, 0x0U, 0, 0, 0x401F828CU
#define IOMUXC_GPIO_EMC_34_FLEXPWM3_PWMB02 0x401F809CU, 0x1U, 0, 0, 0x401F828CU
#define IOMUXC_GPIO_EMC_34_USDHC1_VSELECT 0x401F809CU, 0x2U, 0, 0, 0x401F828CU
#define IOMUXC_GPIO_EMC_34_SAI3_RX_SYNC 0x401F809CU, 0x3U, 0x401F877CU, 0x0U, 0x401F828CU
#define IOMUXC_GPIO_EMC_34_CSI_DATA19 0x401F809CU, 0x4U, 0, 0, 0x401F828CU
#define IOMUXC_GPIO_EMC_34_GPIO3_IO20 0x401F809CU, 0x5U, 0, 0, 0x401F828CU
#define IOMUXC_GPIO_EMC_34_ENET2_RX_ER 0x401F809CU, 0x8U, 0x401F8720U, 0x0U, 0x401F828CU

#define IOMUXC_GPIO_EMC_35_SEMC_DATA13 0x401F80A0U, 0x0U, 0, 0, 0x401F8290U
#define IOMUXC_GPIO_EMC_35_XBAR1_INOUT18 0x401F80A0U, 0x1U, 0x401F8630U, 0x0U, 0x401F8290U
#define IOMUXC_GPIO_EMC_35_GPT1_COMPARE1 0x401F80A0U, 0x2U, 0, 0, 0x401F8290U
#define IOMUXC_GPIO_EMC_35_SAI3_RX_BCLK 0x401F80A0U, 0x3U, 0x401F8774U, 0x0U, 0x401F8290U
#define IOMUXC_GPIO_EMC_35_CSI_DATA18 0x401F80A0U, 0x4U, 0, 0, 0x401F8290U
#define IOMUXC_GPIO_EMC_35_GPIO3_IO21 0x401F80A0U, 0x5U, 0, 0, 0x401F8290U
#define IOMUXC_GPIO_EMC_35_USDHC1_CD_B 0x401F80A0U, 0x6U, 0x401F85D4U, 0x0U, 0x401F8290U
#define IOMUXC_GPIO_EMC_35_ENET2_RDATA00 0x401F80A0U, 0x8U, 0x401F8714U, 0x0U, 0x401F8290U

#define IOMUXC_GPIO_EMC_36_SEMC_DATA14 0x401F80A4U, 0x0U, 0, 0, 0x401F8294U
#define IOMUXC_GPIO_EMC_36_XBAR1_IN22 0x401F80A4U, 0x1U, 0x401F8638U, 0x0U, 0x401F8294U
#define IOMUXC_GPIO_EMC_36_GPT1_COMPARE2 0x401F80A4U, 0x2U, 0, 0, 0x401F8294U
#define IOMUXC_GPIO_EMC_36_SAI3_TX_DATA 0x401F80A4U, 0x3U, 0, 0, 0x401F8294U
#define IOMUXC_GPIO_EMC_36_CSI_DATA17 0x401F80A4U, 0x4U, 0, 0, 0x401F8294U
#define IOMUXC_GPIO_EMC_36_GPIO3_IO22 0x401F80A4U, 0x5U, 0, 0, 0x401F8294U
#define IOMUXC_GPIO_EMC_36_USDHC1_WP 0x401F80A4U, 0x6U, 0x401F85D8U, 0x1U, 0x401F8294U
#define IOMUXC_GPIO_EMC_36_ENET2_RDATA01 0x401F80A4U, 0x8U, 0x401F8718U, 0x0U, 0x401F8294U
#define IOMUXC_GPIO_EMC_36_FLEXCAN3_TX 0x401F80A4U, 0x9U, 0, 0, 0x401F8294U

#define IOMUXC_GPIO_EMC_37_SEMC_DATA15 0x401F80A8U, 0x0U, 0, 0, 0x401F8298U
#define IOMUXC_GPIO_EMC_37_XBAR1_IN23 0x401F80A8U, 0x1U, 0x401F863CU, 0x0U, 0x401F8298U
#define IOMUXC_GPIO_EMC_37_GPT1_COMPARE3 0x401F80A8U, 0x2U, 0, 0, 0x401F8298U
#define IOMUXC_GPIO_EMC_37_SAI3_MCLK 0x401F80A8U, 0x3U, 0x401F8770U, 0x0U, 0x401F8298U
#define IOMUXC_GPIO_EMC_37_CSI_DATA16 0x401F80A8U, 0x4U, 0, 0, 0x401F8298U
#define IOMUXC_GPIO_EMC_37_GPIO3_IO23 0x401F80A8U, 0x5U, 0, 0, 0x401F8298U
#define IOMUXC_GPIO_EMC_37_USDHC2_WP 0x401F80A8U, 0x6U, 0x401F8608U, 0x0U, 0x401F8298U
#define IOMUXC_GPIO_EMC_37_ENET2_RX_EN 0x401F80A8U, 0x8U, 0x401F871CU, 0x0U, 0x401F8298U
#define IOMUXC_GPIO_EMC_37_FLEXCAN3_RX 0x401F80A8U, 0x9U, 0x401F878CU, 0x0U, 0x401F8298U

#define IOMUXC_GPIO_EMC_38_SEMC_DM01 0x401F80ACU, 0x0U, 0, 0, 0x401F829CU
#define IOMUXC_GPIO_EMC_38_FLEXPWM1_PWMA03 0x401F80ACU, 0x1U, 0x401F8454U, 0x2U, 0x401F829CU
#define IOMUXC_GPIO_EMC_38_LPUART8_TX 0x401F80ACU, 0x2U, 0x401F8564U, 0x2U, 0x401F829CU
#define IOMUXC_GPIO_EMC_38_SAI3_TX_BCLK 0x401F80ACU, 0x3U, 0x401F8780U, 0x0U, 0x401F829CU
#define IOMUXC_GPIO_EMC_38_CSI_FIELD 0x401F80ACU, 0x4U, 0, 0, 0x401F829CU
#define IOMUXC_GPIO_EMC_38_GPIO3_IO24 0x401F80ACU, 0x5U, 0, 0, 0x401F829CU
#define IOMUXC_GPIO_EMC_38_USDHC2_VSELECT 0x401F80ACU, 0x6U, 0, 0, 0x401F829CU
#define IOMUXC_GPIO_EMC_38_ENET2_MDC 0x401F80ACU, 0x8U, 0, 0, 0x401F829CU

#define IOMUXC_GPIO_EMC_39_SEMC_DQS 0x401F80B0U, 0x0U, 0, 0, 0x401F82A0U
#define IOMUXC_GPIO_EMC_39_FLEXPWM1_PWMB03 0x401F80B0U, 0x1U, 0x401F8464U, 0x2U, 0x401F82A0U
#define IOMUXC_GPIO_EMC_39_LPUART8_RX 0x401F80B0U, 0x2U, 0x401F8560U, 0x2U, 0x401F82A0U
#define IOMUXC_GPIO_EMC_39_SAI3_TX_SYNC 0x401F80B0U, 0x3U, 0x401F8784U, 0x0U, 0x401F82A0U
#define IOMUXC_GPIO_EMC_39_WDOG1_WDOG_B 0x401F80B0U, 0x4U, 0, 0, 0x401F82A0U
#define IOMUXC_GPIO_EMC_39_GPIO3_IO25 0x401F80B0U, 0x5U, 0, 0, 0x401F82A0U
#define IOMUXC_GPIO_EMC_39_USDHC2_CD_B 0x401F80B0U, 0x6U, 0x401F85E0U, 0x1U, 0x401F82A0U
#define IOMUXC_GPIO_EMC_39_ENET2_MDIO 0x401F80B0U, 0x8U, 0x401F8710U, 0x0U, 0x401F82A0U
#define IOMUXC_GPIO_EMC_39_SEMC_DQS4 0x401F80B0U, 0x9U, 0x401F8788U, 0x1U, 0x401F82A0U

#define IOMUXC_GPIO_EMC_40_SEMC_RDY 0x401F80B4U, 0x0U, 0, 0, 0x401F82A4U
#define IOMUXC_GPIO_EMC_40_GPT2_CAPTURE2 0x401F80B4U, 0x1U, 0x401F8768U, 0x0U, 0x401F82A4U
#define IOMUXC_GPIO_EMC_40_LPSPI1_PCS2 0x401F80B4U, 0x2U, 0, 0, 0x401F82A4U
#define IOMUXC_GPIO_EMC_40_USB_OTG2_OC 0x401F80B4U, 0x3U, 0x401F85CCU, 0x1U, 0x401F82A4U
#define IOMUXC_GPIO_EMC_40_ENET_MDC 0x401F80B4U, 0x4U, 0, 0, 0x401F82A4U
#define IOMUXC_GPIO_EMC_40_GPIO3_IO26 0x401F80B4U, 0x5U, 0, 0, 0x401F82A4U
#define IOMUXC_GPIO_EMC_40_USDHC2_RESET_B 0x401F80B4U, 0x6U, 0, 0, 0x401F82A4U
#define IOMUXC_GPIO_EMC_40_SEMC_CLK5 0x401F80B4U, 0x9U, 0, 0, 0x401F82A4U

#define IOMUXC_GPIO_EMC_41_SEMC_CSX00 0x401F80B8U, 0x0U, 0, 0, 0x401F82A8U
#define IOMUXC_GPIO_EMC_41_GPT2_CAPTURE1 0x401F80B8U, 0x1U, 0x401F8764U, 0x0U, 0x401F82A8U
#define IOMUXC_GPIO_EMC_41_LPSPI1_PCS3 0x401F80B8U, 0x2U, 0, 0, 0x401F82A8U
#define IOMUXC_GPIO_EMC_41_USB_OTG2_PWR 0x401F80B8U, 0x3U, 0, 0, 0x401F82A8U
#define IOMUXC_GPIO_EMC_41_ENET_MDIO 0x401F80B8U, 0x4U, 0x401F8430U, 0x1U, 0x401F82A8U
#define IOMUXC_GPIO_EMC_41_GPIO3_IO27 0x401F80B8U, 0x5U, 0, 0, 0x401F82A8U
#define IOMUXC_GPIO_EMC_41_USDHC1_VSELECT 0x401F80B8U, 0x6U, 0, 0, 0x401F82A8U

#define IOMUXC_GPIO_AD_B0_00_FLEXPWM2_PWMA03 0x401F80BCU, 0x0U, 0x401F8474U, 0x2U, 0x401F82ACU
#define IOMUXC_GPIO_AD_B0_00_XBAR1_INOUT14 0x401F80BCU, 0x1U, 0x401F8644U, 0x0U, 0x401F82ACU
#define IOMUXC_GPIO_AD_B0_00_REF_CLK_32K 0x401F80BCU, 0x2U, 0, 0, 0x401F82ACU
#define IOMUXC_GPIO_AD_B0_00_USB_OTG2_ID 0x401F80BCU, 0x3U, 0x401F83F8U, 0x0U, 0x401F82ACU
#define IOMUXC_GPIO_AD_B0_00_LPI2C1_SCLS 0x401F80BCU, 0x4U, 0, 0, 0x401F82ACU
#define IOMUXC_GPIO_AD_B0_00_GPIO1_IO00 0x401F80BCU, 0x5U, 0, 0, 0x401F82ACU
#define IOMUXC_GPIO_AD_B0_00_USDHC1_RESET_B 0x401F80BCU, 0x6U, 0, 0, 0x401F82ACU
#define IOMUXC_GPIO_AD_B0_00_LPSPI3_SCK 0x401F80BCU, 0x7U, 0x401F8510U, 0x0U, 0x401F82ACU

#define IOMUXC_GPIO_AD_B0_01_FLEXPWM2_PWMB03 0x401F80C0U, 0x0U, 0x401F8484U, 0x2U, 0x401F82B0U
#define IOMUXC_GPIO_AD_B0_01_XBAR1_INOUT15 0x401F80C0U, 0x1U, 0x401F8648U, 0x0U, 0x401F82B0U
#define IOMUXC_GPIO_AD_B0_01_REF_CLK_24M 0x401F80C0U, 0x2U, 0, 0, 0x401F82B0U
#define IOMUXC_GPIO_AD_B0_01_USB_OTG1_ID 0x401F80C0U, 0x3U, 0x401F83F4U, 0x0U, 0x401F82B0U
#define IOMUXC_GPIO_AD_B0_01_LPI2C1_SDAS 0x401F80C0U, 0x4U, 0, 0, 0x401F82B0U
#define IOMUXC_GPIO_AD_B0_01_GPIO1_IO01 0x401F80C0U, 0x5U, 0, 0, 0x401F82B0U
#define IOMUXC_GPIO_AD_B0_01_EWM_OUT_B 0x401F80C0U, 0x6U, 0, 0, 0x401F82B0U
#define IOMUXC_GPIO_AD_B0_01_LPSPI3_SDO 0x401F80C0U, 0x7U, 0x401F8518U, 0x0U, 0x401F82B0U

#define IOMUXC_GPIO_AD_B0_02_FLEXCAN2_TX 0x401F80C4U, 0x0U, 0, 0, 0x401F82B4U
#define IOMUXC_GPIO_AD_B0_02_XBAR1_INOUT16 0x401F80C4U, 0x1U, 0x401F864CU, 0x0U, 0x401F82B4U
#define IOMUXC_GPIO_AD_B0_02_LPUART6_TX 0x401F80C4U, 0x2U, 0x401F8554U, 0x1U, 0x401F82B4U
#define IOMUXC_GPIO_AD_B0_02_USB_OTG1_PWR 0x401F80C4U, 0x3U, 0, 0, 0x401F82B4U
#define IOMUXC_GPIO_AD_B0_02_FLEXPWM1_PWMX00 0x401F80C4U, 0x4U, 0, 0, 0x401F82B4U
#define IOMUXC_GPIO_AD_B0_02_GPIO1_IO02 0x401F80C4U, 0x5U, 0, 0, 0x401F82B4U
#define IOMUXC_GPIO_AD_B0_02_LPI2C1_HREQ 0x401F80C4U, 0x6U, 0, 0, 0x401F82B4U
#define IOMUXC_GPIO_AD_B0_02_LPSPI3_SDI 0x401F80C4U, 0x7U, 0x401F8514U, 0x0U, 0x401F82B4U

#define IOMUXC_GPIO_AD_B0_03_FLEXCAN2_RX 0x401F80C8U, 0x0U, 0x401F8450U, 0x1U, 0x401F82B8U
#define IOMUXC_GPIO_AD_B0_03_XBAR1_INOUT17 0x401F80C8U, 0x1U, 0x401F862CU, 0x1U, 0x401F82B8U
#define IOMUXC_GPIO_AD_B0_03_LPUART6_RX 0x401F80C8U, 0x2U, 0x401F8550U, 0x1U, 0x401F82B8U
#define IOMUXC_GPIO_AD_B0_03_USB_OTG1_OC 0x401F80C8U, 0x3U, 0x401F85D0U, 0x0U, 0x401F82B8U
#define IOMUXC_GPIO_AD_B0_03_FLEXPWM1_PWMX01 0x401F80C8U, 0x4U, 0, 0, 0x401F82B8U
#define IOMUXC_GPIO_AD_B0_03_GPIO1_IO03 0x401F80C8U, 0x5U, 0, 0, 0x401F82B8U
#define IOMUXC_GPIO_AD_B0_03_REF_CLK_24M 0x401F80C8U, 0x6U, 0, 0, 0x401F82B8U
#define IOMUXC_GPIO_AD_B0_03_LPSPI3_PCS0 0x401F80C8U, 0x7U, 0x401F850CU, 0x0U, 0x401F82B8U

#define IOMUXC_GPIO_AD_B0_04_SRC_BOOT_MODE00 0x401F80CCU, 0x0U, 0, 0, 0x401F82BCU
#define IOMUXC_GPIO_AD_B0_04_MQS_RIGHT 0x401F80CCU, 0x1U, 0, 0, 0x401F82BCU
#define IOMUXC_GPIO_AD_B0_04_ENET_TX_DATA03 0x401F80CCU, 0x2U, 0, 0, 0x401F82BCU
#define IOMUXC_GPIO_AD_B0_04_SAI2_TX_SYNC 0x401F80CCU, 0x3U, 0x401F85C4U, 0x1U, 0x401F82BCU
#define IOMUXC_GPIO_AD_B0_04_CSI_DATA09 0x401F80CCU, 0x4U, 0x401F841CU, 0x1U, 0x401F82BCU
#define IOMUXC_GPIO_AD_B0_04_GPIO1_IO04 0x401F80CCU, 0x5U, 0, 0, 0x401F82BCU
#define IOMUXC_GPIO_AD_B0_04_PIT_TRIGGER00 0x401F80CCU, 0x6U, 0, 0, 0x401F82BCU
#define IOMUXC_GPIO_AD_B0_04_LPSPI3_PCS1 0x401F80CCU, 0x7U, 0, 0, 0x401F82BCU

#define IOMUXC_GPIO_AD_B0_05_SRC_BOOT_MODE01 0x401F80D0U, 0x0U, 0, 0, 0x401F82C0U
#define IOMUXC_GPIO_AD_B0_05_MQS_LEFT 0x401F80D0U, 0x1U, 0, 0, 0x401F82C0U
#define IOMUXC_GPIO_AD_B0_05_ENET_TX_DATA02 0x401F80D0U, 0x2U, 0, 0, 0x401F82C0U
#define IOMUXC_GPIO_AD_B0_05_SAI2_TX_BCLK 0x401F80D0U, 0x3U, 0x401F85C0U, 0x1U, 0x401F82C0U
#define IOMUXC_GPIO_AD_B0_05_CSI_DATA08 0x401F80D0U, 0x4U, 0x401F8418U, 0x1U, 0x401F82C0U
#define IOMUXC_GPIO_AD_B0_05_GPIO1_IO05 0x401F80D0U, 0x5U, 0, 0, 0x401F82C0U
#define IOMUXC_GPIO_AD_B0_05_XBAR1_INOUT17 0x401F80D0U, 0x6U, 0x401F862CU, 0x2U, 0x401F82C0U
#define IOMUXC_GPIO_AD_B0_05_LPSPI3_PCS2 0x401F80D0U, 0x7U, 0, 0, 0x401F82C0U

#define IOMUXC_GPIO_AD_B0_06_JTAG_TMS 0x401F80D4U, 0x0U, 0, 0, 0x401F82C4U
#define IOMUXC_GPIO_AD_B0_06_GPT2_COMPARE1 0x401F80D4U, 0x1U, 0, 0, 0x401F82C4U
#define IOMUXC_GPIO_AD_B0_06_ENET_RX_CLK 0x401F80D4U, 0x2U, 0, 0, 0x401F82C4U
#define IOMUXC_GPIO_AD_B0_06_SAI2_RX_BCLK 0x401F80D4U, 0x3U, 0x401F85B4U, 0x1U, 0x401F82C4U
#define IOMUXC_GPIO_AD_B0_06_CSI_DATA07 0x401F80D4U, 0x4U, 0x401F8414U, 0x1U, 0x401F82C4U
#define IOMUXC_GPIO_AD_B0_06_GPIO1_IO06 0x401F80D4U, 0x5U, 0, 0, 0x401F82C4U
#define IOMUXC_GPIO_AD_B0_06_XBAR1_INOUT18 0x401F80D4U, 0x6U, 0x401F8630U, 0x1U, 0x401F82C4U
#define IOMUXC_GPIO_AD_B0_06_LPSPI3_PCS3 0x401F80D4U, 0x7U, 0, 0, 0x401F82C4U

#define IOMUXC_GPIO_AD_B0_07_JTAG_TCK 0x401F80D8U, 0x0U, 0, 0, 0x401F82C8U
#define IOMUXC_GPIO_AD_B0_07_GPT2_COMPARE2 0x401F80D8U, 0x1U, 0, 0, 0x401F82C8U
#define IOMUXC_GPIO_AD_B0_07_ENET_TX_ER 0x401F80D8U, 0x2U, 0, 0, 0x401F82C8U
#define IOMUXC_GPIO_AD_B0_07_SAI2_RX_SYNC 0x401F80D8U, 0x3U, 0x401F85BCU, 0x1U, 0x401F82C8U
#define IOMUXC_GPIO_AD_B0_07_CSI_DATA06 0x401F80D8U, 0x4U, 0x401F8410U, 0x1U, 0x401F82C8U
#define IOMUXC_GPIO_AD_B0_07_GPIO1_IO07 0x401F80D8U, 0x5U, 0, 0, 0x401F82C8U
#define IOMUXC_GPIO_AD_B0_07_XBAR1_INOUT19 0x401F80D8U, 0x6U, 0x401F8654U, 0x1U, 0x401F82C8U
#define IOMUXC_GPIO_AD_B0_07_ENET_1588_EVENT3_OUT 0x401F80D8U, 0x7U, 0, 0, 0x401F82C8U

#define IOMUXC_GPIO_AD_B0_08_JTAG_MOD 0x401F80DCU, 0x0U, 0, 0, 0x401F82CCU
#define IOMUXC_GPIO_AD_B0_08_GPT2_COMPARE3 0x401F80DCU, 0x1U, 0, 0, 0x401F82CCU
#define IOMUXC_GPIO_AD_B0_08_ENET_RX_DATA03 0x401F80DCU, 0x2U, 0, 0, 0x401F82CCU
#define IOMUXC_GPIO_AD_B0_08_SAI2_RX_DATA 0x401F80DCU, 0x3U, 0x401F85B8U, 0x1U, 0x401F82CCU
#define IOMUXC_GPIO_AD_B0_08_CSI_DATA05 0x401F80DCU, 0x4U, 0x401F840CU, 0x1U, 0x401F82CCU
#define IOMUXC_GPIO_AD_B0_08_GPIO1_IO08 0x401F80DCU, 0x5U, 0, 0, 0x401F82CCU
#define IOMUXC_GPIO_AD_B0_08_XBAR1_IN20 0x401F80DCU, 0x6U, 0x401F8634U, 0x1U, 0x401F82CCU
#define IOMUXC_GPIO_AD_B0_08_ENET_1588_EVENT3_IN 0x401F80DCU, 0x7U, 0, 0, 0x401F82CCU

#define IOMUXC_GPIO_AD_B0_09_JTAG_TDI 0x401F80E0U, 0x0U, 0, 0, 0x401F82D0U
#define IOMUXC_GPIO_AD_B0_09_FLEXPWM2_PWMA03 0x401F80E0U, 0x1U, 0x401F8474U, 0x3U, 0x401F82D0U
#define IOMUXC_GPIO_AD_B0_09_ENET_RX_DATA02 0x401F80E0U, 0x2U, 0, 0, 0x401F82D0U
#define IOMUXC_GPIO_AD_B0_09_SAI2_TX_DATA 0x401F80E0U, 0x3U, 0, 0, 0x401F82D0U
#define IOMUXC_GPIO_AD_B0_09_CSI_DATA04 0x401F80E0U, 0x4U, 0x401F8408U, 0x1U, 0x401F82D0U
#define IOMUXC_GPIO_AD_B0_09_GPIO1_IO09 0x401F80E0U, 0x5U, 0, 0, 0x401F82D0U
#define IOMUXC_GPIO_AD_B0_09_XBAR1_IN21 0x401F80E0U, 0x6U, 0x401F8658U, 0x1U, 0x401F82D0U
#define IOMUXC_GPIO_AD_B0_09_GPT2_CLK 0x401F80E0U, 0x7U, 0x401F876CU, 0x0U, 0x401F82D0U
#define IOMUXC_GPIO_AD_B0_09_SEMC_DQS4 0x401F80E0U, 0x9U, 0x401F8788U, 0x2U, 0x401F82D0U

#define IOMUXC_GPIO_AD_B0_10_JTAG_TDO 0x401F80E4U, 0x0U, 0, 0, 0x401F82D4U
#define IOMUXC_GPIO_AD_B0_10_FLEXPWM1_PWMA03 0x401F80E4U, 0x1U, 0x401F8454U, 0x3U, 0x401F82D4U
#define IOMUXC_GPIO_AD_B0_10_ENET_CRS 0x401F80E4U, 0x2U, 0, 0, 0x401F82D4U
#define IOMUXC_GPIO_AD_B0_10_SAI2_MCLK 0x401F80E4U, 0x3U, 0x401F85B0U, 0x1U, 0x401F82D4U
#define IOMUXC_GPIO_AD_B0_10_CSI_DATA03 0x401F80E4U, 0x4U, 0x401F8404U, 0x1U, 0x401F82D4U
#define IOMUXC_GPIO_AD_B0_10_GPIO1_IO10 0x401F80E4U, 0x5U, 0, 0, 0x401F82D4U
#define IOMUXC_GPIO_AD_B0_10_XBAR1_IN22 0x401F80E4U, 0x6U, 0x401F8638U, 0x1U, 0x401F82D4U
#define IOMUXC_GPIO_AD_B0_10_ENET_1588_EVENT0_OUT 0x401F80E4U, 0x7U, 0, 0, 0x401F82D4U
#define IOMUXC_GPIO_AD_B0_10_FLEXCAN3_TX 0x401F80E4U, 0x8U, 0, 0, 0x401F82D4U
#define IOMUXC_GPIO_AD_B0_10_ARM_TRACE_SWO 0x401F80E4U, 0x9U, 0, 0, 0x401F82D4U

#define IOMUXC_GPIO_AD_B0_11_JTAG_TRSTB 0x401F80E8U, 0x0U, 0, 0, 0x401F82D8U
#define IOMUXC_GPIO_AD_B0_11_FLEXPWM1_PWMB03 0x401F80E8U, 0x1U, 0x401F8464U, 0x3U, 0x401F82D8U
#define IOMUXC_GPIO_AD_B0_11_ENET_COL 0x401F80E8U, 0x2U, 0, 0, 0x401F82D8U
#define IOMUXC_GPIO_AD_B0_11_WDOG1_WDOG_B 0x401F80E8U, 0x3U, 0, 0, 0x401F82D8U
#define IOMUXC_GPIO_AD_B0_11_CSI_DATA02 0x401F80E8U, 0x4U, 0x401F8400U, 0x1U, 0x401F82D8U
#define IOMUXC_GPIO_AD_B0_11_GPIO1_IO11 0x401F80E8U, 0x5U, 0, 0, 0x401F82D8U
#define IOMUXC_GPIO_AD_B0_11_XBAR1_IN23 0x401F80E8U, 0x6U, 0x401F863CU, 0x1U, 0x401F82D8U
#define IOMUXC_GPIO_AD_B0_11_ENET_1588_EVENT0_IN 0x401F80E8U, 0x7U, 0x401F8444U, 0x1U, 0x401F82D8U
#define IOMUXC_GPIO_AD_B0_11_FLEXCAN3_RX 0x401F80E8U, 0x8U, 0x401F878CU, 0x2U, 0x401F82D8U
#define IOMUXC_GPIO_AD_B0_11_SEMC_CLK6 0x401F80E8U, 0x9U, 0, 0, 0x401F82D8U

#define IOMUXC_GPIO_AD_B0_12_LPI2C4_SCL 0x401F80ECU, 0x0U, 0x401F84E4U, 0x1U, 0x401F82DCU
#define IOMUXC_GPIO_AD_B0_12_CCM_PMIC_READY 0x401F80ECU, 0x1U, 0x401F83FCU, 0x1U, 0x401F82DCU
#define IOMUXC_GPIO_AD_B0_12_LPUART1_TX 0x401F80ECU, 0x2U, 0, 0, 0x401F82DCU
#define IOMUXC_GPIO_AD_B0_12_WDOG2_WDOG_B 0x401F80ECU, 0x3U, 0, 0, 0x401F82DCU
#define IOMUXC_GPIO_AD_B0_12_FLEXPWM1_PWMX02 0x401F80ECU, 0x4U, 0, 0, 0x401F82DCU
#define IOMUXC_GPIO_AD_B0_12_GPIO1_IO12 0x401F80ECU, 0x5U, 0, 0, 0x401F82DCU
#define IOMUXC_GPIO_AD_B0_12_ENET_1588_EVENT1_OUT 0x401F80ECU, 0x6U, 0, 0, 0x401F82DCU
#define IOMUXC_GPIO_AD_B0_12_NMI_GLUE_NMI 0x401F80ECU, 0x7U, 0x401F8568U, 0x0U, 0x401F82DCU

#define IOMUXC_GPIO_AD_B0_13_LPI2C4_SDA 0x401F80F0U, 0x0U, 0x401F84E8U, 0x1U, 0x401F82E0U
#define IOMUXC_GPIO_AD_B0_13_GPT1_CLK 0x401F80F0U, 0x1U, 0x401F8760U, 0x0U, 0x401F82E0U
#define IOMUXC_GPIO_AD_B0_13_LPUART1_RX 0x401F80F0U, 0x2U, 0, 0, 0x401F82E0U
#define IOMUXC_GPIO_AD_B0_13_EWM_OUT_B 0x401F80F0U, 0x3U, 0, 0, 0x401F82E0U
#define IOMUXC_GPIO_AD_B0_13_FLEXPWM1_PWMX03 0x401F80F0U, 0x4U, 0, 0, 0x401F82E0U
#define IOMUXC_GPIO_AD_B0_13_GPIO1_IO13 0x401F80F0U, 0x5U, 0, 0, 0x401F82E0U
#define IOMUXC_GPIO_AD_B0_13_ENET_1588_EVENT1_IN 0x401F80F0U, 0x6U, 0, 0, 0x401F82E0U
#define IOMUXC_GPIO_AD_B0_13_REF_CLK_24M 0x401F80F0U, 0x7U, 0, 0, 0x401F82E0U

#define IOMUXC_GPIO_AD_B0_14_USB_OTG2_OC 0x401F80F4U, 0x0U, 0x401F85CCU, 0x0U, 0x401F82E4U
#define IOMUXC_GPIO_AD_B0_14_XBAR1_IN24 0x401F80F4U, 0x1U, 0x401F8640U, 0x1U, 0x401F82E4U
#define IOMUXC_GPIO_AD_B0_14_LPUART1_CTS_B 0x401F80F4U, 0x2U, 0, 0, 0x401F82E4U
#define IOMUXC_GPIO_AD_B0_14_ENET_1588_EVENT0_OUT 0x401F80F4U, 0x3U, 0, 0, 0x401F82E4U
#define IOMUXC_GPIO_AD_B0_14_CSI_VSYNC 0x401F80F4U, 0x4U, 0x401F8428U, 0x0U, 0x401F82E4U
#define IOMUXC_GPIO_AD_B0_14_GPIO1_IO14 0x401F80F4U, 0x5U, 0, 0, 0x401F82E4U
#define IOMUXC_GPIO_AD_B0_14_FLEXCAN2_TX 0x401F80F4U, 0x6U, 0, 0, 0x401F82E4U
#define IOMUXC_GPIO_AD_B0_14_FLEXCAN3_TX 0x401F80F4U, 0x8U, 0, 0, 0x401F82E4U

#define IOMUXC_GPIO_AD_B0_15_USB_OTG2_PWR 0x401F80F8U, 0x0U, 0, 0, 0x401F82E8U
#define IOMUXC_GPIO_AD_B0_15_XBAR1_IN25 0x401F80F8U, 0x1U, 0x401F8650U, 0x0U, 0x401F82E8U
#define IOMUXC_GPIO_AD_B0_15_LPUART1_RTS_B 0x401F80F8U, 0x2U, 0, 0, 0x401F82E8U
#define IOMUXC_GPIO_AD_B0_15_ENET_1588_EVENT0_IN 0x401F80F8U, 0x3U, 0x401F8444U, 0x0U, 0x401F82E8U
#define IOMUXC_GPIO_AD_B0_15_CSI_HSYNC 0x401F80F8U, 0x4U, 0x401F8420U, 0x0U, 0x401F82E8U
#define IOMUXC_GPIO_AD_B0_15_GPIO1_IO15 0x401F80F8U, 0x5U, 0, 0, 0x401F82E8U
#define IOMUXC_GPIO_AD_B0_15_FLEXCAN2_RX 0x401F80F8U, 0x6U, 0x401F8450U, 0x2U, 0x401F82E8U
#define IOMUXC_GPIO_AD_B0_15_WDOG1_WDOG_RST_B_DEB 0x401F80F8U, 0x7U, 0, 0, 0x401F82E8U
#define IOMUXC_GPIO_AD_B0_15_FLEXCAN3_RX 0x401F80F8U, 0x8U, 0x401F878CU, 0x1U, 0x401F82E8U

#define IOMUXC_GPIO_AD_B1_00_USB_OTG2_ID 0x401F80FCU, 0x0U, 0x401F83F8U, 0x1U, 0x401F82ECU
#define IOMUXC_GPIO_AD_B1_00_QTIMER3_TIMER0 0x401F80FCU, 0x1U, 0x401F857CU, 0x1U, 0x401F82ECU
#define IOMUXC_GPIO_AD_B1_00_LPUART2_CTS_B 0x401F80FCU, 0x2U, 0, 0, 0x401F82ECU
#define IOMUXC_GPIO_AD_B1_00_LPI2C1_SCL 0x401F80FCU, 0x3U, 0x401F84CCU, 0x1U, 0x401F82ECU
#define IOMUXC_GPIO_AD_B1_00_WDOG1_B 0x401F80FCU, 0x4U, 0, 0, 0x401F82ECU
#define IOMUXC_GPIO_AD_B1_00_GPIO1_IO16 0x401F80FCU, 0x5U, 0, 0, 0x401F82ECU
#define IOMUXC_GPIO_AD_B1_00_USDHC1_WP 0x401F80FCU, 0x6U, 0x401F85D8U, 0x2U, 0x401F82ECU
#define IOMUXC_GPIO_AD_B1_00_KPP_ROW07 0x401F80FCU, 0x7U, 0, 0, 0x401F82ECU
#define IOMUXC_GPIO_AD_B1_00_ENET2_1588_EVENT0_OUT 0x401F80FCU, 0x8U, 0, 0, 0x401F82ECU
#define IOMUXC_GPIO_AD_B1_00_FLEXIO3_FLEXIO00 0x401F80FCU, 0x9U, 0, 0, 0x401F82ECU

#define IOMUXC_GPIO_AD_B1_01_USB_OTG1_PWR 0x401F8100U, 0x0U, 0, 0, 0x401F82F0U
#define IOMUXC_GPIO_AD_B1_01_QTIMER3_TIMER1 0x401F8100U, 0x1U, 0x401F8580U, 0x0U, 0x401F82F0U
#define IOMUXC_GPIO_AD_B1_01_LPUART2_RTS_B 0x401F8100U, 0x2U, 0, 0, 0x401F82F0U
#define IOMUXC_GPIO_AD_B1_01_LPI2C1_SDA 0x401F8100U, 0x3U, 0x401F84D0U, 0x1U, 0x401F82F0U
#define IOMUXC_GPIO_AD_B1_01_CCM_PMIC_READY 0x401F8100U, 0x4U, 0x401F83FCU, 0x2U, 0x401F82F0U
#define IOMUXC_GPIO_AD_B1_01_GPIO1_IO17 0x401F8100U, 0x5U, 0, 0, 0x401F82F0U
#define IOMUXC_GPIO_AD_B1_01_USDHC1_VSELECT 0x401F8100U, 0x6U, 0, 0, 0x401F82F0U
#define IOMUXC_GPIO_AD_B1_01_KPP_COL07 0x401F8100U, 0x7U, 0, 0, 0x401F82F0U
#define IOMUXC_GPIO_AD_B1_01_ENET2_1588_EVENT0_IN 0x401F8100U, 0x8U, 0x401F8724U, 0x0U, 0x401F82F0U
#define IOMUXC_GPIO_AD_B1_01_FLEXIO3_FLEXIO01 0x401F8100U, 0x9U, 0, 0, 0x401F82F0U

#define IOMUXC_GPIO_AD_B1_02_USB_OTG1_ID 0x401F8104U, 0x0U, 0x401F83F4U, 0x1U, 0x401F82F4U
#define IOMUXC_GPIO_AD_B1_02_QTIMER3_TIMER2 0x401F8104U, 0x1U, 0x401F8584U, 0x1U, 0x401F82F4U
#define IOMUXC_GPIO_AD_B1_02_LPUART2_TX 0x401F8104U, 0x2U, 0x401F8530U, 0x1U, 0x401F82F4U
#define IOMUXC_GPIO_AD_B1_02_SPDIF_OUT 0x401F8104U, 0x3U, 0, 0, 0x401F82F4U
#define IOMUXC_GPIO_AD_B1_02_ENET_1588_EVENT2_OUT 0x401F8104U, 0x4U, 0, 0, 0x401F82F4U
#define IOMUXC_GPIO_AD_B1_02_GPIO1_IO18 0x401F8104U, 0x5U, 0, 0, 0x401F82F4U
#define IOMUXC_GPIO_AD_B1_02_USDHC1_CD_B 0x401F8104U, 0x6U, 0x401F85D4U, 0x1U, 0x401F82F4U
#define IOMUXC_GPIO_AD_B1_02_KPP_ROW06 0x401F8104U, 0x7U, 0, 0, 0x401F82F4U
#define IOMUXC_GPIO_AD_B1_02_GPT2_CLK 0x401F8104U, 0x8U, 0x401F876CU, 0x1U, 0x401F82F4U
#define IOMUXC_GPIO_AD_B1_02_FLEXIO3_FLEXIO02 0x401F8104U, 0x9U, 0, 0, 0x401F82F4U

#define IOMUXC_GPIO_AD_B1_03_USB_OTG1_OC 0x401F8108U, 0x0U, 0x401F85D0U, 0x1U, 0x401F82F8U
#define IOMUXC_GPIO_AD_B1_03_QTIMER3_TIMER3 0x401F8108U, 0x1U, 0x401F8588U, 0x1U, 0x401F82F8U
#define IOMUXC_GPIO_AD_B1_03_LPUART2_RX 0x401F8108U, 0x2U, 0x401F852CU, 0x1U, 0x401F82F8U
#define IOMUXC_GPIO_AD_B1_03_SPDIF_IN 0x401F8108U, 0x3U, 0x401F85C8U, 0x0U, 0x401F82F8U
#define IOMUXC_GPIO_AD_B1_03_ENET_1588_EVENT2_IN 0x401F8108U, 0x4U, 0, 0, 0x401F82F8U
#define IOMUXC_GPIO_AD_B1_03_GPIO1_IO19 0x401F8108U, 0x5U, 0, 0, 0x401F82F8U
#define IOMUXC_GPIO_AD_B1_03_USDHC2_CD_B 0x401F8108U, 0x6U, 0x401F85E0U, 0x0U, 0x401F82F8U
#define IOMUXC_GPIO_AD_B1_03_KPP_COL06 0x401F8108U, 0x7U, 0, 0, 0x401F82F8U
#define IOMUXC_GPIO_AD_B1_03_GPT2_CAPTURE1 0x401F8108U, 0x8U, 0x401F8764U, 0x1U, 0x401F82F8U
#define IOMUXC_GPIO_AD_B1_03_FLEXIO3_FLEXIO03 0x401F8108U, 0x9U, 0, 0, 0x401F82F8U

#define IOMUXC_GPIO_AD_B1_04_FLEXSPIB_DATA03 0x401F810CU, 0x0U, 0x401F84C4U, 0x1U, 0x401F82FCU
#define IOMUXC_GPIO_AD_B1_04_ENET_MDC 0x401F810CU, 0x1U, 0, 0, 0x401F82FCU
#define IOMUXC_GPIO_AD_B1_04_LPUART3_CTS_B 0x401F810CU, 0x2U, 0x401F8534U, 0x1U, 0x401F82FCU
#define IOMUXC_GPIO_AD_B1_04_SPDIF_SR_CLK 0x401F810CU, 0x3U, 0, 0, 0x401F82FCU
#define IOMUXC_GPIO_AD_B1_04_CSI_PIXCLK 0x401F810CU, 0x4U, 0x401F8424U, 0x0U, 0x401F82FCU
#define IOMUXC_GPIO_AD_B1_04_GPIO1_IO20 0x401F810CU, 0x5U, 0, 0, 0x401F82FCU
#define IOMUXC_GPIO_AD_B1_04_USDHC2_DATA0 0x401F810CU, 0x6U, 0x401F85E8U, 0x1U, 0x401F82FCU
#define IOMUXC_GPIO_AD_B1_04_KPP_ROW05 0x401F810CU, 0x7U, 0, 0, 0x401F82FCU
#define IOMUXC_GPIO_AD_B1_04_GPT2_CAPTURE2 0x401F810CU, 0x8U, 0x401F8768U, 0x1U, 0x401F82FCU
#define IOMUXC_GPIO_AD_B1_04_FLEXIO3_FLEXIO04 0x401F810CU, 0x9U, 0, 0, 0x401F82FCU

#define IOMUXC_GPIO_AD_B1_05_FLEXSPIB_DATA02 0x401F8110U, 0x0U, 0x401F84C0U, 0x1U, 0x401F8300U
#define IOMUXC_GPIO_AD_B1_05_ENET_MDIO 0x401F8110U, 0x1U, 0x401F8430U, 0x0U, 0x401F8300U
#define IOMUXC_GPIO_AD_B1_05_LPUART3_RTS_B 0x401F8110U, 0x2U, 0, 0, 0x401F8300U
#define IOMUXC_GPIO_AD_B1_05_SPDIF_OUT 0x401F8110U, 0x3U, 0, 0, 0x401F8300U
#define IOMUXC_GPIO_AD_B1_05_CSI_MCLK 0x401F8110U, 0x4U, 0, 0, 0x401F8300U
#define IOMUXC_GPIO_AD_B1_05_GPIO1_IO21 0x401F8110U, 0x5U, 0, 0, 0x401F8300U
#define IOMUXC_GPIO_AD_B1_05_USDHC2_DATA1 0x401F8110U, 0x6U, 0x401F85ECU, 0x1U, 0x401F8300U
#define IOMUXC_GPIO_AD_B1_05_KPP_COL05 0x401F8110U, 0x7U, 0, 0, 0x401F8300U
#define IOMUXC_GPIO_AD_B1_05_GPT2_COMPARE1 0x401F8110U, 0x8U, 0, 0, 0x401F8300U
#define IOMUXC_GPIO_AD_B1_05_FLEXIO3_FLEXIO05 0x401F8110U, 0x9U, 0, 0, 0x401F8300U

#define IOMUXC_GPIO_AD_B1_06_FLEXSPIB_DATA01 0x401F8114U, 0x0U, 0x401F84BCU, 0x1U, 0x401F8304U
#define IOMUXC_GPIO_AD_B1_06_LPI2C3_SDA 0x401F8114U, 0x1U, 0x401F84E0U, 0x2U, 0x401F8304U
#define IOMUXC_GPIO_AD_B1_06_LPUART3_TX 0x401F8114U, 0x2U, 0x401F853CU, 0x0U, 0x401F8304U
#define IOMUXC_GPIO_AD_B1_06_SPDIF_LOCK 0x401F8114U, 0x3U, 0, 0, 0x401F8304U
#define IOMUXC_GPIO_AD_B1_06_CSI_VSYNC 0x401F8114U, 0x4U, 0x401F8428U, 0x1U, 0x401F8304U
#define IOMUXC_GPIO_AD_B1_06_GPIO1_IO22 0x401F8114U, 0x5U, 0, 0, 0x401F8304U
#define IOMUXC_GPIO_AD_B1_06_USDHC2_DATA2 0x401F8114U, 0x6U, 0x401F85F0U, 0x1U, 0x401F8304U
#define IOMUXC_GPIO_AD_B1_06_KPP_ROW04 0x401F8114U, 0x7U, 0, 0, 0x401F8304U
#define IOMUXC_GPIO_AD_B1_06_GPT2_COMPARE2 0x401F8114U, 0x8U, 0, 0, 0x401F8304U
#define IOMUXC_GPIO_AD_B1_06_FLEXIO3_FLEXIO06 0x401F8114U, 0x9U, 0, 0, 0x401F8304U

#define IOMUXC_GPIO_AD_B1_07_FLEXSPIB_DATA00 0x401F8118U, 0x0U, 0x401F84B8U, 0x1U, 0x401F8308U
#define IOMUXC_GPIO_AD_B1_07_LPI2C3_SCL 0x401F8118U, 0x1U, 0x401F84DCU, 0x2U, 0x401F8308U
#define IOMUXC_GPIO_AD_B1_07_LPUART3_RX 0x401F8118U, 0x2U, 0x401F8538U, 0x0U, 0x401F8308U
#define IOMUXC_GPIO_AD_B1_07_SPDIF_EXT_CLK 0x401F8118U, 0x3U, 0, 0, 0x401F8308U
#define IOMUXC_GPIO_AD_B1_07_CSI_HSYNC 0x401F8118U, 0x4U, 0x401F8420U, 0x1U, 0x401F8308U
#define IOMUXC_GPIO_AD_B1_07_GPIO1_IO23 0x401F8118U, 0x5U, 0, 0, 0x401F8308U
#define IOMUXC_GPIO_AD_B1_07_USDHC2_DATA3 0x401F8118U, 0x6U, 0x401F85F4U, 0x1U, 0x401F8308U
#define IOMUXC_GPIO_AD_B1_07_KPP_COL04 0x401F8118U, 0x7U, 0, 0, 0x401F8308U
#define IOMUXC_GPIO_AD_B1_07_GPT2_COMPARE3 0x401F8118U, 0x8U, 0, 0, 0x401F8308U
#define IOMUXC_GPIO_AD_B1_07_FLEXIO3_FLEXIO07 0x401F8118U, 0x9U, 0, 0, 0x401F8308U

#define IOMUXC_GPIO_AD_B1_08_FLEXSPIA_SS1_B 0x401F811CU, 0x0U, 0, 0, 0x401F830CU
#define IOMUXC_GPIO_AD_B1_08_FLEXPWM4_PWMA00 0x401F811CU, 0x1U, 0x401F8494U, 0x1U, 0x401F830CU
#define IOMUXC_GPIO_AD_B1_08_FLEXCAN1_TX 0x401F811CU, 0x2U, 0, 0, 0x401F830CU
#define IOMUXC_GPIO_AD_B1_08_CCM_PMIC_READY 0x401F811CU, 0x3U, 0x401F83FCU, 0x3U, 0x401F830CU
#define IOMUXC_GPIO_AD_B1_08_CSI_DATA09 0x401F811CU, 0x4U, 0x401F841CU, 0x0U, 0x401F830CU
#define IOMUXC_GPIO_AD_B1_08_GPIO1_IO24 0x401F811CU, 0x5U, 0, 0, 0x401F830CU
#define IOMUXC_GPIO_AD_B1_08_USDHC2_CMD 0x401F811CU, 0x6U, 0x401F85E4U, 0x1U, 0x401F830CU
#define IOMUXC_GPIO_AD_B1_08_KPP_ROW03 0x401F811CU, 0x7U, 0, 0, 0x401F830CU
#define IOMUXC_GPIO_AD_B1_08_FLEXIO3_FLEXIO08 0x401F811CU, 0x9U, 0, 0, 0x401F830CU

#define IOMUXC_GPIO_AD_B1_09_FLEXSPIA_DQS 0x401F8120U, 0x0U, 0x401F84A4U, 0x1U, 0x401F8310U
#define IOMUXC_GPIO_AD_B1_09_FLEXPWM4_PWMA01 0x401F8120U, 0x1U, 0x401F8498U, 0x1U, 0x401F8310U
#define IOMUXC_GPIO_AD_B1_09_FLEXCAN1_RX 0x401F8120U, 0x2U, 0x401F844CU, 0x2U, 0x401F8310U
#define IOMUXC_GPIO_AD_B1_09_SAI1_MCLK 0x401F8120U, 0x3U, 0x401F858CU, 0x1U, 0x401F8310U
#define IOMUXC_GPIO_AD_B1_09_CSI_DATA08 0x401F8120U, 0x4U, 0x401F8418U, 0x0U, 0x401F8310U
#define IOMUXC_GPIO_AD_B1_09_GPIO1_IO25 0x401F8120U, 0x5U, 0, 0, 0x401F8310U
#define IOMUXC_GPIO_AD_B1_09_USDHC2_CLK 0x401F8120U, 0x6U, 0x401F85DCU, 0x1U, 0x401F8310U
#define IOMUXC_GPIO_AD_B1_09_KPP_COL03 0x401F8120U, 0x7U, 0, 0, 0x401F8310U
#define IOMUXC_GPIO_AD_B1_09_FLEXIO3_FLEXIO09 0x401F8120U, 0x9U, 0, 0, 0x401F8310U

#define IOMUXC_GPIO_AD_B1_10_FLEXSPIA_DATA03 0x401F8124U, 0x0U, 0x401F84B4U, 0x1U, 0x401F8314U
#define IOMUXC_GPIO_AD_B1_10_WDOG1_B 0x401F8124U, 0x1U, 0, 0, 0x401F8314U
#define IOMUXC_GPIO_AD_B1_10_LPUART8_TX 0x401F8124U, 0x2U, 0x401F8564U, 0x1U, 0x401F8314U
#define IOMUXC_GPIO_AD_B1_10_SAI1_RX_SYNC 0x401F8124U, 0x3U, 0x401F85A4U, 0x1U, 0x401F8314U
#define IOMUXC_GPIO_AD_B1_10_CSI_DATA07 0x401F8124U, 0x4U, 0x401F8414U, 0x0U, 0x401F8314U
#define IOMUXC_GPIO_AD_B1_10_GPIO1_IO26 0x401F8124U, 0x5U, 0, 0, 0x401F8314U
#define IOMUXC_GPIO_AD_B1_10_USDHC2_WP 0x401F8124U, 0x6U, 0x401F8608U, 0x1U, 0x401F8314U
#define IOMUXC_GPIO_AD_B1_10_KPP_ROW02 0x401F8124U, 0x7U, 0, 0, 0x401F8314U
#define IOMUXC_GPIO_AD_B1_10_ENET2_1588_EVENT1_OUT 0x401F8124U, 0x8U, 0, 0, 0x401F8314U
#define IOMUXC_GPIO_AD_B1_10_FLEXIO3_FLEXIO10 0x401F8124U, 0x9U, 0, 0, 0x401F8314U

#define IOMUXC_GPIO_AD_B1_11_FLEXSPIA_DATA02 0x401F8128U, 0x0U, 0x401F84B0U, 0x1U, 0x401F8318U
#define IOMUXC_GPIO_AD_B1_11_EWM_OUT_B 0x401F8128U, 0x1U, 0, 0, 0x401F8318U
#define IOMUXC_GPIO_AD_B1_11_LPUART8_RX 0x401F8128U, 0x2U, 0x401F8560U, 0x1U, 0x401F8318U
#define IOMUXC_GPIO_AD_B1_11_SAI1_RX_BCLK 0x401F8128U, 0x3U, 0x401F8590U, 0x1U, 0x401F8318U
#define IOMUXC_GPIO_AD_B1_11_CSI_DATA06 0x401F8128U, 0x4U, 0x401F8410U, 0x0U, 0x401F8318U
#define IOMUXC_GPIO_AD_B1_11_GPIO1_IO27 0x401F8128U, 0x5U, 0, 0, 0x401F8318U
#define IOMUXC_GPIO_AD_B1_11_USDHC2_RESET_B 0x401F8128U, 0x6U, 0, 0, 0x401F8318U
#define IOMUXC_GPIO_AD_B1_11_KPP_COL02 0x401F8128U, 0x7U, 0, 0, 0x401F8318U
#define IOMUXC_GPIO_AD_B1_11_ENET2_1588_EVENT1_IN 0x401F8128U, 0x8U, 0, 0, 0x401F8318U
#define IOMUXC_GPIO_AD_B1_11_FLEXIO3_FLEXIO11 0x401F8128U, 0x9U, 0, 0, 0x401F8318U

#define IOMUXC_GPIO_AD_B1_12_FLEXSPIA_DATA01 0x401F812CU, 0x0U, 0x401F84ACU, 0x1U, 0x401F831CU
#define IOMUXC_GPIO_AD_B1_12_ACMP_OUT00 0x401F812CU, 0x1U, 0, 0, 0x401F831CU
#define IOMUXC_GPIO_AD_B1_12_LPSPI3_PCS0 0x401F812CU, 0x2U, 0x401F850CU, 0x1U, 0x401F831CU
#define IOMUXC_GPIO_AD_B1_12_SAI1_RX_DATA00 0x401F812CU, 0x3U, 0x401F8594U, 0x1U, 0x401F831CU
#define IOMUXC_GPIO_AD_B1_12_CSI_DATA05 0x401F812CU, 0x4U, 0x401F840CU, 0x0U, 0x401F831CU
#define IOMUXC_GPIO_AD_B1_12_GPIO1_IO28 0x401F812CU, 0x5U, 0, 0, 0x401F831CU
#define IOMUXC_GPIO_AD_B1_12_USDHC2_DATA4 0x401F812CU, 0x6U, 0x401F85F8U, 0x1U, 0x401F831CU
#define IOMUXC_GPIO_AD_B1_12_KPP_ROW01 0x401F812CU, 0x7U, 0, 0, 0x401F831CU
#define IOMUXC_GPIO_AD_B1_12_ENET2_1588_EVENT2_OUT 0x401F812CU, 0x8U, 0, 0, 0x401F831CU
#define IOMUXC_GPIO_AD_B1_12_FLEXIO3_FLEXIO12 0x401F812CU, 0x9U, 0, 0, 0x401F831CU

#define IOMUXC_GPIO_AD_B1_13_FLEXSPIA_DATA00 0x401F8130U, 0x0U, 0x401F84A8U, 0x1U, 0x401F8320U
#define IOMUXC_GPIO_AD_B1_13_ACMP_OUT01 0x401F8130U, 0x1U, 0, 0, 0x401F8320U
#define IOMUXC_GPIO_AD_B1_13_LPSPI3_SDI 0x401F8130U, 0x2U, 0x401F8514U, 0x1U, 0x401F8320U
#define IOMUXC_GPIO_AD_B1_13_SAI1_TX_DATA00 0x401F8130U, 0x3U, 0, 0, 0x401F8320U
#define IOMUXC_GPIO_AD_B1_13_CSI_DATA04 0x401F8130U, 0x4U, 0x401F8408U, 0x0U, 0x401F8320U
#define IOMUXC_GPIO_AD_B1_13_GPIO1_IO29 0x401F8130U, 0x5U, 0, 0, 0x401F8320U
#define IOMUXC_GPIO_AD_B1_13_USDHC2_DATA5 0x401F8130U, 0x6U, 0x401F85FCU, 0x1U, 0x401F8320U
#define IOMUXC_GPIO_AD_B1_13_KPP_COL01 0x401F8130U, 0x7U, 0, 0, 0x401F8320U
#define IOMUXC_GPIO_AD_B1_13_ENET2_1588_EVENT2_IN 0x401F8130U, 0x8U, 0, 0, 0x401F8320U
#define IOMUXC_GPIO_AD_B1_13_FLEXIO3_FLEXIO13 0x401F8130U, 0x9U, 0, 0, 0x401F8320U

#define IOMUXC_GPIO_AD_B1_14_FLEXSPIA_SCLK 0x401F8134U, 0x0U, 0x401F84C8U, 0x1U, 0x401F8324U
#define IOMUXC_GPIO_AD_B1_14_ACMP_OUT02 0x401F8134U, 0x1U, 0, 0, 0x401F8324U
#define IOMUXC_GPIO_AD_B1_14_LPSPI3_SDO 0x401F8134U, 0x2U, 0x401F8518U, 0x1U, 0x401F8324U
#define IOMUXC_GPIO_AD_B1_14_SAI1_TX_BCLK 0x401F8134U, 0x3U, 0x401F85A8U, 0x1U, 0x401F8324U
#define IOMUXC_GPIO_AD_B1_14_CSI_DATA03 0x401F8134U, 0x4U, 0x401F8404U, 0x0U, 0x401F8324U
#define IOMUXC_GPIO_AD_B1_14_GPIO1_IO30 0x401F8134U, 0x5U, 0, 0, 0x401F8324U
#define IOMUXC_GPIO_AD_B1_14_USDHC2_DATA6 0x401F8134U, 0x6U, 0x401F8600U, 0x1U, 0x401F8324U
#define IOMUXC_GPIO_AD_B1_14_KPP_ROW00 0x401F8134U, 0x7U, 0, 0, 0x401F8324U
#define IOMUXC_GPIO_AD_B1_14_ENET2_1588_EVENT3_OUT 0x401F8134U, 0x8U, 0, 0, 0x401F8324U
#define IOMUXC_GPIO_AD_B1_14_FLEXIO3_FLEXIO14 0x401F8134U, 0x9U, 0, 0, 0x401F8324U

#define IOMUXC_GPIO_AD_B1_15_FLEXSPIA_SS0_B 0x401F8138U, 0x0U, 0, 0, 0x401F8328U
#define IOMUXC_GPIO_AD_B1_15_ACMP_OUT03 0x401F8138U, 0x1U, 0, 0, 0x401F8328U
#define IOMUXC_GPIO_AD_B1_15_LPSPI3_SCK 0x401F8138U, 0x2U, 0x401F8510U, 0x1U, 0x401F8328U
#define IOMUXC_GPIO_AD_B1_15_SAI1_TX_SYNC 0x401F8138U, 0x3U, 0x401F85ACU, 0x1U, 0x401F8328U
#define IOMUXC_GPIO_AD_B1_15_CSI_DATA02 0x401F8138U, 0x4U, 0x401F8400U, 0x0U, 0x401F8328U
#define IOMUXC_GPIO_AD_B1_15_GPIO1_IO31 0x401F8138U, 0x5U, 0, 0, 0x401F8328U
#define IOMUXC_GPIO_AD_B1_15_USDHC2_DATA7 0x401F8138U, 0x6U, 0x401F8604U, 0x1U, 0x401F8328U
#define IOMUXC_GPIO_AD_B1_15_KPP_COL00 0x401F8138U, 0x7U, 0, 0, 0x401F8328U
#define IOMUXC_GPIO_AD_B1_15_ENET2_1588_EVENT3_IN 0x401F8138U, 0x8U, 0, 0, 0x401F8328U
#define IOMUXC_GPIO_AD_B1_15_FLEXIO3_FLEXIO15 0x401F8138U, 0x9U, 0, 0, 0x401F8328U

#define IOMUXC_GPIO_B0_00_LCD_CLK 0x401F813CU, 0x0U, 0, 0, 0x401F832CU
#define IOMUXC_GPIO_B0_00_QTIMER1_TIMER0 0x401F813CU, 0x1U, 0, 0, 0x401F832CU
#define IOMUXC_GPIO_B0_00_MQS_RIGHT 0x401F813CU, 0x2U, 0, 0, 0x401F832CU
#define IOMUXC_GPIO_B0_00_LPSPI4_PCS0 0x401F813CU, 0x3U, 0x401F851CU, 0x0U, 0x401F832CU
#define IOMUXC_GPIO_B0_00_FLEXIO2_FLEXIO00 0x401F813CU, 0x4U, 0, 0, 0x401F832CU
#define IOMUXC_GPIO_B0_00_GPIO2_IO00 0x401F813CU, 0x5U, 0, 0, 0x401F832CU
#define IOMUXC_GPIO_B0_00_SEMC_CSX01 0x401F813CU, 0x6U, 0, 0, 0x401F832CU
#define IOMUXC_GPIO_B0_00_ENET2_MDC 0x401F813CU, 0x8U, 0, 0, 0x401F832CU

#define IOMUXC_GPIO_B0_01_LCD_ENABLE 0x401F8140U, 0x0U, 0, 0, 0x401F8330U
#define IOMUXC_GPIO_B0_01_QTIMER1_TIMER1 0x401F8140U, 0x1U, 0, 0, 0x401F8330U
#define IOMUXC_GPIO_B0_01_MQS_LEFT 0x401F8140U, 0x2U, 0, 0, 0x401F8330U
#define IOMUXC_GPIO_B0_01_LPSPI4_SDI 0x401F8140U, 0x3U, 0x401F8524U, 0x0U, 0x401F8330U
#define IOMUXC_GPIO_B0_01_FLEXIO2_FLEXIO01 0x401F8140U, 0x4U, 0, 0, 0x401F8330U
#define IOMUXC_GPIO_B0_01_GPIO2_IO01 0x401F8140U, 0x5U, 0, 0, 0x401F8330U
#define IOMUXC_GPIO_B0_01_SEMC_CSX02 0x401F8140U, 0x6U, 0, 0, 0x401F8330U
#define IOMUXC_GPIO_B0_01_ENET2_MDIO 0x401F8140U, 0x8U, 0x401F8710U, 0x1U, 0x401F8330U

#define IOMUXC_GPIO_B0_02_LCD_HSYNC 0x401F8144U, 0x0U, 0, 0, 0x401F8334U
#define IOMUXC_GPIO_B0_02_QTIMER1_TIMER2 0x401F8144U, 0x1U, 0, 0, 0x401F8334U
#define IOMUXC_GPIO_B0_02_FLEXCAN1_TX 0x401F8144U, 0x2U, 0, 0, 0x401F8334U
#define IOMUXC_GPIO_B0_02_LPSPI4_SDO 0x401F8144U, 0x3U, 0x401F8528U, 0x0U, 0x401F8334U
#define IOMUXC_GPIO_B0_02_FLEXIO2_FLEXIO02 0x401F8144U, 0x4U, 0, 0, 0x401F8334U
#define IOMUXC_GPIO_B0_02_GPIO2_IO02 0x401F8144U, 0x5U, 0, 0, 0x401F8334U
#define IOMUXC_GPIO_B0_02_SEMC_CSX03 0x401F8144U, 0x6U, 0, 0, 0x401F8334U
#define IOMUXC_GPIO_B0_02_ENET2_1588_EVENT0_OUT 0x401F8144U, 0x8U, 0, 0, 0x401F8334U

#define IOMUXC_GPIO_B0_03_LCD_VSYNC 0x401F8148U, 0x0U, 0, 0, 0x401F8338U
#define IOMUXC_GPIO_B0_03_QTIMER2_TIMER0 0x401F8148U, 0x1U, 0x401F856CU, 0x1U, 0x401F8338U
#define IOMUXC_GPIO_B0_03_FLEXCAN1_RX 0x401F8148U, 0x2U, 0x401F844CU, 0x3U, 0x401F8338U
#define IOMUXC_GPIO_B0_03_LPSPI4_SCK 0x401F8148U, 0x3U, 0x401F8520U, 0x0U, 0x401F8338U
#define IOMUXC_GPIO_B0_03_FLEXIO2_FLEXIO03 0x401F8148U, 0x4U, 0, 0, 0x401F8338U
#define IOMUXC_GPIO_B0_03_GPIO2_IO03 0x401F8148U, 0x5U, 0, 0, 0x401F8338U
#define IOMUXC_GPIO_B0_03_WDOG2_RESET_B_DEB 0x401F8148U, 0x6U, 0, 0, 0x401F8338U
#define IOMUXC_GPIO_B0_03_ENET2_1588_EVENT0_IN 0x401F8148U, 0x8U, 0x401F8724U, 0x1U, 0x401F8338U

#define IOMUXC_GPIO_B0_04_LCD_DATA00 0x401F814CU, 0x0U, 0, 0, 0x401F833CU
#define IOMUXC_GPIO_B0_04_QTIMER2_TIMER1 0x401F814CU, 0x1U, 0x401F8570U, 0x1U, 0x401F833CU
#define IOMUXC_GPIO_B0_04_LPI2C2_SCL 0x401F814CU, 0x2U, 0x401F84D4U, 0x1U, 0x401F833CU
#define IOMUXC_GPIO_B0_04_ARM_TRACE0 0x401F814CU, 0x3U, 0, 0, 0x401F833CU
#define IOMUXC_GPIO_B0_04_FLEXIO2_FLEXIO04 0x401F814CU, 0x4U, 0, 0, 0x401F833CU
#define IOMUXC_GPIO_B0_04_GPIO2_IO04 0x401F814CU, 0x5U, 0, 0, 0x401F833CU
#define IOMUXC_GPIO_B0_04_SRC_BOOT_CFG00 0x401F814CU, 0x6U, 0, 0, 0x401F833CU
#define IOMUXC_GPIO_B0_04_ENET2_TDATA03 0x401F814CU, 0x8U, 0, 0, 0x401F833CU

#define IOMUXC_GPIO_B0_05_LCD_DATA01 0x401F8150U, 0x0U, 0, 0, 0x401F8340U
#define IOMUXC_GPIO_B0_05_QTIMER2_TIMER2 0x401F8150U, 0x1U, 0x401F8574U, 0x1U, 0x401F8340U
#define IOMUXC_GPIO_B0_05_LPI2C2_SDA 0x401F8150U, 0x2U, 0x401F84D8U, 0x1U, 0x401F8340U
#define IOMUXC_GPIO_B0_05_ARM_TRACE1 0x401F8150U, 0x3U, 0, 0, 0x401F8340U
#define IOMUXC_GPIO_B0_05_FLEXIO2_FLEXIO05 0x401F8150U, 0x4U, 0, 0, 0x401F8340U
#define IOMUXC_GPIO_B0_05_GPIO2_IO05 0x401F8150U, 0x5U, 0, 0, 0x401F8340U
#define IOMUXC_GPIO_B0_05_SRC_BOOT_CFG01 0x401F8150U, 0x6U, 0, 0, 0x401F8340U
#define IOMUXC_GPIO_B0_05_ENET2_TDATA02 0x401F8150U, 0x8U, 0, 0, 0x401F8340U

#define IOMUXC_GPIO_B0_06_LCD_DATA02 0x401F8154U, 0x0U, 0, 0, 0x401F8344U
#define IOMUXC_GPIO_B0_06_QTIMER3_TIMER0 0x401F8154U, 0x1U, 0x401F857CU, 0x2U, 0x401F8344U
#define IOMUXC_GPIO_B0_06_FLEXPWM2_PWMA00 0x401F8154U, 0x2U, 0x401F8478U, 0x1U, 0x401F8344U
#define IOMUXC_GPIO_B0_06_ARM_TRACE2 0x401F8154U, 0x3U, 0, 0, 0x401F8344U
#define IOMUXC_GPIO_B0_06_FLEXIO2_FLEXIO06 0x401F8154U, 0x4U, 0, 0, 0x401F8344U
#define IOMUXC_GPIO_B0_06_GPIO2_IO06 0x401F8154U, 0x5U, 0, 0, 0x401F8344U
#define IOMUXC_GPIO_B0_06_SRC_BOOT_CFG02 0x401F8154U, 0x6U, 0, 0, 0x401F8344U
#define IOMUXC_GPIO_B0_06_ENET2_RX_CLK 0x401F8154U, 0x8U, 0, 0, 0x401F8344U

#define IOMUXC_GPIO_B0_07_LCD_DATA03 0x401F8158U, 0x0U, 0, 0, 0x401F8348U
#define IOMUXC_GPIO_B0_07_QTIMER3_TIMER1 0x401F8158U, 0x1U, 0x401F8580U, 0x2U, 0x401F8348U
#define IOMUXC_GPIO_B0_07_FLEXPWM2_PWMB00 0x401F8158U, 0x2U, 0x401F8488U, 0x1U, 0x401F8348U
#define IOMUXC_GPIO_B0_07_ARM_TRACE3 0x401F8158U, 0x3U, 0, 0, 0x401F8348U
#define IOMUXC_GPIO_B0_07_FLEXIO2_FLEXIO07 0x401F8158U, 0x4U, 0, 0, 0x401F8348U
#define IOMUXC_GPIO_B0_07_GPIO2_IO07 0x401F8158U, 0x5U, 0, 0, 0x401F8348U
#define IOMUXC_GPIO_B0_07_SRC_BOOT_CFG03 0x401F8158U, 0x6U, 0, 0, 0x401F8348U
#define IOMUXC_GPIO_B0_07_ENET2_TX_ER 0x401F8158U, 0x8U, 0, 0, 0x401F8348U

#define IOMUXC_GPIO_B0_08_LCD_DATA04 0x401F815CU, 0x0U, 0, 0, 0x401F834CU
#define IOMUXC_GPIO_B0_08_QTIMER3_TIMER2 0x401F815CU, 0x1U, 0x401F8584U, 0x2U, 0x401F834CU
#define IOMUXC_GPIO_B0_08_FLEXPWM2_PWMA01 0x401F815CU, 0x2U, 0x401F847CU, 0x1U, 0x401F834CU
#define IOMUXC_GPIO_B0_08_LPUART3_TX 0x401F815CU, 0x3U, 0x401F853CU, 0x2U, 0x401F834CU
#define IOMUXC_GPIO_B0_08_FLEXIO2_FLEXIO08 0x401F815CU, 0x4U, 0, 0, 0x401F834CU
#define IOMUXC_GPIO_B0_08_GPIO2_IO08 0x401F815CU, 0x5U, 0, 0, 0x401F834CU
#define IOMUXC_GPIO_B0_08_SRC_BOOT_CFG04 0x401F815CU, 0x6U, 0, 0, 0x401F834CU
#define IOMUXC_GPIO_B0_08_ENET2_RDATA03 0x401F815CU, 0x8U, 0, 0, 0x401F834CU

#define IOMUXC_GPIO_B0_09_LCD_DATA05 0x401F8160U, 0x0U, 0, 0, 0x401F8350U
#define IOMUXC_GPIO_B0_09_QTIMER4_TIMER0 0x401F8160U, 0x1U, 0, 0, 0x401F8350U
#define IOMUXC_GPIO_B0_09_FLEXPWM2_PWMB01 0x401F8160U, 0x2U, 0x401F848CU, 0x1U, 0x401F8350U
#define IOMUXC_GPIO_B0_09_LPUART3_RX 0x401F8160U, 0x3U, 0x401F8538U, 0x2U, 0x401F8350U
#define IOMUXC_GPIO_B0_09_FLEXIO2_FLEXIO09 0x401F8160U, 0x4U, 0, 0, 0x401F8350U
#define IOMUXC_GPIO_B0_09_GPIO2_IO09 0x401F8160U, 0x5U, 0, 0, 0x401F8350U
#define IOMUXC_GPIO_B0_09_SRC_BOOT_CFG05 0x401F8160U, 0x6U, 0, 0, 0x401F8350U
#define IOMUXC_GPIO_B0_09_ENET2_RDATA02 0x401F8160U, 0x8U, 0, 0, 0x401F8350U

#define IOMUXC_GPIO_B0_10_LCD_DATA06 0x401F8164U, 0x0U, 0, 0, 0x401F8354U
#define IOMUXC_GPIO_B0_10_QTIMER4_TIMER1 0x401F8164U, 0x1U, 0, 0, 0x401F8354U
#define IOMUXC_GPIO_B0_10_FLEXPWM2_PWMA02 0x401F8164U, 0x2U, 0x401F8480U, 0x1U, 0x401F8354U
#define IOMUXC_GPIO_B0_10_SAI1_TX_DATA03 0x401F8164U, 0x3U, 0x401F8598U, 0x1U, 0x401F8354U
#define IOMUXC_GPIO_B0_10_FLEXIO2_FLEXIO10 0x401F8164U, 0x4U, 0, 0, 0x401F8354U
#define IOMUXC_GPIO_B0_10_GPIO2_IO10 0x401F8164U, 0x5U, 0, 0, 0x401F8354U
#define IOMUXC_GPIO_B0_10_SRC_BOOT_CFG06 0x401F8164U, 0x6U, 0, 0, 0x401F8354U
#define IOMUXC_GPIO_B0_10_ENET2_CRS 0x401F8164U, 0x8U, 0, 0, 0x401F8354U

#define IOMUXC_GPIO_B0_11_LCD_DATA07 0x401F8168U, 0x0U, 0, 0, 0x401F8358U
#define IOMUXC_GPIO_B0_11_QTIMER4_TIMER2 0x401F8168U, 0x1U, 0, 0, 0x401F8358U
#define IOMUXC_GPIO_B0_11_FLEXPWM2_PWMB02 0x401F8168U, 0x2U, 0x401F8490U, 0x1U, 0x401F8358U
#define IOMUXC_GPIO_B0_11_SAI1_TX_DATA02 0x401F8168U, 0x3U, 0x401F859CU, 0x1U, 0x401F8358U
#define IOMUXC_GPIO_B0_11_FLEXIO2_FLEXIO11 0x401F8168U, 0x4U, 0, 0, 0x401F8358U
#define IOMUXC_GPIO_B0_11_GPIO2_IO11 0x401F8168U, 0x5U, 0, 0, 0x401F8358U
#define IOMUXC_GPIO_B0_11_SRC_BOOT_CFG07 0x401F8168U, 0x6U, 0, 0, 0x401F8358U
#define IOMUXC_GPIO_B0_11_ENET2_COL 0x401F8168U, 0x8U, 0, 0, 0x401F8358U

#define IOMUXC_GPIO_B0_12_LCD_DATA08 0x401F816CU, 0x0U, 0, 0, 0x401F835CU
#define IOMUXC_GPIO_B0_12_XBAR1_INOUT10 0x401F816CU, 0x1U, 0, 0, 0x401F835CU
#define IOMUXC_GPIO_B0_12_ARM_TRACE_CLK 0x401F816CU, 0x2U, 0, 0, 0x401F835CU
#define IOMUXC_GPIO_B0_12_SAI1_TX_DATA01 0x401F816CU, 0x3U, 0x401F85A0U, 0x1U, 0x401F835CU
#define IOMUXC_GPIO_B0_12_FLEXIO2_FLEXIO12 0x401F816CU, 0x4U, 0, 0, 0x401F835CU
#define IOMUXC_GPIO_B0_12_GPIO2_IO12 0x401F816CU, 0x5U, 0, 0, 0x401F835CU
#define IOMUXC_GPIO_B0_12_SRC_BOOT_CFG08 0x401F816CU, 0x6U, 0, 0, 0x401F835CU
#define IOMUXC_GPIO_B0_12_ENET2_TDATA00 0x401F816CU, 0x8U, 0, 0, 0x401F835CU

#define IOMUXC_GPIO_B0_13_LCD_DATA09 0x401F8170U, 0x0U, 0, 0, 0x401F8360U
#define IOMUXC_GPIO_B0_13_XBAR1_INOUT11 0x401F8170U, 0x1U, 0, 0, 0x401F8360U
#define IOMUXC_GPIO_B0_13_ARM_TRACE_SWO 0x401F8170U, 0x2U, 0, 0, 0x401F8360U
#define IOMUXC_GPIO_B0_13_SAI1_MCLK 0x401F8170U, 0x3U, 0x401F858CU, 0x2U, 0x401F8360U
#define IOMUXC_GPIO_B0_13_FLEXIO2_FLEXIO13 0x401F8170U, 0x4U, 0, 0, 0x401F8360U
#define IOMUXC_GPIO_B0_13_GPIO2_IO13 0x401F8170U, 0x5U, 0, 0, 0x401F8360U
#define IOMUXC_GPIO_B0_13_SRC_BOOT_CFG09 0x401F8170U, 0x6U, 0, 0, 0x401F8360U
#define IOMUXC_GPIO_B0_13_ENET2_TDATA01 0x401F8170U, 0x8U, 0, 0, 0x401F8360U

#define IOMUXC_GPIO_B0_14_LCD_DATA10 0x401F8174U, 0x0U, 0, 0, 0x401F8364U
#define IOMUXC_GPIO_B0_14_XBAR1_INOUT12 0x401F8174U, 0x1U, 0, 0, 0x401F8364U
#define IOMUXC_GPIO_B0_14_ARM_TXEV 0x401F8174U, 0x2U, 0, 0, 0x401F8364U
#define IOMUXC_GPIO_B0_14_SAI1_RX_SYNC 0x401F8174U, 0x3U, 0x401F85A4U, 0x2U, 0x401F8364U
#define IOMUXC_GPIO_B0_14_FLEXIO2_FLEXIO14 0x401F8174U, 0x4U, 0, 0, 0x401F8364U
#define IOMUXC_GPIO_B0_14_GPIO2_IO14 0x401F8174U, 0x5U, 0, 0, 0x401F8364U
#define IOMUXC_GPIO_B0_14_SRC_BOOT_CFG10 0x401F8174U, 0x6U, 0, 0, 0x401F8364U
#define IOMUXC_GPIO_B0_14_ENET2_TX_EN 0x401F8174U, 0x8U, 0, 0, 0x401F8364U

#define IOMUXC_GPIO_B0_15_LCD_DATA11 0x401F8178U, 0x0U, 0, 0, 0x401F8368U
#define IOMUXC_GPIO_B0_15_XBAR1_INOUT13 0x401F8178U, 0x1U, 0, 0, 0x401F8368U
#define IOMUXC_GPIO_B0_15_ARM_RXEV 0x401F8178U, 0x2U, 0, 0, 0x401F8368U
#define IOMUXC_GPIO_B0_15_SAI1_RX_BCLK 0x401F8178U, 0x3U, 0x401F8590U, 0x2U, 0x401F8368U
#define IOMUXC_GPIO_B0_15_FLEXIO2_FLEXIO15 0x401F8178U, 0x4U, 0, 0, 0x401F8368U
#define IOMUXC_GPIO_B0_15_GPIO2_IO15 0x401F8178U, 0x5U, 0, 0, 0x401F8368U
#define IOMUXC_GPIO_B0_15_SRC_BOOT_CFG11 0x401F8178U, 0x6U, 0, 0, 0x401F8368U
#define IOMUXC_GPIO_B0_15_ENET2_TX_CLK 0x401F8178U, 0x8U, 0x401F8728U, 0x2U, 0x401F8368U
#define IOMUXC_GPIO_B0_15_ENET2_REF_CLK2 0x401F8178U, 0x9U, 0x401F870CU, 0x2U, 0x401F8368U

#define IOMUXC_GPIO_B1_00_LCD_DATA12 0x401F817CU, 0x0U, 0, 0, 0x401F836CU
#define IOMUXC_GPIO_B1_00_XBAR1_INOUT14 0x401F817CU, 0x1U, 0x401F8644U, 0x1U, 0x401F836CU
#define IOMUXC_GPIO_B1_00_LPUART4_TX 0x401F817CU, 0x2U, 0x401F8544U, 0x2U, 0x401F836CU
#define IOMUXC_GPIO_B1_00_SAI1_RX_DATA00 0x401F817CU, 0x3U, 0x401F8594U, 0x2U, 0x401F836CU
#define IOMUXC_GPIO_B1_00_FLEXIO2_FLEXIO16 0x401F817CU, 0x4U, 0, 0, 0x401F836CU
#define IOMUXC_GPIO_B1_00_GPIO2_IO16 0x401F817CU, 0x5U, 0, 0, 0x401F836CU
#define IOMUXC_GPIO_B1_00_FLEXPWM1_PWMA03 0x401F817CU, 0x6U, 0x401F8454U, 0x4U, 0x401F836CU
#define IOMUXC_GPIO_B1_00_ENET2_RX_ER 0x401F817CU, 0x8U, 0x401F8720U, 0x2U, 0x401F836CU
#define IOMUXC_GPIO_B1_00_FLEXIO3_FLEXIO16 0x401F817CU, 0x9U, 0, 0, 0x401F836CU

#define IOMUXC_GPIO_B1_01_LCD_DATA13 0x401F8180U, 0x0U, 0, 0, 0x401F8370U
#define IOMUXC_GPIO_B1_01_XBAR1_INOUT15 0x401F8180U, 0x1U, 0x401F8648U, 0x1U, 0x401F8370U
#define IOMUXC_GPIO_B1_01_LPUART4_RX 0x401F8180U, 0x2U, 0x401F8540U, 0x2U, 0x401F8370U
#define IOMUXC_GPIO_B1_01_SAI1_TX_DATA00 0x401F8180U, 0x3U, 0, 0, 0x401F8370U
#define IOMUXC_GPIO_B1_01_FLEXIO2_FLEXIO17 0x401F8180U, 0x4U, 0, 0, 0x401F8370U
#define IOMUXC_GPIO_B1_01_GPIO2_IO17 0x401F8180U, 0x5U, 0, 0, 0x401F8370U
#define IOMUXC_GPIO_B1_01_FLEXPWM1_PWMB03 0x401F8180U, 0x6U, 0x401F8464U, 0x4U, 0x401F8370U
#define IOMUXC_GPIO_B1_01_ENET2_RDATA00 0x401F8180U, 0x8U, 0x401F8714U, 0x2U, 0x401F8370U
#define IOMUXC_GPIO_B1_01_FLEXIO3_FLEXIO17 0x401F8180U, 0x9U, 0, 0, 0x401F8370U

#define IOMUXC_GPIO_B1_02_LCD_DATA14 0x401F8184U, 0x0U, 0, 0, 0x401F8374U
#define IOMUXC_GPIO_B1_02_XBAR1_INOUT16 0x401F8184U, 0x1U, 0x401F864CU, 0x1U, 0x401F8374U
#define IOMUXC_GPIO_B1_02_LPSPI4_PCS2 0x401F8184U, 0x2U, 0, 0, 0x401F8374U
#define IOMUXC_GPIO_B1_02_SAI1_TX_BCLK 0x401F8184U, 0x3U, 0x401F85A8U, 0x2U, 0x401F8374U
#define IOMUXC_GPIO_B1_02_FLEXIO2_FLEXIO18 0x401F8184U, 0x4U, 0, 0, 0x401F8374U
#define IOMUXC_GPIO_B1_02_GPIO2_IO18 0x401F8184U, 0x5U, 0, 0, 0x401F8374U
#define IOMUXC_GPIO_B1_02_FLEXPWM2_PWMA03 0x401F8184U, 0x6U, 0x401F8474U, 0x4U, 0x401F8374U
#define IOMUXC_GPIO_B1_02_ENET2_RDATA01 0x401F8184U, 0x8U, 0x401F8718U, 0x2U, 0x401F8374U
#define IOMUXC_GPIO_B1_02_FLEXIO3_FLEXIO18 0x401F8184U, 0x9U, 0, 0, 0x401F8374U

#define IOMUXC_GPIO_B1_03_LCD_DATA15 0x401F8188U, 0x0U, 0, 0, 0x401F8378U
#define IOMUXC_GPIO_B1_03_XBAR1_INOUT17 0x401F8188U, 0x1U, 0x401F862CU, 0x3U, 0x401F8378U
#define IOMUXC_GPIO_B1_03_LPSPI4_PCS1 0x401F8188U, 0x2U, 0, 0, 0x401F8378U
#define IOMUXC_GPIO_B1_03_SAI1_TX_SYNC 0x401F8188U, 0x3U, 0x401F85ACU, 0x2U, 0x401F8378U
#define IOMUXC_GPIO_B1_03_FLEXIO2_FLEXIO19 0x401F8188U, 0x4U, 0, 0, 0x401F8378U
#define IOMUXC_GPIO_B1_03_GPIO2_IO19 0x401F8188U, 0x5U, 0, 0, 0x401F8378U
#define IOMUXC_GPIO_B1_03_FLEXPWM2_PWMB03 0x401F8188U, 0x6U, 0x401F8484U, 0x3U, 0x401F8378U
#define IOMUXC_GPIO_B1_03_ENET2_RX_EN 0x401F8188U, 0x8U, 0x401F871CU, 0x2U, 0x401F8378U
#define IOMUXC_GPIO_B1_03_FLEXIO3_FLEXIO19 0x401F8188U, 0x9U, 0, 0, 0x401F8378U

#define IOMUXC_GPIO_B1_04_LCD_DATA16 0x401F818CU, 0x0U, 0, 0, 0x401F837CU
#define IOMUXC_GPIO_B1_04_LPSPI4_PCS0 0x401F818CU, 0x1U, 0x401F851CU, 0x1U, 0x401F837CU
#define IOMUXC_GPIO_B1_04_CSI_DATA15 0x401F818CU, 0x2U, 0, 0, 0x401F837CU
#define IOMUXC_GPIO_B1_04_ENET_RX_DATA00 0x401F818CU, 0x3U, 0x401F8434U, 0x1U, 0x401F837CU
#define IOMUXC_GPIO_B1_04_FLEXIO2_FLEXIO20 0x401F818CU, 0x4U, 0, 0, 0x401F837CU
#define IOMUXC_GPIO_B1_04_GPIO2_IO20 0x401F818CU, 0x5U, 0, 0, 0x401F837CU
#define IOMUXC_GPIO_B1_04_GPT1_CLK 0x401F818CU, 0x8U, 0x401F8760U, 0x1U, 0x401F837CU
#define IOMUXC_GPIO_B1_04_FLEXIO3_FLEXIO20 0x401F818CU, 0x9U, 0, 0, 0x401F837CU

#define IOMUXC_GPIO_B1_05_LCD_DATA17 0x401F8190U, 0x0U, 0, 0, 0x401F8380U
#define IOMUXC_GPIO_B1_05_LPSPI4_SDI 0x401F8190U, 0x1U, 0x401F8524U, 0x1U, 0x401F8380U
#define IOMUXC_GPIO_B1_05_CSI_DATA14 0x401F8190U, 0x2U, 0, 0, 0x401F8380U
#define IOMUXC_GPIO_B1_05_ENET_RX_DATA01 0x401F8190U, 0x3U, 0x401F8438U, 0x1U, 0x401F8380U
#define IOMUXC_GPIO_B1_05_FLEXIO2_FLEXIO21 0x401F8190U, 0x4U, 0, 0, 0x401F8380U
#define IOMUXC_GPIO_B1_05_GPIO2_IO21 0x401F8190U, 0x5U, 0, 0, 0x401F8380U
#define IOMUXC_GPIO_B1_05_GPT1_CAPTURE1 0x401F8190U, 0x8U, 0x401F8758U, 0x1U, 0x401F8380U
#define IOMUXC_GPIO_B1_05_FLEXIO3_FLEXIO21 0x401F8190U, 0x9U, 0, 0, 0x401F8380U

#define IOMUXC_GPIO_B1_06_LCD_DATA18 0x401F8194U, 0x0U, 0, 0, 0x401F8384U
#define IOMUXC_GPIO_B1_06_LPSPI4_SDO 0x401F8194U, 0x1U, 0x401F8528U, 0x1U, 0x401F8384U
#define IOMUXC_GPIO_B1_06_CSI_DATA13 0x401F8194U, 0x2U, 0, 0, 0x401F8384U
#define IOMUXC_GPIO_B1_06_ENET_RX_EN 0x401F8194U, 0x3U, 0x401F843CU, 0x1U, 0x401F8384U
#define IOMUXC_GPIO_B1_06_FLEXIO2_FLEXIO22 0x401F8194U, 0x4U, 0, 0, 0x401F8384U
#define IOMUXC_GPIO_B1_06_GPIO2_IO22 0x401F8194U, 0x5U, 0, 0, 0x401F8384U
#define IOMUXC_GPIO_B1_06_GPT1_CAPTURE2 0x401F8194U, 0x8U, 0x401F875CU, 0x1U, 0x401F8384U
#define IOMUXC_GPIO_B1_06_FLEXIO3_FLEXIO22 0x401F8194U, 0x9U, 0, 0, 0x401F8384U

#define IOMUXC_GPIO_B1_07_LCD_DATA19 0x401F8198U, 0x0U, 0, 0, 0x401F8388U
#define IOMUXC_GPIO_B1_07_LPSPI4_SCK 0x401F8198U, 0x1U, 0x401F8520U, 0x1U, 0x401F8388U
#define IOMUXC_GPIO_B1_07_CSI_DATA12 0x401F8198U, 0x2U, 0, 0, 0x401F8388U
#define IOMUXC_GPIO_B1_07_ENET_TX_DATA00 0x401F8198U, 0x3U, 0, 0, 0x401F8388U
#define IOMUXC_GPIO_B1_07_FLEXIO2_FLEXIO23 0x401F8198U, 0x4U, 0, 0, 0x401F8388U
#define IOMUXC_GPIO_B1_07_GPIO2_IO23 0x401F8198U, 0x5U, 0, 0, 0x401F8388U
#define IOMUXC_GPIO_B1_07_GPT1_COMPARE1 0x401F8198U, 0x8U, 0, 0, 0x401F8388U
#define IOMUXC_GPIO_B1_07_FLEXIO3_FLEXIO23 0x401F8198U, 0x9U, 0, 0, 0x401F8388U

#define IOMUXC_GPIO_B1_08_LCD_DATA20 0x401F819CU, 0x0U, 0, 0, 0x401F838CU
#define IOMUXC_GPIO_B1_08_QTIMER1_TIMER3 0x401F819CU, 0x1U, 0, 0, 0x401F838CU
#define IOMUXC_GPIO_B1_08_CSI_DATA11 0x401F819CU, 0x2U, 0, 0, 0x401F838CU
#define IOMUXC_GPIO_B1_08_ENET_TX_DATA01 0x401F819CU, 0x3U, 0, 0, 0x401F838CU
#define IOMUXC_GPIO_B1_08_FLEXIO2_FLEXIO24 0x401F819CU, 0x4U, 0, 0, 0x401F838CU
#define IOMUXC_GPIO_B1_08_GPIO2_IO24 0x401F819CU, 0x5U, 0, 0, 0x401F838CU
#define IOMUXC_GPIO_B1_08_FLEXCAN2_TX 0x401F819CU, 0x6U, 0, 0, 0x401F838CU
#define IOMUXC_GPIO_B1_08_GPT1_COMPARE2 0x401F819CU, 0x8U, 0, 0, 0x401F838CU
#define IOMUXC_GPIO_B1_08_FLEXIO3_FLEXIO24 0x401F819CU, 0x9U, 0, 0, 0x401F838CU

#define IOMUXC_GPIO_B1_09_LCD_DATA21 0x401F81A0U, 0x0U, 0, 0, 0x401F8390U
#define IOMUXC_GPIO_B1_09_QTIMER2_TIMER3 0x401F81A0U, 0x1U, 0x401F8578U, 0x1U, 0x401F8390U
#define IOMUXC_GPIO_B1_09_CSI_DATA10 0x401F81A0U, 0x2U, 0, 0, 0x401F8390U
#define IOMUXC_GPIO_B1_09_ENET_TX_EN 0x401F81A0U, 0x3U, 0, 0, 0x401F8390U
#define IOMUXC_GPIO_B1_09_FLEXIO2_FLEXIO25 0x401F81A0U, 0x4U, 0, 0, 0x401F8390U
#define IOMUXC_GPIO_B1_09_GPIO2_IO25 0x401F81A0U, 0x5U, 0, 0, 0x401F8390U
#define IOMUXC_GPIO_B1_09_FLEXCAN2_RX 0x401F81A0U, 0x6U, 0x401F8450U, 0x3U, 0x401F8390U
#define IOMUXC_GPIO_B1_09_GPT1_COMPARE3 0x401F81A0U, 0x8U, 0, 0, 0x401F8390U
#define IOMUXC_GPIO_B1_09_FLEXIO3_FLEXIO25 0x401F81A0U, 0x9U, 0, 0, 0x401F8390U

#define IOMUXC_GPIO_B1_10_LCD_DATA22 0x401F81A4U, 0x0U, 0, 0, 0x401F8394U
#define IOMUXC_GPIO_B1_10_QTIMER3_TIMER3 0x401F81A4U, 0x1U, 0x401F8588U, 0x2U, 0x401F8394U
#define IOMUXC_GPIO_B1_10_CSI_DATA00 0x401F81A4U, 0x2U, 0, 0, 0x401F8394U
#define IOMUXC_GPIO_B1_10_ENET_TX_CLK 0x401F81A4U, 0x3U, 0x401F8448U, 0x1U, 0x401F8394U
#define IOMUXC_GPIO_B1_10_FLEXIO2_FLEXIO26 0x401F81A4U, 0x4U, 0, 0, 0x401F8394U
#define IOMUXC_GPIO_B1_10_GPIO2_IO26 0x401F81A4U, 0x5U, 0, 0, 0x401F8394U
#define IOMUXC_GPIO_B1_10_ENET_REF_CLK 0x401F81A4U, 0x6U, 0x401F842CU, 0x1U, 0x401F8394U
#define IOMUXC_GPIO_B1_10_FLEXIO3_FLEXIO26 0x401F81A4U, 0x9U, 0, 0, 0x401F8394U

#define IOMUXC_GPIO_B1_11_LCD_DATA23 0x401F81A8U, 0x0U, 0, 0, 0x401F8398U
#define IOMUXC_GPIO_B1_11_QTIMER4_TIMER3 0x401F81A8U, 0x1U, 0, 0, 0x401F8398U
#define IOMUXC_GPIO_B1_11_CSI_DATA01 0x401F81A8U, 0x2U, 0, 0, 0x401F8398U
#define IOMUXC_GPIO_B1_11_ENET_RX_ER 0x401F81A8U, 0x3U, 0x401F8440U, 0x1U, 0x401F8398U
#define IOMUXC_GPIO_B1_11_FLEXIO2_FLEXIO27 0x401F81A8U, 0x4U, 0, 0, 0x401F8398U
#define IOMUXC_GPIO_B1_11_GPIO2_IO27 0x401F81A8U, 0x5U, 0, 0, 0x401F8398U
#define IOMUXC_GPIO_B1_11_LPSPI4_PCS3 0x401F81A8U, 0x6U, 0, 0, 0x401F8398U
#define IOMUXC_GPIO_B1_11_FLEXIO3_FLEXIO27 0x401F81A8U, 0x9U, 0, 0, 0x401F8398U

#define IOMUXC_GPIO_B1_12_LPUART5_TX 0x401F81ACU, 0x1U, 0x401F854CU, 0x1U, 0x401F839CU
#define IOMUXC_GPIO_B1_12_CSI_PIXCLK 0x401F81ACU, 0x2U, 0x401F8424U, 0x1U, 0x401F839CU
#define IOMUXC_GPIO_B1_12_ENET_1588_EVENT0_IN 0x401F81ACU, 0x3U, 0x401F8444U, 0x2U, 0x401F839CU
#define IOMUXC_GPIO_B1_12_FLEXIO2_FLEXIO28 0x401F81ACU, 0x4U, 0, 0, 0x401F839CU
#define IOMUXC_GPIO_B1_12_GPIO2_IO28 0x401F81ACU, 0x5U, 0, 0, 0x401F839CU
#define IOMUXC_GPIO_B1_12_USDHC1_CD_B 0x401F81ACU, 0x6U, 0x401F85D4U, 0x2U, 0x401F839CU
#define IOMUXC_GPIO_B1_12_FLEXIO3_FLEXIO28 0x401F81ACU, 0x9U, 0, 0, 0x401F839CU

#define IOMUXC_GPIO_B1_13_WDOG1_B 0x401F81B0U, 0x0U, 0, 0, 0x401F83A0U
#define IOMUXC_GPIO_B1_13_LPUART5_RX 0x401F81B0U, 0x1U, 0x401F8548U, 0x1U, 0x401F83A0U
#define IOMUXC_GPIO_B1_13_CSI_VSYNC 0x401F81B0U, 0x2U, 0x401F8428U, 0x2U, 0x401F83A0U
#define IOMUXC_GPIO_B1_13_ENET_1588_EVENT0_OUT 0x401F81B0U, 0x3U, 0, 0, 0x401F83A0U
#define IOMUXC_GPIO_B1_13_FLEXIO2_FLEXIO29 0x401F81B0U, 0x4U, 0, 0, 0x401F83A0U
#define IOMUXC_GPIO_B1_13_GPIO2_IO29 0x401F81B0U, 0x5U, 0, 0, 0x401F83A0U
#define IOMUXC_GPIO_B1_13_USDHC1_WP 0x401F81B0U, 0x6U, 0x401F85D8U, 0x3U, 0x401F83A0U
#define IOMUXC_GPIO_B1_13_SEMC_DQS4 0x401F81B0U, 0x8U, 0x401F8788U, 0x3U, 0x401F83A0U
#define IOMUXC_GPIO_B1_13_FLEXIO3_FLEXIO29 0x401F81B0U, 0x9U, 0, 0, 0x401F83A0U

#define IOMUXC_GPIO_B1_14_ENET_MDC 0x401F81B4U, 0x0U, 0, 0, 0x401F83A4U
#define IOMUXC_GPIO_B1_14_FLEXPWM4_PWMA02 0x401F81B4U, 0x1U, 0x401F849CU, 0x1U, 0x401F83A4U
#define IOMUXC_GPIO_B1_14_CSI_HSYNC 0x401F81B4U, 0x2U, 0x401F8420U, 0x2U, 0x401F83A4U
#define IOMUXC_GPIO_B1_14_XBAR1_IN02 0x401F81B4U, 0x3U, 0x401F860CU, 0x1U, 0x401F83A4U
#define IOMUXC_GPIO_B1_14_FLEXIO2_FLEXIO30 0x401F81B4U, 0x4U, 0, 0, 0x401F83A4U
#define IOMUXC_GPIO_B1_14_GPIO2_IO30 0x401F81B4U, 0x5U, 0, 0, 0x401F83A4U
#define IOMUXC_GPIO_B1_14_USDHC1_VSELECT 0x401F81B4U, 0x6U, 0, 0, 0x401F83A4U
#define IOMUXC_GPIO_B1_14_ENET2_TDATA00 0x401F81B4U, 0x8U, 0, 0, 0x401F83A4U
#define IOMUXC_GPIO_B1_14_FLEXIO3_FLEXIO30 0x401F81B4U, 0x9U, 0, 0, 0x401F83A4U

#define IOMUXC_GPIO_B1_15_ENET_MDIO 0x401F81B8U, 0x0U, 0x401F8430U, 0x2U, 0x401F83A8U
#define IOMUXC_GPIO_B1_15_FLEXPWM4_PWMA03 0x401F81B8U, 0x1U, 0x401F84A0U, 0x1U, 0x401F83A8U
#define IOMUXC_GPIO_B1_15_CSI_MCLK 0x401F81B8U, 0x2U, 0, 0, 0x401F83A8U
#define IOMUXC_GPIO_B1_15_XBAR1_IN03 0x401F81B8U, 0x3U, 0x401F8610U, 0x1U, 0x401F83A8U
#define IOMUXC_GPIO_B1_15_FLEXIO2_FLEXIO31 0x401F81B8U, 0x4U, 0, 0, 0x401F83A8U
#define IOMUXC_GPIO_B1_15_GPIO2_IO31 0x401F81B8U, 0x5U, 0, 0, 0x401F83A8U
#define IOMUXC_GPIO_B1_15_USDHC1_RESET_B 0x401F81B8U, 0x6U, 0, 0, 0x401F83A8U
#define IOMUXC_GPIO_B1_15_ENET2_TDATA01 0x401F81B8U, 0x8U, 0, 0, 0x401F83A8U
#define IOMUXC_GPIO_B1_15_FLEXIO3_FLEXIO31 0x401F81B8U, 0x9U, 0, 0, 0x401F83A8U

#define IOMUXC_GPIO_SD_B0_00_USDHC1_CMD 0x401F81BCU, 0x0U, 0, 0, 0x401F83ACU
#define IOMUXC_GPIO_SD_B0_00_FLEXPWM1_PWMA00 0x401F81BCU, 0x1U, 0x401F8458U, 0x1U, 0x401F83ACU
#define IOMUXC_GPIO_SD_B0_00_LPI2C3_SCL 0x401F81BCU, 0x2U, 0x401F84DCU, 0x1U, 0x401F83ACU
#define IOMUXC_GPIO_SD_B0_00_XBAR1_INOUT04 0x401F81BCU, 0x3U, 0x401F8614U, 0x1U, 0x401F83ACU
#define IOMUXC_GPIO_SD_B0_00_LPSPI1_SCK 0x401F81BCU, 0x4U, 0x401F84F0U, 0x1U, 0x401F83ACU
#define IOMUXC_GPIO_SD_B0_00_GPIO3_IO12 0x401F81BCU, 0x5U, 0, 0, 0x401F83ACU
#define IOMUXC_GPIO_SD_B0_00_FLEXSPIA_SS1_B 0x401F81BCU, 0x6U, 0, 0, 0x401F83ACU
#define IOMUXC_GPIO_SD_B0_00_ENET2_TX_EN 0x401F81BCU, 0x8U, 0, 0, 0x401F83ACU
#define IOMUXC_GPIO_SD_B0_00_SEMC_DQS4 0x401F81BCU, 0x9U, 0x401F8788U, 0x0U, 0x401F83ACU

#define IOMUXC_GPIO_SD_B0_01_USDHC1_CLK 0x401F81C0U, 0x0U, 0, 0, 0x401F83B0U
#define IOMUXC_GPIO_SD_B0_01_FLEXPWM1_PWMB00 0x401F81C0U, 0x1U, 0x401F8468U, 0x1U, 0x401F83B0U
#define IOMUXC_GPIO_SD_B0_01_LPI2C3_SDA 0x401F81C0U, 0x2U, 0x401F84E0U, 0x1U, 0x401F83B0U
#define IOMUXC_GPIO_SD_B0_01_XBAR1_INOUT05 0x401F81C0U, 0x3U, 0x401F8618U, 0x1U, 0x401F83B0U
#define IOMUXC_GPIO_SD_B0_01_LPSPI1_PCS0 0x401F81C0U, 0x4U, 0x401F84ECU, 0x0U, 0x401F83B0U
#define IOMUXC_GPIO_SD_B0_01_GPIO3_IO13 0x401F81C0U, 0x5U, 0, 0, 0x401F83B0U
#define IOMUXC_GPIO_SD_B0_01_FLEXSPIB_SS1_B 0x401F81C0U, 0x6U, 0, 0, 0x401F83B0U
#define IOMUXC_GPIO_SD_B0_01_ENET2_TX_CLK 0x401F81C0U, 0x8U, 0x401F8728U, 0x1U, 0x401F83B0U
#define IOMUXC_GPIO_SD_B0_01_ENET2_REF_CLK2 0x401F81C0U, 0x9U, 0x401F870CU, 0x1U, 0x401F83B0U

#define IOMUXC_GPIO_SD_B0_02_USDHC1_DATA0 0x401F81C4U, 0x0U, 0, 0, 0x401F83B4U
#define IOMUXC_GPIO_SD_B0_02_FLEXPWM1_PWMA01 0x401F81C4U, 0x1U, 0x401F845CU, 0x1U, 0x401F83B4U
#define IOMUXC_GPIO_SD_B0_02_LPUART8_CTS_B 0x401F81C4U, 0x2U, 0, 0, 0x401F83B4U
#define IOMUXC_GPIO_SD_B0_02_XBAR1_INOUT06 0x401F81C4U, 0x3U, 0x401F861CU, 0x1U, 0x401F83B4U
#define IOMUXC_GPIO_SD_B0_02_LPSPI1_SDO 0x401F81C4U, 0x4U, 0x401F84F8U, 0x1U, 0x401F83B4U
#define IOMUXC_GPIO_SD_B0_02_GPIO3_IO14 0x401F81C4U, 0x5U, 0, 0, 0x401F83B4U
#define IOMUXC_GPIO_SD_B0_02_ENET2_RX_ER 0x401F81C4U, 0x8U, 0x401F8720U, 0x1U, 0x401F83B4U
#define IOMUXC_GPIO_SD_B0_02_SEMC_CLK5 0x401F81C4U, 0x9U, 0, 0, 0x401F83B4U

#define IOMUXC_GPIO_SD_B0_03_USDHC1_DATA1 0x401F81C8U, 0x0U, 0, 0, 0x401F83B8U
#define IOMUXC_GPIO_SD_B0_03_FLEXPWM1_PWMB01 0x401F81C8U, 0x1U, 0x401F846CU, 0x1U, 0x401F83B8U
#define IOMUXC_GPIO_SD_B0_03_LPUART8_RTS_B 0x401F81C8U, 0x2U, 0, 0, 0x401F83B8U
#define IOMUXC_GPIO_SD_B0_03_XBAR1_INOUT07 0x401F81C8U, 0x3U, 0x401F8620U, 0x1U, 0x401F83B8U
#define IOMUXC_GPIO_SD_B0_03_LPSPI1_SDI 0x401F81C8U, 0x4U, 0x401F84F4U, 0x1U, 0x401F83B8U
#define IOMUXC_GPIO_SD_B0_03_GPIO3_IO15 0x401F81C8U, 0x5U, 0, 0, 0x401F83B8U
#define IOMUXC_GPIO_SD_B0_03_ENET2_RDATA00 0x401F81C8U, 0x8U, 0x401F8714U, 0x1U, 0x401F83B8U
#define IOMUXC_GPIO_SD_B0_03_SEMC_CLK6 0x401F81C8U, 0x9U, 0, 0, 0x401F83B8U

#define IOMUXC_GPIO_SD_B0_04_USDHC1_DATA2 0x401F81CCU, 0x0U, 0, 0, 0x401F83BCU
#define IOMUXC_GPIO_SD_B0_04_FLEXPWM1_PWMA02 0x401F81CCU, 0x1U, 0x401F8460U, 0x1U, 0x401F83BCU
#define IOMUXC_GPIO_SD_B0_04_LPUART8_TX 0x401F81CCU, 0x2U, 0x401F8564U, 0x0U, 0x401F83BCU
#define IOMUXC_GPIO_SD_B0_04_XBAR1_INOUT08 0x401F81CCU, 0x3U, 0x401F8624U, 0x1U, 0x401F83BCU
#define IOMUXC_GPIO_SD_B0_04_FLEXSPIB_SS0_B 0x401F81CCU, 0x4U, 0, 0, 0x401F83BCU
#define IOMUXC_GPIO_SD_B0_04_GPIO3_IO16 0x401F81CCU, 0x5U, 0, 0, 0x401F83BCU
#define IOMUXC_GPIO_SD_B0_04_CCM_CLKO1 0x401F81CCU, 0x6U, 0, 0, 0x401F83BCU
#define IOMUXC_GPIO_SD_B0_04_ENET2_RDATA01 0x401F81CCU, 0x8U, 0x401F8718U, 0x1U, 0x401F83BCU

#define IOMUXC_GPIO_SD_B0_05_USDHC1_DATA3 0x401F81D0U, 0x0U, 0, 0, 0x401F83C0U
#define IOMUXC_GPIO_SD_B0_05_FLEXPWM1_PWMB02 0x401F81D0U, 0x1U, 0x401F8470U, 0x1U, 0x401F83C0U
#define IOMUXC_GPIO_SD_B0_05_LPUART8_RX 0x401F81D0U, 0x2U, 0x401F8560U, 0x0U, 0x401F83C0U
#define IOMUXC_GPIO_SD_B0_05_XBAR1_INOUT09 0x401F81D0U, 0x3U, 0x401F8628U, 0x1U, 0x401F83C0U
#define IOMUXC_GPIO_SD_B0_05_FLEXSPIB_DQS 0x401F81D0U, 0x4U, 0, 0, 0x401F83C0U
#define IOMUXC_GPIO_SD_B0_05_GPIO3_IO17 0x401F81D0U, 0x5U, 0, 0, 0x401F83C0U
#define IOMUXC_GPIO_SD_B0_05_CCM_CLKO2 0x401F81D0U, 0x6U, 0, 0, 0x401F83C0U
#define IOMUXC_GPIO_SD_B0_05_ENET2_RX_EN 0x401F81D0U, 0x8U, 0x401F871CU, 0x1U, 0x401F83C0U

#define IOMUXC_GPIO_SD_B1_00_USDHC2_DATA3 0x401F81D4U, 0x0U, 0x401F85F4U, 0x0U, 0x401F83C4U
#define IOMUXC_GPIO_SD_B1_00_FLEXSPIB_DATA03 0x401F81D4U, 0x1U, 0x401F84C4U, 0x0U, 0x401F83C4U
#define IOMUXC_GPIO_SD_B1_00_FLEXPWM1_PWMA03 0x401F81D4U, 0x2U, 0x401F8454U, 0x0U, 0x401F83C4U
#define IOMUXC_GPIO_SD_B1_00_SAI1_TX_DATA03 0x401F81D4U, 0x3U, 0x401F8598U, 0x0U, 0x401F83C4U
#define IOMUXC_GPIO_SD_B1_00_LPUART4_TX 0x401F81D4U, 0x4U, 0x401F8544U, 0x0U, 0x401F83C4U
#define IOMUXC_GPIO_SD_B1_00_GPIO3_IO00 0x401F81D4U, 0x5U, 0, 0, 0x401F83C4U
#define IOMUXC_GPIO_SD_B1_00_SAI3_RX_DATA 0x401F81D4U, 0x8U, 0x401F8778U, 0x1U, 0x401F83C4U

#define IOMUXC_GPIO_SD_B1_01_USDHC2_DATA2 0x401F81D8U, 0x0U, 0x401F85F0U, 0x0U, 0x401F83C8U
#define IOMUXC_GPIO_SD_B1_01_FLEXSPIB_DATA02 0x401F81D8U, 0x1U, 0x401F84C0U, 0x0U, 0x401F83C8U
#define IOMUXC_GPIO_SD_B1_01_FLEXPWM1_PWMB03 0x401F81D8U, 0x2U, 0x401F8464U, 0x0U, 0x401F83C8U
#define IOMUXC_GPIO_SD_B1_01_SAI1_TX_DATA02 0x401F81D8U, 0x3U, 0x401F859CU, 0x0U, 0x401F83C8U
#define IOMUXC_GPIO_SD_B1_01_LPUART4_RX 0x401F81D8U, 0x4U, 0x401F8540U, 0x0U, 0x401F83C8U
#define IOMUXC_GPIO_SD_B1_01_GPIO3_IO01 0x401F81D8U, 0x5U, 0, 0, 0x401F83C8U
#define IOMUXC_GPIO_SD_B1_01_SAI3_TX_DATA 0x401F81D8U, 0x8U, 0, 0, 0x401F83C8U

#define IOMUXC_GPIO_SD_B1_02_USDHC2_DATA1 0x401F81DCU, 0x0U, 0x401F85ECU, 0x0U, 0x401F83CCU
#define IOMUXC_GPIO_SD_B1_02_FLEXSPIB_DATA01 0x401F81DCU, 0x1U, 0x401F84BCU, 0x0U, 0x401F83CCU
#define IOMUXC_GPIO_SD_B1_02_FLEXPWM2_PWMA03 0x401F81DCU, 0x2U, 0x401F8474U, 0x0U, 0x401F83CCU
#define IOMUXC_GPIO_SD_B1_02_SAI1_TX_DATA01 0x401F81DCU, 0x3U, 0x401F85A0U, 0x0U, 0x401F83CCU
#define IOMUXC_GPIO_SD_B1_02_FLEXCAN1_TX 0x401F81DCU, 0x4U, 0, 0, 0x401F83CCU
#define IOMUXC_GPIO_SD_B1_02_GPIO3_IO02 0x401F81DCU, 0x5U, 0, 0, 0x401F83CCU
#define IOMUXC_GPIO_SD_B1_02_CCM_WAIT 0x401F81DCU, 0x6U, 0, 0, 0x401F83CCU
#define IOMUXC_GPIO_SD_B1_02_SAI3_TX_SYNC 0x401F81DCU, 0x8U, 0x401F8784U, 0x1U, 0x401F83CCU

#define IOMUXC_GPIO_SD_B1_03_USDHC2_DATA0 0x401F81E0U, 0x0U, 0x401F85E8U, 0x0U, 0x401F83D0U
#define IOMUXC_GPIO_SD_B1_03_FLEXSPIB_DATA00 0x401F81E0U, 0x1U, 0x401F84B8U, 0x0U, 0x401F83D0U
#define IOMUXC_GPIO_SD_B1_03_FLEXPWM2_PWMB03 0x401F81E0U, 0x2U, 0x401F8484U, 0x0U, 0x401F83D0U
#define IOMUXC_GPIO_SD_B1_03_SAI1_MCLK 0x401F81E0U, 0x3U, 0x401F858CU, 0x0U, 0x401F83D0U
#define IOMUXC_GPIO_SD_B1_03_FLEXCAN1_RX 0x401F81E0U, 0x4U, 0x401F844CU, 0x0U, 0x401F83D0U
#define IOMUXC_GPIO_SD_B1_03_GPIO3_IO03 0x401F81E0U, 0x5U, 0, 0, 0x401F83D0U
#define IOMUXC_GPIO_SD_B1_03_CCM_PMIC_READY 0x401F81E0U, 0x6U, 0x401F83FCU, 0x0U, 0x401F83D0U
#define IOMUXC_GPIO_SD_B1_03_SAI3_TX_BCLK 0x401F81E0U, 0x8U, 0x401F8780U, 0x1U, 0x401F83D0U

#define IOMUXC_GPIO_SD_B1_04_USDHC2_CLK 0x401F81E4U, 0x0U, 0x401F85DCU, 0x0U, 0x401F83D4U
#define IOMUXC_GPIO_SD_B1_04_FLEXSPIB_SCLK 0x401F81E4U, 0x1U, 0, 0, 0x401F83D4U
#define IOMUXC_GPIO_SD_B1_04_LPI2C1_SCL 0x401F81E4U, 0x2U, 0x401F84CCU, 0x0U, 0x401F83D4U
#define IOMUXC_GPIO_SD_B1_04_SAI1_RX_SYNC 0x401F81E4U, 0x3U, 0x401F85A4U, 0x0U, 0x401F83D4U
#define IOMUXC_GPIO_SD_B1_04_FLEXSPIA_SS1_B 0x401F81E4U, 0x4U, 0, 0, 0x401F83D4U
#define IOMUXC_GPIO_SD_B1_04_GPIO3_IO04 0x401F81E4U, 0x5U, 0, 0, 0x401F83D4U
#define IOMUXC_GPIO_SD_B1_04_CCM_STOP 0x401F81E4U, 0x6U, 0, 0, 0x401F83D4U
#define IOMUXC_GPIO_SD_B1_04_SAI3_MCLK 0x401F81E4U, 0x8U, 0x401F8770U, 0x1U, 0x401F83D4U

#define IOMUXC_GPIO_SD_B1_05_USDHC2_CMD 0x401F81E8U, 0x0U, 0x401F85E4U, 0x0U, 0x401F83D8U
#define IOMUXC_GPIO_SD_B1_05_FLEXSPIA_DQS 0x401F81E8U, 0x1U, 0x401F84A4U, 0x0U, 0x401F83D8U
#define IOMUXC_GPIO_SD_B1_05_LPI2C1_SDA 0x401F81E8U, 0x2U, 0x401F84D0U, 0x0U, 0x401F83D8U
#define IOMUXC_GPIO_SD_B1_05_SAI1_RX_BCLK 0x401F81E8U, 0x3U, 0x401F8590U, 0x0U, 0x401F83D8U
#define IOMUXC_GPIO_SD_B1_05_FLEXSPIB_SS0_B 0x401F81E8U, 0x4U, 0, 0, 0x401F83D8U
#define IOMUXC_GPIO_SD_B1_05_GPIO3_IO05 0x401F81E8U, 0x5U, 0, 0, 0x401F83D8U
#define IOMUXC_GPIO_SD_B1_05_SAI3_RX_SYNC 0x401F81E8U, 0x8U, 0x401F877CU, 0x1U, 0x401F83D8U

#define IOMUXC_GPIO_SD_B1_06_USDHC2_RESET_B 0x401F81ECU, 0x0U, 0, 0, 0x401F83DCU
#define IOMUXC_GPIO_SD_B1_06_FLEXSPIA_SS0_B 0x401F81ECU, 0x1U, 0, 0, 0x401F83DCU
#define IOMUXC_GPIO_SD_B1_06_LPUART7_CTS_B 0x401F81ECU, 0x2U, 0, 0, 0x401F83DCU
#define IOMUXC_GPIO_SD_B1_06_SAI1_RX_DATA00 0x401F81ECU, 0x3U, 0x401F8594U, 0x0U, 0x401F83DCU
#define IOMUXC_GPIO_SD_B1_06_LPSPI2_PCS0 0x401F81ECU, 0x4U, 0x401F84FCU, 0x0U, 0x401F83DCU
#define IOMUXC_GPIO_SD_B1_06_GPIO3_IO06 0x401F81ECU, 0x5U, 0, 0, 0x401F83DCU
#define IOMUXC_GPIO_SD_B1_06_SAI3_RX_BCLK 0x401F81ECU, 0x8U, 0x401F8774U, 0x1U, 0x401F83DCU

#define IOMUXC_GPIO_SD_B1_07_SEMC_CSX01 0x401F81F0U, 0x0U, 0, 0, 0x401F83E0U
#define IOMUXC_GPIO_SD_B1_07_FLEXSPIA_SCLK 0x401F81F0U, 0x1U, 0x401F84C8U, 0x0U, 0x401F83E0U
#define IOMUXC_GPIO_SD_B1_07_LPUART7_RTS_B 0x401F81F0U, 0x2U, 0, 0, 0x401F83E0U
#define IOMUXC_GPIO_SD_B1_07_SAI1_TX_DATA00 0x401F81F0U, 0x3U, 0, 0, 0x401F83E0U
#define IOMUXC_GPIO_SD_B1_07_LPSPI2_SCK 0x401F81F0U, 0x4U, 0x401F8500U, 0x0U, 0x401F83E0U
#define IOMUXC_GPIO_SD_B1_07_GPIO3_IO07 0x401F81F0U, 0x5U, 0, 0, 0x401F83E0U

#define IOMUXC_GPIO_SD_B1_08_USDHC2_DATA4 0x401F81F4U, 0x0U, 0x401F85F8U, 0x0U, 0x401F83E4U
#define IOMUXC_GPIO_SD_B1_08_FLEXSPIA_DATA00 0x401F81F4U, 0x1U, 0x401F84A8U, 0x0U, 0x401F83E4U
#define IOMUXC_GPIO_SD_B1_08_LPUART7_TX 0x401F81F4U, 0x2U, 0x401F855CU, 0x0U, 0x401F83E4U
#define IOMUXC_GPIO_SD_B1_08_SAI1_TX_BCLK 0x401F81F4U, 0x3U, 0x401F85A8U, 0x0U, 0x401F83E4U
#define IOMUXC_GPIO_SD_B1_08_LPSPI2_SD0 0x401F81F4U, 0x4U, 0x401F8508U, 0x0U, 0x401F83E4U
#define IOMUXC_GPIO_SD_B1_08_GPIO3_IO08 0x401F81F4U, 0x5U, 0, 0, 0x401F83E4U
#define IOMUXC_GPIO_SD_B1_08_SEMC_CSX02 0x401F81F4U, 0x6U, 0, 0, 0x401F83E4U

#define IOMUXC_GPIO_SD_B1_09_USDHC2_DATA5 0x401F81F8U, 0x0U, 0x401F85FCU, 0x0U, 0x401F83E8U
#define IOMUXC_GPIO_SD_B1_09_FLEXSPIA_DATA01 0x401F81F8U, 0x1U, 0x401F84ACU, 0x0U, 0x401F83E8U
#define IOMUXC_GPIO_SD_B1_09_LPUART7_RX 0x401F81F8U, 0x2U, 0x401F8558U, 0x0U, 0x401F83E8U
#define IOMUXC_GPIO_SD_B1_09_SAI1_TX_SYNC 0x401F81F8U, 0x3U, 0x401F85ACU, 0x0U, 0x401F83E8U
#define IOMUXC_GPIO_SD_B1_09_LPSPI2_SDI 0x401F81F8U, 0x4U, 0x401F8504U, 0x0U, 0x401F83E8U
#define IOMUXC_GPIO_SD_B1_09_GPIO3_IO09 0x401F81F8U, 0x5U, 0, 0, 0x401F83E8U

#define IOMUXC_GPIO_SD_B1_10_USDHC2_DATA6 0x401F81FCU, 0x0U, 0x401F8600U, 0x0U, 0x401F83ECU
#define IOMUXC_GPIO_SD_B1_10_FLEXSPIA_DATA02 0x401F81FCU, 0x1U, 0x401F84B0U, 0x0U, 0x401F83ECU
#define IOMUXC_GPIO_SD_B1_10_LPUART2_RX 0x401F81FCU, 0x2U, 0x401F852CU, 0x0U, 0x401F83ECU
#define IOMUXC_GPIO_SD_B1_10_LPI2C2_SDA 0x401F81FCU, 0x3U, 0x401F84D8U, 0x0U, 0x401F83ECU
#define IOMUXC_GPIO_SD_B1_10_LPSPI2_PCS2 0x401F81FCU, 0x4U, 0, 0, 0x401F83ECU
#define IOMUXC_GPIO_SD_B1_10_GPIO3_IO10 0x401F81FCU, 0x5U, 0, 0, 0x401F83ECU

#define IOMUXC_GPIO_SD_B1_11_USDHC2_DATA7 0x401F8200U, 0x0U, 0x401F8604U, 0x0U, 0x401F83F0U
#define IOMUXC_GPIO_SD_B1_11_FLEXSPIA_DATA03 0x401F8200U, 0x1U, 0x401F84B4U, 0x0U, 0x401F83F0U
#define IOMUXC_GPIO_SD_B1_11_LPUART2_TX 0x401F8200U, 0x2U, 0x401F8530U, 0x0U, 0x401F83F0U
#define IOMUXC_GPIO_SD_B1_11_LPI2C2_SCL 0x401F8200U, 0x3U, 0x401F84D4U, 0x0U, 0x401F83F0U
#define IOMUXC_GPIO_SD_B1_11_LPSPI2_PCS3 0x401F8200U, 0x4U, 0, 0, 0x401F83F0U
#define IOMUXC_GPIO_SD_B1_11_GPIO3_IO11 0x401F8200U, 0x5U, 0, 0, 0x401F83F0U

#define IOMUXC_GPIO_SPI_B0_00 0x401F865CU, 0, 0, 0, 0x401F86B4U

#define IOMUXC_GPIO_SPI_B0_01_FLEXSPI2_B_SCLK 0x401F8660U, 0x0U, 0x401F8754U, 0x1U, 0x401F86B8U
#define IOMUXC_GPIO_SPI_B0_01_GPIO10_IO01 0x401F8660U, 0x5U, 0, 0, 0x401F86B8U

#define IOMUXC_GPIO_SPI_B0_02_FLEXSPI2_A_DATA00 0x401F8664U, 0x0U, 0x401F8730U, 0x2U, 0x401F86BCU
#define IOMUXC_GPIO_SPI_B0_02_GPIO10_IO02 0x401F8664U, 0x5U, 0, 0, 0x401F86BCU

#define IOMUXC_GPIO_SPI_B0_03_FLEXSPI2_B_DATA02 0x401F8668U, 0x0U, 0x401F8748U, 0x1U, 0x401F86C0U
#define IOMUXC_GPIO_SPI_B0_03_GPIO10_IO03 0x401F8668U, 0x5U, 0, 0, 0x401F86C0U

#define IOMUXC_GPIO_SPI_B0_04_FLEXSPI2_B_DATA03 0x401F866CU, 0x0U, 0x401F874CU, 0x1U, 0x401F86C4U
#define IOMUXC_GPIO_SPI_B0_04_GPIO10_IO04 0x401F866CU, 0x5U, 0, 0, 0x401F86C4U

#define IOMUXC_GPIO_SPI_B0_05_FLEXSPI2_A_SS0_B 0x401F8670U, 0x0U, 0, 0, 0x401F86C8U
#define IOMUXC_GPIO_SPI_B0_05_GPIO10_IO05 0x401F8670U, 0x5U, 0, 0, 0x401F86C8U

#define IOMUXC_GPIO_SPI_B0_06_FLEXSPI2_A_DATA02 0x401F8674U, 0x0U, 0x401F8738U, 0x2U, 0x401F86CCU
#define IOMUXC_GPIO_SPI_B0_06_GPIO10_IO06 0x401F8674U, 0x5U, 0, 0, 0x401F86CCU

#define IOMUXC_GPIO_SPI_B0_07_FLEXSPI2_B_DATA01 0x401F8678U, 0x0U, 0x401F8744U, 0x1U, 0x401F86D0U
#define IOMUXC_GPIO_SPI_B0_07_GPIO10_IO07 0x401F8678U, 0x5U, 0, 0, 0x401F86D0U

#define IOMUXC_GPIO_SPI_B0_08_FLEXSPI2_A_SCLK 0x401F867CU, 0x0U, 0x401F8750U, 0x2U, 0x401F86D4U
#define IOMUXC_GPIO_SPI_B0_08_GPIO10_IO08 0x401F867CU, 0x5U, 0, 0, 0x401F86D4U

#define IOMUXC_GPIO_SPI_B0_09_FLEXSPI2_A_DQS 0x401F8680U, 0x0U, 0x401F872CU, 0x2U, 0x401F86D8U
#define IOMUXC_GPIO_SPI_B0_09_GPIO10_IO09 0x401F8680U, 0x5U, 0, 0, 0x401F86D8U

#define IOMUXC_GPIO_SPI_B0_10_FLEXSPI2_A_DATA03 0x401F8684U, 0x0U, 0x401F873CU, 0x2U, 0x401F86DCU
#define IOMUXC_GPIO_SPI_B0_10_GPIO10_IO10 0x401F8684U, 0x5U, 0, 0, 0x401F86DCU

#define IOMUXC_GPIO_SPI_B0_11_FLEXSPI2_B_DATA00 0x401F8688U, 0x0U, 0x401F8740U, 0x1U, 0x401F86E0U
#define IOMUXC_GPIO_SPI_B0_11_GPIO10_IO11 0x401F8688U, 0x5U, 0, 0, 0x401F86E0U

#define IOMUXC_GPIO_SPI_B0_12_FLEXSPI2_A_DATA01 0x401F868CU, 0x0U, 0x401F8734U, 0x2U, 0x401F86E4U
#define IOMUXC_GPIO_SPI_B0_12_GPIO10_IO12 0x401F868CU, 0x5U, 0, 0, 0x401F86E4U

#define IOMUXC_GPIO_SPI_B0_13 0x401F8690U, 0, 0, 0, 0x401F86E8U

#define IOMUXC_GPIO_SPI_B1_00_FLEXSPI2_A_DQS 0x401F8694U, 0x0U, 0x401F872CU, 0x0U, 0x401F86ECU
#define IOMUXC_GPIO_SPI_B1_00_GPIO10_IO14 0x401F8694U, 0x5U, 0, 0, 0x401F86ECU

#define IOMUXC_GPIO_SPI_B1_01_FLEXSPI2_A_DATA03 0x401F8698U, 0x0U, 0x401F873CU, 0x0U, 0x401F86F0U
#define IOMUXC_GPIO_SPI_B1_01_GPIO10_IO15 0x401F8698U, 0x5U, 0, 0, 0x401F86F0U

#define IOMUXC_GPIO_SPI_B1_02_FLEXSPI2_A_DATA02 0x401F869CU, 0x0U, 0x401F8738U, 0x0U, 0x401F86F4U
#define IOMUXC_GPIO_SPI_B1_02_GPIO10_IO16 0x401F869CU, 0x5U, 0, 0, 0x401F86F4U

#define IOMUXC_GPIO_SPI_B1_03_FLEXSPI2_A_DATA01 0x401F86A0U, 0x0U, 0x401F8734U, 0x0U, 0x401F86F8U
#define IOMUXC_GPIO_SPI_B1_03_GPIO10_IO17 0x401F86A0U, 0x5U, 0, 0, 0x401F86F8U

#define IOMUXC_GPIO_SPI_B1_04_FLEXSPI2_A_DATA00 0x401F86A4U, 0x0U, 0x401F8730U, 0x0U, 0x401F86FCU
#define IOMUXC_GPIO_SPI_B1_04_GPIO10_IO18 0x401F86A4U, 0x5U, 0, 0, 0x401F86FCU

#define IOMUXC_GPIO_SPI_B1_05_FLEXSPI2_A_SCLK 0x401F86A8U, 0x0U, 0x401F8750U, 0x0U, 0x401F8700U
#define IOMUXC_GPIO_SPI_B1_05_GPIO10_IO19 0x401F86A8U, 0x5U, 0, 0, 0x401F8700U

#define IOMUXC_GPIO_SPI_B1_06_FLEXSPI2_A_SS0_B 0x401F86ACU, 0x0U, 0, 0, 0x401F8704U
#define IOMUXC_GPIO_SPI_B1_06_GPIO10_IO20 0x401F86ACU, 0x5U, 0, 0, 0x401F8704U

#define IOMUXC_GPIO_SPI_B1_07 0x401F86B0U, 0, 0, 0, 0x401F8708U

#define IOMUXC_SNVS_WAKEUP_GPIO5_IO00 0x400A8000U, 0x5U, 0, 0, 0x400A8018U
#define IOMUXC_SNVS_WAKEUP_NMI 0x400A8000U, 0x7U, 0x401F8568U, 0x1U, 0x400A8018U

#define IOMUXC_SNVS_PMIC_ON_REQ_SNVS_PMIC_ON_REQ 0x400A8004U, 0x0U, 0, 0, 0x400A801CU
#define IOMUXC_SNVS_PMIC_ON_REQ_GPIO5_IO01 0x400A8004U, 0x5U, 0, 0, 0x400A801CU

#define IOMUXC_SNVS_PMIC_STBY_REQ_CCM_PMIC_STBY_REQ 0x400A8008U, 0x0U, 0, 0, 0x400A8020U
#define IOMUXC_SNVS_PMIC_STBY_REQ_GPIO5_IO02 0x400A8008U, 0x5U, 0, 0, 0x400A8020U

#define IOMUXC_SNVS_TEST_MODE 0, 0, 0, 0, 0x400A800CU

#define IOMUXC_SNVS_POR_B 0, 0, 0, 0, 0x400A8010U

#define IOMUXC_SNVS_ONOFF 0, 0, 0, 0, 0x400A8014U

/*@}*/

#define IOMUXC_GPR_SAIMCLK_LOWBITMASK (0x7U)
#define IOMUXC_GPR_SAIMCLK_HIGHBITMASK (0x3U)

typedef enum _iomuxc_gpr_mode
{
    kIOMUXC_GPR_GlobalInterruptRequest    = IOMUXC_GPR_GPR1_GINT_MASK,
    kIOMUXC_GPR_ENET1RefClkMode           = IOMUXC_GPR_GPR1_ENET1_CLK_SEL_MASK,
    kIOMUXC_GPR_ENET2RefClkMode           = IOMUXC_GPR_GPR1_ENET2_CLK_SEL_MASK,
    kIOMUXC_GPR_ENET1TxClkOutputDir       = IOMUXC_GPR_GPR1_ENET1_TX_CLK_DIR_MASK,
    kIOMUXC_GPR_ENET2TxClkOutputDir       = IOMUXC_GPR_GPR1_ENET2_TX_CLK_DIR_MASK,
    kIOMUXC_GPR_SAI1MClkOutputDir         = IOMUXC_GPR_GPR1_SAI1_MCLK_DIR_MASK,
    kIOMUXC_GPR_SAI2MClkOutputDir         = IOMUXC_GPR_GPR1_SAI2_MCLK_DIR_MASK,
    kIOMUXC_GPR_SAI3MClkOutputDir         = IOMUXC_GPR_GPR1_SAI3_MCLK_DIR_MASK,
    kIOMUXC_GPR_ExcMonitorSlavErrResponse = IOMUXC_GPR_GPR1_EXC_MON_MASK,
    kIOMUXC_GPR_ENETIpgClkOn              = IOMUXC_GPR_GPR1_ENET_IPG_CLK_S_EN_MASK,
    kIOMUXC_GPR_AHBClockEnable            = (int)IOMUXC_GPR_GPR1_CM7_FORCE_HCLK_EN_MASK,
} iomuxc_gpr_mode_t;

typedef enum _iomuxc_gpr_saimclk
{
    kIOMUXC_GPR_SAI1MClk1Sel = IOMUXC_GPR_GPR1_SAI1_MCLK1_SEL_SHIFT,
    kIOMUXC_GPR_SAI1MClk2Sel = IOMUXC_GPR_GPR1_SAI1_MCLK2_SEL_SHIFT,
    kIOMUXC_GPR_SAI1MClk3Sel = IOMUXC_GPR_GPR1_SAI1_MCLK3_SEL_SHIFT,
    kIOMUXC_GPR_SAI2MClk3Sel = IOMUXC_GPR_GPR1_SAI2_MCLK3_SEL_SHIFT,
    kIOMUXC_GPR_SAI3MClk3Sel = IOMUXC_GPR_GPR1_SAI3_MCLK3_SEL_SHIFT,
} iomuxc_gpr_saimclk_t;

typedef enum _iomuxc_mqs_pwm_oversample_rate
{
    kIOMUXC_MqsPwmOverSampleRate32 = 0, /* MQS PWM over sampling rate 32. */
    kIOMUXC_MqsPwmOverSampleRate64 = 1  /* MQS PWM over sampling rate 64. */
} iomuxc_mqs_pwm_oversample_rate_t;

#if defined(__cplusplus)
extern "C" {
#endif /*_cplusplus */

/*! @name Configuration */
/*@{*/

/*!
 * @brief Sets the IOMUXC pin mux mode.
 * @note The first five parameters can be filled with the pin function ID macros.
 *
 * This is an example to set the PTA6 as the lpuart0_tx:
 * @code
 * IOMUXC_SetPinMux(IOMUXC_PTA6_LPUART0_TX, 0);
 * @endcode
 *
 * This is an example to set the PTA0 as GPIOA0:
 * @code
 * IOMUXC_SetPinMux(IOMUXC_PTA0_GPIOA0, 0);
 * @endcode
 *
 * @param muxRegister  The pin mux register.
 * @param muxMode      The pin mux mode.
 * @param inputRegister The select input register.
 * @param inputDaisy   The input daisy.
 * @param configRegister  The config register.
 * @param inputOnfield   Software input on field.
 */
static inline void IOMUXC_SetPinMux(uint32_t muxRegister,
                                    uint32_t muxMode,
                                    uint32_t inputRegister,
                                    uint32_t inputDaisy,
                                    uint32_t configRegister,
                                    uint32_t inputOnfield)
{
    *((volatile uint32_t *)muxRegister) =
        IOMUXC_SW_MUX_CTL_PAD_MUX_MODE(muxMode) | IOMUXC_SW_MUX_CTL_PAD_SION(inputOnfield);

    if (inputRegister != 0UL)
    {
        *((volatile uint32_t *)inputRegister) = inputDaisy;
    }
}

/*!
 * @brief Sets the IOMUXC pin configuration.
 * @note The previous five parameters can be filled with the pin function ID macros.
 *
 * This is an example to set pin configuration for IOMUXC_PTA3_LPI2C0_SCLS:
 * @code
 * IOMUXC_SetPinConfig(IOMUXC_PTA3_LPI2C0_SCLS,IOMUXC_SW_PAD_CTL_PAD_PUS_MASK|IOMUXC_SW_PAD_CTL_PAD_PUS(2U))
 * @endcode
 *
 * @param muxRegister  The pin mux register.
 * @param muxMode      The pin mux mode.
 * @param inputRegister The select input register.
 * @param inputDaisy   The input daisy.
 * @param configRegister  The config register.
 * @param configValue   The pin config value.
 */
static inline void IOMUXC_SetPinConfig(uint32_t muxRegister,
                                       uint32_t muxMode,
                                       uint32_t inputRegister,
                                       uint32_t inputDaisy,
                                       uint32_t configRegister,
                                       uint32_t configValue)
{
    if (configRegister != 0UL)
    {
        *((volatile uint32_t *)configRegister) = configValue;
    }
}

/*!
 * @brief Sets IOMUXC general configuration for some mode.
 *
 * @param base     The IOMUXC GPR base address.
 * @param mode     The mode for setting. the mode is the logical OR of "iomuxc_gpr_mode"
 * @param enable   True enable false disable.
 */
static inline void IOMUXC_EnableMode(IOMUXC_GPR_Type *base, uint32_t mode, bool enable)
{
    mode &= ~(IOMUXC_GPR_GPR1_SAI1_MCLK1_SEL_MASK | IOMUXC_GPR_GPR1_SAI1_MCLK2_SEL_MASK |
              IOMUXC_GPR_GPR1_SAI1_MCLK3_SEL_MASK | IOMUXC_GPR_GPR1_SAI2_MCLK3_SEL_MASK |
              IOMUXC_GPR_GPR1_SAI3_MCLK3_SEL_MASK);

    if (enable)
    {
        base->GPR1 |= mode;
    }
    else
    {
        base->GPR1 &= ~mode;
    }
}

/*!
 * @brief Sets IOMUXC general configuration for SAI MCLK selection.
 *
 * @param base     The IOMUXC GPR base address.
 * @param mclk     The SAI MCLK.
 * @param clkSrc   The clock source. Take refer to register setting details for the clock source in RM.
 */
static inline void IOMUXC_SetSaiMClkClockSource(IOMUXC_GPR_Type *base, iomuxc_gpr_saimclk_t mclk, uint8_t clkSrc)
{
    uint32_t gpr;

    if (mclk > kIOMUXC_GPR_SAI1MClk2Sel)
    {
        gpr = base->GPR1 & ~((uint32_t)IOMUXC_GPR_SAIMCLK_HIGHBITMASK << (uint32_t)mclk);
        base->GPR1 = (((uint32_t)clkSrc & IOMUXC_GPR_SAIMCLK_HIGHBITMASK) << (uint32_t)mclk) | gpr;
    }
    else
    {
        gpr = base->GPR1 & ~((uint32_t)IOMUXC_GPR_SAIMCLK_LOWBITMASK << (uint32_t)mclk);
        base->GPR1 = (((uint32_t)clkSrc & IOMUXC_GPR_SAIMCLK_LOWBITMASK) << (uint32_t)mclk) | gpr;
    }
}

/*!
 * @brief Enters or exit MQS software reset.
 *
 * @param base     The IOMUXC GPR base address.
 * @param enable   Enter or exit MQS software reset.
 */
static inline void IOMUXC_MQSEnterSoftwareReset(IOMUXC_GPR_Type *base, bool enable)
{
    if (enable)
    {
        base->GPR2 |= IOMUXC_GPR_GPR2_MQS_SW_RST_MASK;
    }
    else
    {
        base->GPR2 &= ~IOMUXC_GPR_GPR2_MQS_SW_RST_MASK;
    }
}

/*!
 * @brief Enables or disables MQS.
 *
 * @param base     The IOMUXC GPR base address.
 * @param enable   Enable or disable the MQS.
 */
static inline void IOMUXC_MQSEnable(IOMUXC_GPR_Type *base, bool enable)
{
    if (enable)
    {
        base->GPR2 |= IOMUXC_GPR_GPR2_MQS_EN_MASK;
    }
    else
    {
        base->GPR2 &= ~IOMUXC_GPR_GPR2_MQS_EN_MASK;
    }
}

/*!
 * @brief Configure MQS PWM oversampling rate compared with mclk and divider ratio control for mclk from hmclk.
 *
 * @param base     The IOMUXC GPR base address.
 * @param rate     The MQS PWM oversampling rate, refer to "iomuxc_mqs_pwm_oversample_rate_t".
 * @param divider  The divider ratio control for mclk from hmclk. mclk freq = 1 /(divider + 1) * hmclk freq.
 */

static inline void IOMUXC_MQSConfig(IOMUXC_GPR_Type *base, iomuxc_mqs_pwm_oversample_rate_t rate, uint8_t divider)
{
    uint32_t gpr = base->GPR2 & ~(IOMUXC_GPR_GPR2_MQS_OVERSAMPLE_MASK | IOMUXC_GPR_GPR2_MQS_CLK_DIV_MASK);
    base->GPR2 = gpr | IOMUXC_GPR_GPR2_MQS_OVERSAMPLE(rate) | IOMUXC_GPR_GPR2_MQS_CLK_DIV(divider);
}

/*@}*/

#if defined(__cplusplus)
}
#endif /*_cplusplus */

/*! @}*/

#endif /* _FSL_IOMUXC_H_ */
