#ifndef _ZHUANGTAIJ_H_
#define _ZHUANGTAIJ_H_

void Motor_Control();
extern float g_target_angular_velocity_setpoint;
extern float g_current_angular_rate; // 当前角速度，由PIT_CH2更新
extern int g_zebra_detected_flag;
extern float vy; 
extern float vx; 
// 声明全局变量
extern PID_TypeDef wheelPID[3];
extern PID_TypeDef distancePID[1];
extern PID_TypeDef anglePID[1];
extern PID_TypeDef boxAlignAnglePID[1]; // 推箱子用角度PID
extern PID_TypeDef textModeAnglePID[1]; // 锁定推箱子模式用角度PID
extern PID_TypeDef zyPID[1];
extern PID_TypeDef qhPID[1];
extern PID_TypeDef saoxianPID[1];
extern PID_TypeDef saoxianPIDh[1];
extern PID_TypeDef angularRatePID[1];

#endif