#ifndef CE_H
#define CE_H


#define MOTORl_DIR               (D1 )        //左前
#define MOTORl_PWM               (PWM1_MODULE3_CHA_D0)
#define MOTORr_DIR               (D15 )       //右前
#define MOTORr_PWM               (PWM1_MODULE1_CHA_D14)
#define MOTORb_DIR               (D13 )        //后
#define MOTORb_PWM               (PWM1_MODULE0_CHA_D12)

#define  yq_encode QTIMER1_ENCODER1       //右前
#define  zq_encode QTIMER2_ENCODER1       //左前
#define  hou_encode QTIMER4_ENCODER1       //后
#define  yqencode_dir  C1
#define  zqencode_dir  C4
#define  houencode_dir  C10
typedef struct {
    float kp;
    float ki;
    float kd;
    float output;
    float error_last;
    float error_second_last;
    uint8_t is_angle;      // 0 表示普通PID, 1 表示角度PID
    uint8_t pid_mode;      // 0 表示增量式PID, 1 表示位置式PID
} PID_TypeDef;
extern int32 ti; // 全局变量，记录时间间隔
extern int32 tim; // 全局变量，记录时间间隔
extern float omegaRef[3]; // 三个轮子的期望速度
extern float omegaMeasured[3];
extern float robot_x;
extern float robot_y;
extern float total_distance;
void UpdateRobotPosition(void);
void count_get(void);
void SetWheelSpeed(float *omegaRef, float *omegaMeasured);
void InverseKinematics(float vx, float vy, float omega, float *omegaRef);
extern float PID_Compute(PID_TypeDef *pid, float setpoint, float measured);
#endif