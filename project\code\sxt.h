#ifndef SXT_H
#define SXT_H

#include "zf_common_headfile.h" // 包含中封装了常用头文件

//-------------------------------------------------------------------------------------------------------------------
//  宏定义
//-------------------------------------------------------------------------------------------------------------------

// 透视变换相关宏
#define RESULT_ROW      120            // 结果图像行数
#define RESULT_COL      160             // 结果图像列数
#define USED_ROW        120             // 透视变换使用图像行数
#define USED_COL        160             // 透视变换使用图像列数
#define PER_IMG         mt9v03x_image   // 用于透视变换的原始图像
#define ImageUsed       *PerImg_ip      // 最终用于寻线和识别的图像指针

// 图像处理相关宏
#define WHITE_PIXEL     255             // 白像素值
#define BLACK_PIXEL     0               // 黑像素值
#define BORDER_MAX      (MT9V03X_W-2)   // 边界最大值
#define BORDER_MIN      1               // 边界最小值


//-------------------------------------------------------------------------------------------------------------------
//  外部变量声明
//-------------------------------------------------------------------------------------------------------------------

// --- 图像数据区 ---
extern uint8_t   *PerImg_ip[RESULT_ROW][RESULT_COL];    // 透视变换后的图像指针数组
extern uint8_t   mt9v03x_image_2[MT9V03X_H][MT9V03X_W]; // 图像处理中间缓冲
extern uint8_t   original_image[MT9V03X_H][MT9V03X_W]; // 原始灰度图像
extern uint8_t   bin_image[MT9V03X_H][MT9V03X_W];      // 二值化后图像

// --- 赛道边界线 ---
extern int      Left_Line[MT9V03X_H];                   // 左边界数组
extern int      Right_Line[MT9V03X_H];                  // 右边界数组
extern int      Mid_Line[MT9V03X_H];                    // 中线数组

// --- 寻线基本信息 ---
extern int      feedbackposition;                       // 基础巡线偏差
extern int      feedbackposition1;                      // 十字巡线偏差1
extern int      feedbackposition2;                      // 十字巡线偏差2
extern volatile int Search_Stop_Line;                   // 搜索截止行
extern volatile int Boundry_Start_Right;                // 右边界起始行
extern volatile int Boundry_Start_Left;                 // 左边界起始行
extern volatile int Left_Lost_Time;                     // 左边丢线计数
extern volatile int Right_Lost_Time;                    // 右边丢线计数
extern volatile int Both_Lost_Time;                     // 两边都丢线计数

// --- 元素状态标志 ---
extern volatile int  Ramp_Flag;                         // 坡道状态标志
extern volatile uint8 Img_Disappear_Flag;               // 图像消失标志
extern volatile int  Cross_Flag;                        // 十字状态标志
extern volatile int  Left_Down_Find;                    // 左下角点发现标志（行数）
extern volatile int  Left_Up_Find;                      // 左上角点发现标志（行数）
extern volatile int  Right_Down_Find;                   // 右下角点发现标志（行数）
extern volatile int  Right_Up_Find;                     // 右上角点发现标志（行数）

// --- 透视变换矩阵 ---
extern float hd[3][3];                                  // 透视变换矩阵

extern float Get_angle(float Ax, float Ay, float Bx, float By, float Cx, float Cy);
extern int16 L_start_x, L_start_y, R_start_x, R_start_y;
extern uint8 left_findflag, right_findflag;
extern void search_neighborhood();
extern void draw_eight_neighborhood_boundaries();
extern uint8 L_edge_count;
extern uint8 R_edge_count;

extern int16 L_corner_flag, R_corner_flag;
extern int16 L_corner_row, R_corner_row;
extern int16 L_corner_col, R_corner_col;
extern int L_corner_angle, R_corner_angle;
//-------------------------------------------------------------------------------------------------------------------
//  外部函数声明
//-------------------------------------------------------------------------------------------------------------------

// --- 图像预处理 ---
void    otsu_erzhihua(uint8 *image, uint8_t *binary_image); // 大津法二值化
void    turn_to_bin(void);                              // 转换为二值图像（未使用大津法）

// --- 边界与中线 ---
void    draw_boundaries(void);                          // 绘制边界
void    draw_mid_line(void);                            // 绘制中线
void    Longest_White_Column(void);                     // 最长白列寻线
void    Lengthen_Left_Boundry(int start,int end);       // 左边界延长
void    Lengthen_Right_Boundry(int start,int end);      // 右边界延长
void    Left_Add_Line(int x1,int y1,int x2,int y2);     // 左边界画线
void    Right_Add_Line(int x1,int y1,int x2,int y2);    // 右边界画线
void    Draw_Line(int startX, int startY, int endX, int endY); // 通用画线
void    Show_Boundry(void);                             // 显示边界

// --- 角点检测 ---
void    Find_Up_Point(int start,int end);               // 寻找上角点
void    Find_Down_Point(int start,int end);             // 寻找下角点

// --- 功能函数 ---
void    ImagePerspective_Init(void);                    // 透视变换初始化
float   Get_angle(float Ax, float Ay, float Bx, float By, float Cx, float Cy); // 计算角度
int16   limit_a_b(int16 x, int a, int b);               // 数值限幅
void    get_turning_point(void);                         // 获取拐点
void    balinyu();
void    zongsaoxian();

#endif