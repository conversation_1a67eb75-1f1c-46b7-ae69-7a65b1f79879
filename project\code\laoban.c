#include "zf_common_headfile.h"



/*******************************************************************************
* generated by lcd-image-converter rev.030b30d from 2019-03-17 01:38:34 +0500
* image
* filename: unsaved
* name: 牢版
*
* preset name: 
* data block size: 8 bit(s), uint8_t
* RLE compression enabled: no
* conversion type: Monochrome, Diffuse Dither 128
* split to rows: yes
* bits per pixel: 24
*
* preprocess:
*  main scan direction: top_to_bottom
*  line scan direction: forward
*  inverse: no
*******************************************************************************/

/*
 typedef struct {
     const uint8_t *data;
     uint16_t width;
     uint16_t height;
     uint8_t dataSize;
     } tImage;
*/
#include <stdint.h>



static const uint32_t image_data_laoban[34560] = {
    // █∙██∙██∙██∙█∙█∙█∙█∙█∙█∙∙█∙∙█∙∙∙█∙∙∙█∙█∙███████∙█∙██∙█∙██∙██∙██∙██∙██∙██∙██∙██∙██∙███∙██∙█∙█∙█∙█∙
    // ∙██∙█∙██∙█∙█∙█∙█∙█∙∙█∙∙█∙∙∙∙█∙∙∙∙█∙∙█∙████∙█∙██∙█∙█∙██∙█∙█∙█∙██∙██∙██∙██∙██∙███∙█████∙██∙██∙██∙█
    // █∙██∙█∙∙█∙█∙█∙█∙█∙█∙∙∙█∙∙█∙∙∙█∙█∙∙█∙∙█∙█∙█∙██∙█∙██∙█∙█∙██∙██∙█∙█∙██∙█∙█∙██████████████∙█████∙███
    // █∙█∙█∙█∙█∙█∙█∙█∙█∙█∙█∙█∙█∙█∙█∙∙∙█∙█∙█∙██∙██∙█∙██∙██∙██∙█∙█∙██∙██∙█∙██████████████████████∙████∙█
    // ∙█∙∙∙█∙█∙█∙█∙█∙█∙█∙███████∙█∙█████∙███∙██∙█∙██∙█∙█∙█∙██∙███∙██∙█████∙███████████████∙██∙███∙████
    // ∙∙██∙█∙█∙█∙∙█∙█∙█∙██∙██∙███∙██∙█∙██∙█∙██∙███∙█∙██∙█∙█∙█∙█∙█∙█∙█∙█∙███████████∙████∙██████∙███∙██
    // █∙∙█∙∙█∙∙∙█∙∙█∙██████████∙██∙██∙█∙████∙██∙█∙███∙█∙█∙∙∙█∙█∙██∙██∙█∙█∙██████∙█████∙███∙█∙████∙███∙
    // ∙█∙∙█∙∙█∙█∙∙█████████∙█∙██∙██∙██∙██∙∙██∙█∙██∙█∙█∙∙∙█∙∙∙∙∙█∙∙█∙█∙██∙███∙█∙███∙█∙███∙████∙█∙███∙██
    // ∙∙█∙∙█∙∙∙∙█∙∙███∙█∙█∙███∙██∙██∙██∙███∙█∙██∙█∙∙∙∙∙█∙∙∙█∙█∙∙∙█∙██∙█∙█∙█∙███∙█∙███∙█∙█∙█∙██∙██∙██∙█
    // ∙█∙∙∙∙█∙█∙█∙█∙█∙███∙██∙██∙██∙█∙█∙█∙█∙█∙█∙█∙█∙█∙∙∙∙∙█∙∙∙∙∙█∙∙∙█∙████████∙████∙█∙██∙██∙█∙██∙█∙█∙██
    // ∙∙∙█∙█∙∙█∙∙█∙█∙██∙██∙██∙██∙██∙█████∙██████∙∙∙∙∙∙∙█∙∙∙∙█∙∙∙∙█∙∙██∙█∙█∙████∙██████∙██████∙████∙██∙
    // ∙█∙█∙∙█∙∙█∙█∙██∙█∙█∙█∙██∙█∙█∙██∙█∙███∙███∙█∙█∙∙█∙∙∙∙∙∙∙∙█∙∙∙∙∙∙███████∙████∙█∙████∙█∙█∙██∙█∙██∙█
    // ∙█∙∙█∙█∙█∙∙█∙█∙██∙██∙█∙██∙███∙████∙██████∙∙∙∙∙█∙∙∙∙∙█∙∙∙∙∙█∙∙█∙∙█∙█∙███∙█∙████∙█∙███∙███∙███████
    // ∙█∙██∙█∙∙█∙█∙██∙██∙████∙███∙████∙███████∙∙∙█∙∙∙█∙█∙∙∙∙█∙█∙∙∙∙∙∙∙∙███∙█∙███∙█∙████∙█∙██∙██∙█∙█∙█∙
    // █∙█∙∙█∙█∙∙∙█∙█∙█∙██∙█∙████████████∙███∙█∙█∙∙∙█∙∙∙∙∙█∙∙∙∙∙∙∙█∙∙█∙∙∙███∙██∙█████∙█████∙██∙████∙███
    // ∙∙∙█∙∙∙∙█∙█∙█∙██∙█∙████████████∙█∙█∙█∙█∙∙∙∙∙∙∙∙█∙█∙∙█∙∙∙∙█∙∙∙∙∙∙█∙∙███████∙██∙██∙█∙██████∙████∙█
    // ∙█∙∙∙█∙∙∙∙∙█∙█∙█∙█∙█∙█████∙██∙██∙█∙█∙█∙∙∙∙∙█∙∙∙∙█∙█∙∙█∙█∙∙∙∙∙∙█∙∙∙█∙█∙█∙█∙██∙██∙███∙█∙█∙██∙█∙███
    // ∙∙█∙█∙∙█∙█∙█∙█∙█∙█∙█∙█∙█∙█∙█∙█∙█∙██∙█∙∙█∙∙∙∙█∙█∙█∙██∙█∙∙∙∙∙█∙∙∙∙∙∙∙██∙██∙██∙██∙██∙██∙█∙█∙██∙█∙█∙
    // █∙∙∙∙█∙∙█∙█∙█∙█∙█∙█∙█∙█∙█∙█∙██∙██∙██∙█∙∙∙██∙∙█∙███∙██∙█∙█∙∙∙∙∙∙∙█∙∙█∙██∙████∙███∙█∙██████∙████∙█
    // ∙█∙█∙∙█∙∙∙∙█∙∙∙∙█∙∙█∙█∙█∙█∙█∙██∙██∙██∙∙∙█∙∙██∙█∙███∙██∙█∙█∙∙█∙█∙∙∙∙∙█████∙████∙█████∙█∙███∙█∙███
    // ∙█∙█∙█∙∙∙█∙∙█∙█∙∙█∙∙█∙∙█∙∙█∙██∙██∙█∙█∙∙█∙█∙█∙███∙█∙██∙██∙█∙█∙∙∙∙∙∙█∙█∙█∙███∙████∙███████∙█████∙█
    // █∙█∙█∙█∙█∙█∙∙█∙█∙∙█∙∙█∙█∙█∙██∙██∙███∙∙∙∙█∙█∙█∙█∙█∙█∙██∙█∙█∙█∙█∙∙█∙∙∙█████∙███∙████∙█∙█∙███∙█████
    // █∙██∙█∙∙∙∙∙█∙∙█∙█∙∙█∙█∙∙█∙█∙██∙██∙█∙█∙█∙∙█∙██∙██∙███∙█∙█∙█∙█∙∙∙∙∙∙∙█∙█∙████∙███∙████████∙███∙█∙█
    // █∙█∙█∙█∙█∙█∙█∙∙∙█∙█∙█∙█∙█∙█∙█∙██∙███∙∙∙█∙█∙█∙██∙██∙██∙█∙█∙█∙█∙█∙∙∙∙∙███∙█████∙███∙█∙██∙█████████
    // █∙█∙█∙█∙∙█∙█∙█∙█∙█∙∙█∙∙█∙█∙███∙██∙███∙∙█∙∙∙█∙█∙██∙██∙██∙█∙█∙█∙∙∙∙█∙█∙███∙█∙████∙████∙███∙█∙█∙█∙█
    // ∙██∙█∙█∙█∙∙∙█∙█∙∙∙█∙█∙█∙█∙█∙█∙██∙██∙█∙∙∙█∙∙∙█∙█∙█∙█∙█∙█∙█∙█∙█∙∙█∙∙∙∙██∙█∙█∙∙█∙█∙█∙█∙█∙∙██∙█∙████
    // █∙█∙█∙█∙∙∙█∙█∙∙█∙█∙█∙█∙∙█∙∙███∙███∙█∙∙█∙∙██∙∙█∙█∙█∙∙∙∙∙█∙█∙█∙∙∙∙∙∙∙█∙████∙█∙██∙█∙███∙██∙█∙█∙∙█∙█
    // █∙██∙█∙∙█∙∙█∙∙█∙█∙∙∙█∙█∙∙█∙█∙██∙█∙███∙∙█∙∙█∙█∙█∙█∙∙██∙█∙█∙█∙∙█∙∙∙█∙███∙█∙█∙█∙█∙█∙█∙█∙∙█∙█∙∙█∙███
    // █∙█∙█∙█∙∙█∙∙█∙∙█∙∙█∙∙█∙█∙█∙██∙██∙██∙∙█∙∙█∙∙█∙∙█∙█∙∙∙██∙█∙∙█∙█∙∙∙∙∙∙█∙███∙█∙█∙██∙█∙█∙█∙█∙█∙█∙█∙██
    // ██∙█∙█∙█∙∙█∙█∙█∙∙█∙∙█∙∙∙█∙█∙██∙██∙██∙█∙█∙█∙∙█∙██∙█∙█∙∙∙∙█∙█∙∙∙∙∙∙█∙███∙██∙█∙██∙∙█∙██∙█∙██∙█∙█∙██
    // ∙██∙█∙█∙∙█∙█∙∙∙∙∙∙∙∙∙∙∙█∙∙█∙█∙█∙██∙██∙█∙█∙█∙█∙█∙█∙█∙∙█∙█∙█∙█∙█∙∙∙∙██∙████████∙██∙██████∙██∙███∙█
    // █∙██∙█∙█∙∙∙∙█∙█∙█∙∙∙∙█∙∙█∙█∙████∙██∙█∙█∙█∙█∙█∙█∙██∙∙█∙█∙█∙∙∙∙∙∙∙█∙████∙█∙█∙███████∙█∙█∙████∙████
    // █∙█∙█∙█∙∙█∙█∙∙∙∙∙∙∙█∙∙∙█∙█∙█∙█∙██∙██∙█∙████∙█∙█∙∙██∙█∙█∙∙█∙█∙∙█∙∙∙█∙████∙██∙█∙∙█∙███∙██∙█∙█∙█∙█∙
    // █∙██∙█∙█∙∙█∙█∙█∙█∙∙∙∙█∙█∙∙█∙██∙█∙██∙██∙█∙∙█∙██∙██∙████∙██∙█∙█∙∙∙█∙███∙███∙█∙██∙█∙█∙██∙█∙█∙█∙████
    // ██∙██∙█∙∙█∙∙∙█∙█∙∙∙█∙∙█∙█∙█∙█∙███∙██∙█∙█∙∙∙█∙██∙█∙█∙█∙██∙█∙∙∙∙█∙∙█∙████∙████∙███████████████∙█∙█
    // ∙██∙█∙█∙█∙∙█∙∙█∙∙∙∙∙█∙∙█∙∙█∙██∙█∙█∙██∙∙∙█∙█∙∙█∙█∙█∙█∙██∙█∙█∙█∙∙█∙███∙████∙███∙██∙█∙█∙█∙█∙█∙█████
    // █∙█∙█∙█∙∙█∙∙∙█∙∙∙█∙∙∙█∙∙█∙∙█∙██∙███∙█∙█∙∙∙∙█∙∙∙∙∙∙█∙█∙█∙█∙∙∙∙█∙█∙█∙███∙███∙█∙█∙█████████████∙█∙█
    // ██∙██∙█∙█∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙█∙█∙██∙█∙██∙∙█∙█∙∙∙█∙█∙█∙∙█∙█∙∙█∙█∙█∙∙████∙██∙███∙█∙█∙∙█∙█∙█∙█∙█∙█∙█∙∙
    // ∙██∙█∙█∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙███∙██∙██∙█∙∙∙∙█∙█∙∙█∙█∙█∙∙∙█∙∙█∙∙█∙██∙██████∙█∙█∙█∙██∙███∙██∙∙█∙∙█∙█
    // █∙██∙█∙█∙█∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙█∙█∙█∙██∙█∙█∙█∙∙∙∙█∙∙█∙█∙∙█∙∙█∙∙█∙∙█∙██∙█∙∙█∙█∙█∙██∙█∙█∙█∙█∙█∙∙∙█∙█∙∙
    // ██∙██∙█∙∙∙∙█∙∙∙∙∙∙∙∙█∙∙∙█∙∙█∙████∙███∙█∙∙█∙██∙█∙∙∙∙∙█∙∙█∙█∙∙██████∙█∙█∙█∙█∙█∙█∙█∙██∙█∙█∙███∙█∙██
    // █∙█∙█∙█∙█∙∙∙∙∙∙█∙∙∙∙∙∙∙∙∙∙∙██∙█∙██∙█∙█∙█∙∙█∙██∙██∙█∙█∙█∙∙∙██∙█∙█∙██∙█∙∙█∙∙█∙∙∙█∙██∙█∙∙█∙∙∙∙█∙█∙█
    // ████∙█∙█∙█∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙█∙██∙██∙██∙∙█∙█∙∙█∙█∙█∙█∙∙∙█∙█∙██████∙█∙█∙█∙█∙█∙██∙█∙█∙∙█∙∙█∙██∙████
    // ∙█∙██∙█∙∙∙█∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙█∙██∙██∙██∙██∙∙█∙∙█∙█∙∙█∙█∙█∙∙∙█∙█∙█∙█████████████∙██████∙███∙█████∙█∙
    // ██∙█∙█∙██∙∙∙∙∙∙∙∙∙█∙∙∙█∙∙∙∙█∙██∙██∙██∙█∙█∙∙█∙∙∙█∙█∙∙█∙∙█∙███████∙█∙█∙█∙█∙█∙███∙█∙███∙████∙█∙██∙█
    // ∙██∙██∙█∙∙█∙∙∙∙█∙∙∙∙∙∙∙∙∙∙∙██∙██∙██∙███∙█∙∙∙∙█∙∙█∙█∙∙∙█∙∙█∙█∙█∙█████████████∙█████∙██∙█∙████∙███
    // █∙██∙█∙∙█∙∙∙∙█∙∙∙∙∙∙█∙∙∙█∙∙█∙█∙██∙██∙█∙█∙█∙█∙█∙█∙█∙∙█∙∙∙█∙██████∙██∙█∙██∙█∙████∙█████████∙████∙█
    // ∙██∙█∙██∙█∙█∙∙∙∙∙█∙∙∙∙█∙∙█∙██∙█∙██∙██∙██∙∙█∙█∙█∙█∙∙█∙∙█∙∙██∙█∙███∙█████∙████∙███∙█∙█∙█∙███∙█∙███
    // █∙███∙█∙∙∙█∙∙█∙█∙∙█∙█∙∙█∙∙█∙████∙██∙██∙∙█∙∙██∙█∙∙█∙∙∙∙∙███████∙████∙█∙████∙███∙████████∙██████∙█
    // ██∙█∙█∙██∙∙█∙∙∙∙█∙∙█∙█∙∙█∙∙█∙█∙█∙█∙█∙██∙∙∙█∙∙█∙∙█∙∙█∙█∙∙∙█∙█∙█∙█∙██████∙████∙███∙█∙█∙███∙█∙█∙███
    // ∙██∙██∙█∙█∙∙█∙█∙∙█∙∙█∙█∙█∙█∙██∙██████∙∙∙∙∙∙█∙∙∙∙∙∙∙∙∙∙∙█∙████∙█∙█∙█∙█∙███∙████∙██████∙███████∙█∙
    // █∙██∙█∙█∙∙█∙█∙∙█∙█∙█∙∙█∙∙█∙█∙██∙█∙∙∙∙∙█∙∙∙∙∙∙∙█∙█∙█∙█∙∙∙█∙∙∙█∙█∙███████∙███∙████∙██∙██∙█∙█∙█████
    // ██∙██∙█∙█∙∙█∙█∙∙∙∙█∙█∙∙█∙█∙██∙█∙████∙█∙∙∙█∙█∙∙∙∙∙∙∙∙∙█∙█∙∙█∙██∙██∙█∙█∙███∙███∙███∙█████████∙█∙█∙
    // ∙██∙██∙█∙█∙∙∙∙∙∙█∙∙█∙█∙█∙∙█∙█∙██∙█∙█∙∙∙█∙∙∙∙█∙█∙█∙█∙█∙∙∙█∙█∙█∙█∙███████∙███∙██∙████∙█∙█∙█∙██████
    // █∙██∙█∙█∙∙█∙█∙█∙∙█∙█∙█∙█∙█∙███∙██∙█∙█∙█∙∙█∙█∙∙∙∙∙∙∙█∙∙█∙█∙∙█∙█∙██∙█∙█∙███∙█████∙█∙██████∙██∙█∙█∙
    // ██∙█∙██∙█∙█∙█∙∙∙∙∙█∙∙█∙∙█∙█∙█∙██∙██∙█∙∙█∙∙█∙█∙█∙█∙█∙∙█∙█∙█∙█∙██∙██∙████∙███∙█∙████∙█∙█∙███∙█████
    // ∙███∙█∙█∙∙∙█∙∙█∙█∙∙█∙█∙█∙█∙███∙███∙█∙█∙∙█∙∙█∙█∙∙∙█∙∙█∙∙∙█∙█∙██∙██∙██∙███∙█████∙█∙██████∙███∙█∙█∙
    // █∙█∙██∙█∙█∙∙█∙∙∙█∙█∙█∙█∙████∙███∙███∙██∙∙█∙∙█∙█∙█∙∙█∙∙█∙█∙█∙█∙██∙████∙███∙█∙██████∙█∙█∙██∙████∙█
    // ██∙██∙█∙█∙█∙█∙█∙∙█∙█∙█∙██∙███∙████∙██∙∙█∙∙█∙∙∙∙█∙∙█∙∙█∙█∙█∙███∙███∙███∙█████∙█∙█∙██████∙██∙█∙███
    // ∙██∙█∙█∙∙█∙∙█∙∙█∙█∙∙█∙██∙██∙███∙███∙███∙█∙∙█∙∙█∙∙█∙∙█∙∙█∙█∙█∙███∙███∙███∙█∙██████∙█∙█∙███∙███∙█∙
    // █∙████∙█∙∙█∙∙█∙█∙∙██∙██∙█████∙██∙███∙█∙█∙█∙█∙∙∙█∙∙█∙∙█∙█∙██∙██∙███∙███∙████∙█∙█∙██████∙████∙████
    // ██∙█∙██∙█∙█∙█∙∙█∙█∙██∙████∙██████∙█∙██∙█∙█∙█∙█∙∙█∙∙█∙█∙█∙█∙██∙██∙███∙███∙█████∙██∙█∙█∙██∙█∙██∙█∙
    // ∙██∙█∙█∙█∙∙█∙█∙█∙██∙████∙███∙█∙█∙███∙██∙█∙█∙██∙█∙█∙█∙█∙██∙██∙█████∙███∙██∙█∙█∙██∙█████∙██∙██∙███
    // █∙██∙██∙∙█∙∙█∙∙█∙█∙██∙████∙███████∙██∙██∙█∙█∙█∙█∙∙█∙█∙█∙██∙███∙█∙███∙██∙████∙█████∙█∙██∙███∙██∙█
    // ∙██∙█∙█∙█∙█∙∙█∙█∙██∙███∙████∙█∙█∙██∙██∙██∙█∙∙██∙██∙█∙██∙█∙██∙████∙█∙██∙██∙█∙██∙█∙████∙███∙███∙██
    // █∙████∙█∙∙∙█∙█∙██∙███∙██∙█∙██████∙██∙██∙██∙█∙∙█∙█∙█∙█∙████∙██∙█∙█████∙██∙█∙██∙███∙█∙███∙██∙███∙█
    // ∙██∙∙██∙█∙█∙█∙∙█∙█∙███∙██∙██∙█∙███∙██∙██∙█∙█∙██∙█∙██∙█∙█∙██∙████∙█∙█∙██∙███∙██∙█∙███∙█∙██∙██∙███
    // █∙███∙█∙█∙∙█∙█∙██∙█∙█∙█∙██∙██∙█∙█∙████∙██∙██∙█∙██∙█∙███∙██∙██∙█∙██████∙█∙█∙██∙████∙█████∙██∙██∙█
    // ∙██∙█∙█∙∙█∙∙∙█∙█∙█∙███∙█∙█∙█∙█████∙█∙██∙██∙███∙█∙███∙█∙██∙██∙████∙█∙█∙██∙██∙██∙█∙██∙█∙█∙████∙██∙
    // █∙██∙███∙∙██∙█∙█∙██∙█∙█∙█∙███∙█∙█∙███∙██∙██∙∙██∙█∙█∙███∙███∙██∙█∙█████∙██∙██∙█∙██∙███████∙█∙████
    // ∙██∙█∙█∙█∙∙∙█∙█∙█∙████∙██∙█∙██∙███∙███∙██∙█∙∙█∙██∙██∙█∙██∙██∙█████∙█∙██∙█∙█∙█∙██∙██∙█∙█∙██∙██∙█∙
    // █∙███∙█∙∙█∙█∙█∙█∙█∙█∙██∙█∙█∙█∙██∙██∙█∙██∙███∙███∙█∙██∙██∙██∙██∙█∙████∙██∙██∙██∙██∙██∙█∙█∙██∙██∙█
    // ∙██∙█∙██∙∙█∙∙█∙█∙█∙██∙█∙█∙██∙███∙█████∙██∙█∙██∙████∙██∙██∙██∙██∙██∙███∙██∙██∙█∙███∙██████∙██∙███
    // █∙∙█∙█∙█∙█∙█∙█∙█∙██∙██∙█∙█∙██∙███∙█∙█∙██∙███∙██∙█∙█∙█∙██∙██∙██∙██∙██∙██∙█∙█∙█∙█∙█∙██∙█∙█∙████∙█∙
    // ∙█∙∙█∙█∙∙∙∙∙█∙██∙█∙█∙██∙█∙█∙██∙█∙███∙██∙██∙∙█∙██∙█∙███∙██∙██∙██∙███∙██∙█∙█∙█∙█████∙████∙██∙█∙███
    // ∙█∙█∙█∙██∙█∙█∙█∙█∙█∙█∙█∙█∙██∙██∙██∙██∙██∙█∙█∙█∙██∙█∙█∙█∙██∙██∙███∙██∙███∙██∙█∙█∙█∙██∙█∙████∙██∙█
    // ∙█∙∙█∙█∙∙█∙█∙█∙█∙█∙█∙∙█∙█∙█∙██∙██∙██∙██∙███∙███∙███∙████∙██∙██∙█∙██∙██∙██∙█∙█∙██∙██∙████∙█∙█████
    // ∙█∙█∙█∙█∙∙∙∙█∙█∙█∙█∙█∙█∙█∙███∙██∙████∙██∙█∙██∙██∙█∙█∙█∙█∙███∙████∙███∙█∙█∙█∙██∙██∙██∙█∙█████∙█∙█
    // ∙█∙█∙█∙█∙█∙█∙█∙█∙█∙█∙█∙█∙█∙█∙█∙███∙█∙█∙███∙█∙█∙██∙██∙█∙██∙█∙██∙█∙██∙███∙██∙█∙██∙██∙█∙██∙█∙∙██∙█∙
    // ∙█∙█∙█∙█∙∙█∙█∙█∙█∙██∙∙█∙██∙████∙█∙██∙██∙█∙█∙███∙█∙█∙███∙█∙██∙█∙██∙██∙█∙█∙█∙█∙█∙██∙∙∙█∙█∙█∙█∙█∙██
    // ∙█∙█∙█∙∙█∙∙█∙█∙█∙█∙█∙█∙█∙██∙█∙████∙██∙█∙████∙█∙██∙██∙∙█∙██∙██∙█∙██∙██∙██∙█∙█∙██∙██∙█∙█∙█∙██∙█∙█∙
    // ∙█∙∙█∙∙∙∙█∙█∙█∙█∙█∙█∙∙█∙█∙█∙█∙█∙█∙█∙██∙█∙∙███∙██∙█∙███∙█∙██∙████∙██∙██∙██∙█∙█∙██∙█∙∙█∙███∙██∙█∙█
    // ∙∙∙∙∙∙█∙█∙∙∙█∙█∙█∙█∙█∙█∙█∙██∙██∙████∙█∙█∙█∙█∙██∙∙█∙∙∙∙███∙█∙█∙█∙█∙██∙█∙█∙█∙███∙██∙█∙█∙∙∙█∙█∙██∙∙
    // ∙█∙█∙∙∙█∙∙∙█∙█∙█∙█∙█∙∙█∙██∙██∙██∙█∙█∙████∙∙█∙███∙∙∙∙∙∙∙█∙█∙██∙██∙█∙█∙██∙█∙█∙∙██∙████∙█████∙█∙█∙█
    // ∙∙∙∙█∙∙∙∙█∙∙█∙█∙█∙█∙█∙∙█∙██∙█∙█∙█∙███∙█∙██∙∙█∙█∙█∙█∙█∙███∙█∙██∙████∙█∙██∙███∙█∙██∙██████∙█∙█∙█∙∙
    // █∙█∙█∙███∙██∙█∙█∙█∙█∙█∙∙█∙█∙████∙█∙█∙██∙█∙█∙███∙██∙██∙█∙██∙█∙█∙█∙█∙███∙█∙█∙█∙██∙██∙███∙████∙█∙█∙
    // ████∙██∙██∙█∙█∙█∙█∙█∙∙█∙██∙█∙█∙████∙█∙██∙█∙█∙█∙█∙██∙██∙█∙∙██∙∙∙█∙██∙█∙█∙█∙█∙█∙██∙█∙██████∙██∙█∙∙
    // ∙∙∙██∙██∙██∙█∙█∙█∙█∙█∙∙∙∙██∙██∙█∙∙████∙██∙∙∙█████∙██∙█∙██∙█∙██∙█∙█∙██∙█∙█∙█∙██∙██∙██∙████████∙█∙
    // ∙█∙∙∙█∙∙∙∙█∙██∙∙█∙█∙∙█∙█∙∙██∙█∙█∙█∙█∙█∙█∙█∙██∙█∙∙█∙█∙██∙█∙█∙█∙∙∙█∙█∙█∙██∙█∙█∙██∙██∙█∙█∙██∙██████
    // ∙∙█∙█∙∙█∙█∙█∙█∙█∙█∙█∙∙∙█∙█∙█∙██∙██∙█∙██∙██∙∙█∙∙█∙█∙██∙█∙█∙█∙███∙████∙█∙█∙█∙█∙█∙██∙██∙██∙██∙█∙██∙
    // █∙█∙∙█∙█∙██∙█∙█∙█∙█∙█∙█∙∙∙█∙█∙∙█∙██∙█∙██∙█∙███∙∙∙∙∙∙∙█████∙██∙█∙∙∙∙██∙█∙█∙█∙███∙██∙██∙██∙█∙██∙██
    // ██∙████∙██∙█∙█∙∙██∙█∙∙∙∙█∙∙█∙██∙█∙████∙█∙█∙█∙∙∙█∙∙∙█∙∙█∙█∙█∙█∙██∙█∙∙∙█∙█∙█∙█∙█∙█∙██∙███████∙█∙∙█
    // ∙█∙█∙∙█∙∙█∙█∙█∙█∙█∙█∙█∙∙∙∙█∙█∙∙███∙∙█∙█∙█∙█∙█∙∙∙∙█∙█∙█∙∙∙█∙∙█∙∙∙█∙██∙█∙∙█∙█∙█∙███∙█∙█∙█∙█∙█∙█∙█∙
    // ∙∙█∙█∙∙█∙∙█∙█∙█∙█∙█∙∙∙█∙█∙∙█∙██∙∙█∙█∙█∙█∙█∙█∙█∙█∙█∙█∙∙∙█∙∙∙∙∙█∙∙██∙∙∙█∙█∙∙█∙██∙█∙██∙██∙█∙█∙█∙█∙█
    // █∙∙∙∙█∙∙█∙█∙█∙█∙█∙█∙█∙∙∙∙∙█∙∙█∙█∙█∙∙∙∙∙∙∙∙█∙∙█∙█∙██∙█∙∙∙∙∙█∙∙∙█∙∙∙█∙█∙█∙█∙█∙█∙█∙█∙██∙∙∙∙∙∙∙∙█∙█∙
    // ████∙∙█∙∙█∙█∙█∙█∙█∙█∙∙█∙∙█∙█∙█∙█∙∙█∙█∙∙█∙∙█∙█∙███∙∙█∙∙∙∙∙∙∙∙∙∙∙∙█∙█∙█∙∙█∙█∙█∙████∙█∙∙█∙█∙█∙∙████
    // █∙█∙██∙█∙∙█∙█∙∙█∙█∙∙█∙∙∙∙∙█∙█∙█∙∙█∙∙∙∙█∙∙█∙∙∙█∙█∙█∙∙∙█∙∙∙█∙∙∙█∙∙∙███∙█∙∙█∙∙██∙█∙∙█∙██∙█∙█∙██∙█∙█
    // ████∙█∙██∙██∙█∙██∙█∙∙█∙∙█∙█∙█∙█∙∙∙█∙█∙∙∙∙∙█∙∙█∙█∙∙∙█∙∙∙∙∙∙∙∙∙∙∙██∙∙∙███∙∙█∙█∙█∙∙∙███████∙█∙█∙█∙█
    // █∙█████∙██∙∙█∙█∙█∙██∙∙∙∙█∙∙█∙█∙∙█∙∙∙∙∙∙∙█∙∙█∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙█∙█∙∙∙█∙█∙█∙∙∙∙∙∙∙█∙█∙█████∙█∙█∙
    // ∙█∙█∙█∙█∙█∙∙∙∙∙█∙█∙∙∙∙█∙∙█∙∙█∙█∙∙∙∙∙∙∙█∙∙∙█∙∙∙∙∙∙∙∙∙∙∙∙█∙∙∙█∙∙∙█∙∙∙∙∙██∙█∙∙█∙█∙█∙██████∙█∙██████
    // █∙∙█∙∙█∙█∙█∙█∙█∙█∙██∙∙∙∙∙∙█∙∙∙∙∙█∙∙█∙∙∙∙∙∙∙∙█∙█∙█∙█∙∙∙∙∙∙█∙∙∙∙∙██∙█∙∙∙█∙∙∙█∙∙█∙∙∙∙∙█∙█∙█████∙██∙
    // ∙█∙∙█∙█∙∙█∙∙∙∙∙∙█∙∙∙█∙█∙█∙∙∙█∙█∙∙∙∙∙∙∙∙∙∙∙█∙∙∙█∙∙█∙∙█∙█∙∙∙∙∙∙∙∙█∙∙∙█∙███∙∙∙██∙██∙█∙█∙██∙█∙██████
    // ∙█∙█∙∙∙█∙∙█∙█∙█∙∙∙█∙∙∙∙∙∙██∙█∙∙█∙∙∙∙∙∙█∙∙∙∙∙∙∙∙█∙∙█∙█∙∙∙∙∙∙∙∙∙∙█∙█∙∙██∙█∙█∙█∙█∙█∙█∙∙█∙∙█∙█∙█∙█∙█
    // ∙█∙∙█∙█∙█∙∙█∙█∙∙█∙∙█∙∙█∙█∙█∙∙█∙█∙█∙∙∙∙∙∙∙█∙∙∙█∙∙█∙∙∙∙█∙█∙∙∙∙∙█∙∙█∙█∙███∙█∙█∙█∙█∙█∙█∙∙█∙█∙█∙█∙███
    // ∙∙█∙∙█∙∙∙█∙∙█∙█∙█∙█∙█∙█∙█∙∙█∙∙∙∙∙∙∙█∙∙∙█∙∙∙█∙∙∙∙∙∙█∙∙∙∙∙∙∙█∙∙∙∙∙██∙██∙█∙█∙█∙█∙█∙∙█∙██∙███∙███∙█∙
    // █∙∙█∙∙█∙█∙█∙█∙█∙█∙█∙█∙█∙∙█∙∙∙█∙█∙█∙∙∙█∙∙∙∙∙∙∙∙█∙█∙█∙∙█∙∙∙∙∙∙∙∙∙∙∙████∙█∙∙█∙█∙█∙█∙∙█∙███∙███∙████
    // ∙█∙█∙█∙█∙∙∙█∙█∙█∙█∙█∙∙∙█∙∙█∙∙∙∙∙∙∙∙█∙∙∙∙█∙∙∙█∙∙█∙█∙█∙∙∙█∙█∙∙█∙∙∙∙∙∙█∙∙∙█∙∙█∙█∙█∙█∙∙██████∙███∙██
    // ∙█∙∙█∙∙∙█∙█∙∙∙█∙∙█∙∙█∙∙∙█∙∙∙█∙∙█∙∙∙∙∙∙█∙∙∙█∙∙∙█∙█∙█∙█∙∙∙∙∙∙∙∙∙█∙█∙∙∙∙█∙∙∙∙∙█∙∙∙∙∙█∙∙█∙∙█∙█∙█∙█∙∙
    // ∙█∙█∙█∙█∙∙∙█∙█∙∙█∙∙█∙∙█∙∙∙█∙∙∙∙∙█∙█∙█∙∙∙∙∙∙∙█∙█∙█∙█∙█∙█∙∙∙█∙∙∙∙∙∙∙█∙∙∙∙∙∙█∙∙∙█∙█∙∙∙█∙█∙█∙█∙█∙█∙█
    // ∙█∙∙∙█∙∙█∙∙∙∙∙∙█∙∙∙∙∙∙∙∙█∙∙∙∙█∙∙∙∙∙∙∙∙█∙█∙█∙█∙█∙█∙██∙█∙∙█∙∙∙█∙∙█∙∙∙∙∙∙█∙∙∙∙█∙∙∙∙∙█∙∙█∙█∙█∙█∙█∙█∙
    // ∙█∙█∙∙█∙∙█∙█∙█∙∙∙∙█∙█∙█∙∙∙∙█∙∙█∙█∙█∙█∙∙∙∙∙█∙█∙█∙█∙∙█∙█∙∙∙∙∙∙∙∙∙∙∙∙█∙∙∙∙∙█∙∙∙∙∙█∙∙∙█∙█∙∙█∙∙█∙∙█∙█
    // █∙█∙█∙█∙█∙∙∙∙∙∙∙█∙∙∙∙∙∙∙∙█∙∙∙∙∙█∙∙∙█∙█∙██∙∙█∙█∙∙█∙█∙█∙██∙█∙∙█∙█∙█∙∙∙∙∙█∙∙∙∙∙█∙∙∙∙█∙█∙█∙█∙█∙█∙█∙∙
    // ∙█∙█∙█∙█∙█∙█∙∙█∙∙∙∙█∙∙∙█∙∙∙█∙█∙∙██∙∙█∙█∙∙█∙█∙∙█∙█∙█∙█∙∙█∙∙█∙∙∙█∙∙∙∙∙∙█∙∙∙█∙∙∙∙∙█∙∙█∙█∙█∙█∙█∙█∙██
    // ∙█∙∙█∙█∙█∙█∙∙∙∙∙█∙∙∙∙█∙∙∙█∙∙█∙∙█∙∙██∙█∙█∙∙█∙█∙█∙∙█∙█∙█∙██∙█∙█∙███∙█∙∙∙∙∙█∙∙█∙█∙∙█∙█∙█∙█∙█∙█∙██∙█
    // ∙∙█∙∙█∙∙█∙∙∙█∙█∙∙∙█∙∙∙∙█∙██∙█∙█∙∙█∙█∙█∙∙█∙∙█∙█∙█∙∙∙█∙█∙∙█∙██∙█∙∙∙█∙∙∙∙█∙∙████∙██∙█∙█∙█∙█∙█∙█∙█∙█
    // █∙∙█∙∙█∙∙∙█∙∙█∙∙█∙∙∙█∙∙∙█∙██∙█∙██∙█∙█∙█∙∙█∙█∙∙█∙∙█∙∙█∙█∙█∙█∙█∙██∙∙∙█∙∙∙∙██∙████∙██∙█∙█∙██∙█∙█∙█∙
    // ██∙██∙█∙██∙██∙██∙██∙∙∙∙∙█∙███∙█∙█∙██∙█∙█∙█∙█∙█∙∙█∙█∙∙█∙█∙█∙█∙█∙∙█∙∙∙█∙∙∙∙██∙█∙█████∙█∙█∙█∙██∙█∙█
    // ∙██∙████∙███∙████∙∙█∙∙█∙∙█∙█∙█████∙█∙█∙∙█∙∙█∙∙█∙∙∙∙█∙█∙█∙█∙█∙∙█∙█∙█∙∙∙∙█∙█∙██∙█∙█∙█∙██∙█∙█∙█∙█∙█
    // ██∙██∙████∙███∙█∙█∙∙█∙∙∙█∙███∙█∙∙██∙█∙∙█∙█∙█∙█∙█∙█∙∙█∙∙█∙∙█∙█∙∙█∙∙∙∙█∙∙∙∙██∙█∙█∙█∙█∙█∙█∙█∙█∙█∙█∙
    // █∙████∙█∙██∙██∙█∙∙█∙∙∙█∙█∙█∙██∙██∙█∙█∙█∙∙█∙█∙∙█∙∙∙█∙∙█∙∙█∙█∙█∙█∙█∙█∙∙∙█∙∙█∙∙█∙█∙█∙█∙█∙█∙█∙█∙█∙██
    0xff7f8ea5, 0xff8c9bb2, 0xff96a4be, 0xff96a4be, 0xff94a2bc, 0xff96a4be, 0xff97a5c0, 0xff94a2bd, 0xff909eb9, 0xff8a98b3, 0xff8694b1, 0xff8290ad, 0xff74829f, 0xff6a7895, 0xff7380a0, 0xff8592b4, 0xff7986a9, 0xff6b779d, 0xff687598, 0xff727fa2, 0xff7582a4, 0xff677496, 0xff556282, 0xff4d5a7a, 0xff4a5777, 0xff495676, 0xff4c597c, 0xff515e81, 0xff505c84, 0xff48547c, 0xff404c76, 0xff3e4a70, 0xff3a4561, 0xff39455d, 0xff48546a, 0xff5c687e, 0xff5b6a7d, 0xff606f82, 0xff8e9faf, 0xffc9daea, 0xffe1f4ff, 0xffdef1ff, 0xffd5eaff, 0xffcbe0f5, 0xffbacee7, 0xffa5b9d2, 0xff8fa4bf, 0xff7f96b5, 0xff7f94bf, 0xff8197c6, 0xff8399c8, 0xff8399c8, 0xff8297c4, 0xff8196c3, 0xff8196c3, 0xff8398c5, 0xff899bc9, 0xff8a9cca, 0xff889bc6, 0xff8598c3, 0xff879ac5, 0xff8c9fca, 0xff8da0cb, 0xff879eca, 0xff8ba5d6, 0xff84a3d2, 0xff839fcf, 0xff83a0cc, 0xff86a2ca, 0xff88a4c9, 0xff8fa8c7, 0xff92acc7, 0xff94acc6, 0xff93acc2, 0xff96aac2, 0xff94a9be, 0xff96a8bc, 0xff99abbf, 0xff9eafc3, 0xffa2b3c3, 0xff99a7b2, 0xffa8b6bf, 0xffa9b7c4, 0xffa1aebf, 0xff9caac4, 0xff94a2bf, 0xff8894ba, 0xff8793bd, 0xff7f8cb8, 0xff7885b2, 0xff7381b0, 0xff7583b0, 0xff7482af, 0xff707fa8, 0xff7382ab, 0xff7988af, 
    0xff8190a7, 0xff91a0b7, 0xff9eacc6, 0xff9fadc7, 0xff98a6c0, 0xff92a0ba, 0xff8b99b4, 0xff8593ae, 0xff808ea9, 0xff7a88a3, 0xff7583a0, 0xff7785a2, 0xff818fac, 0xff8593b0, 0xff7d8aaa, 0xff727f9f, 0xff7582a4, 0xff647193, 0xff546183, 0xff4e5b7b, 0xff475474, 0xff3c4969, 0xff3a4865, 0xff3f4d6a, 0xff404e6b, 0xff3c4969, 0xff394668, 0xff394669, 0xff364268, 0xff2e3a62, 0xff2a365e, 0xff2a365c, 0xff3f4a6a, 0xff444f6b, 0xff4b5672, 0xff4c5a74, 0xff516077, 0xff6b7a91, 0xff9eafc3, 0xffccddf1, 0xffc1d3e9, 0xffb4c6dc, 0xffa0b4cc, 0xff90a4bd, 0xff889cb7, 0xff879bb6, 0xff899ebb, 0xff8ca0c1, 0xff869cc5, 0xff849bc7, 0xff8197c6, 0xff7e95c1, 0xff7d92bf, 0xff7e93c0, 0xff8196c3, 0xff8398c5, 0xff8294c2, 0xff8598c3, 0xff8699c4, 0xff8598c3, 0xff889bc6, 0xff8ea1cc, 0xff90a3ce, 0xff8ca1ce, 0xff859dcd, 0xff85a0cf, 0xff8aa5d2, 0xff90a9d2, 0xff92abd3, 0xff92aacc, 0xff92a9c8, 0xff94a9c4, 0xff96acc4, 0xff99aec3, 0xff9dafc3, 0xff9fb1c5, 0xffa2b3c5, 0xffa6b7c9, 0xffabbacd, 0xffafbfce, 0xffc7d5de, 0xffcbdae1, 0xffccdae5, 0xffcbd8e8, 0xffbccbe2, 0xff9eacc7, 0xff8e9bbe, 0xff99a5cd, 0xff9eabd5, 0xff96a3cf, 0xff909ecb, 0xff909fca, 0xff8f9ec7, 0xff8d9cc3, 0xff91a1c5, 0xff98a8cc, 
    0xff8896b0, 0xff92a0ba, 0xff97a5bf, 0xff8e9cb6, 0xff7f8da8, 0xff76849f, 0xff72809b, 0xff717f9a, 0xff73819e, 0xff7c8aa7, 0xff7c8aa7, 0xff74829f, 0xff7380a0, 0xff7885a5, 0xff707d9d, 0xff606d8d, 0xff576280, 0xff515c78, 0xff4f5a76, 0xff4e5a74, 0xff46526c, 0xff3d4963, 0xff3e4c66, 0xff47556f, 0xff44526d, 0xff394762, 0xff2d3c5b, 0xff2c3b5c, 0xff2e3c5f, 0xff303e63, 0xff35436a, 0xff3a486f, 0xff374369, 0xff435073, 0xff435073, 0xff374466, 0xff3c4969, 0xff5f6e8d, 0xff8a99b6, 0xffa3b2cf, 0xff8e9fbb, 0xff8c9dbb, 0xff889bb9, 0xff879aba, 0xff859ab9, 0xff8498b9, 0xff8195b8, 0xff7f93b8, 0xff879dc6, 0xff8499c4, 0xff8196c3, 0xff8095c0, 0xff8396c1, 0xff8598c3, 0xff879ac5, 0xff879ac5, 0xff889bc6, 0xff8b9ec8, 0xff8b9ec8, 0xff899cc6, 0xff8a9bc6, 0xff8d9ec9, 0xff8d9ec9, 0xff8a9dc8, 0xff90a5d0, 0xff8fa4cf, 0xff8ba1ca, 0xff899dc2, 0xff899dc0, 0xff8da0be, 0xff93a7c0, 0xff99abc1, 0xffa3b4c8, 0xffabbacd, 0xffb7c7d7, 0xffc5d2e2, 0xffd1dded, 0xffdce8f8, 0xffe7f0ff, 0xffeaf6ff, 0xffedfcff, 0xffe8f7fc, 0xffe9faff, 0xfff2ffff, 0xffe4f3ff, 0xffb8c7de, 0xffa8b7d6, 0xffbeccef, 0xffbdcbf0, 0xffb6c4eb, 0xffafbee5, 0xffadbce3, 0xffacbce0, 0xffadbddf, 0xffb2c2e3, 0xffb8c8e9, 
    0xff7b89a3, 0xff7b89a3, 0xff75839d, 0xff67758f, 0xff5a6883, 0xff596782, 0xff64728d, 0xff6f7d98, 0xff72809d, 0xff7a88a5, 0xff7a88a5, 0xff6f7d9a, 0xff667393, 0xff677494, 0xff687595, 0xff677592, 0xff76829c, 0xff79859b, 0xff7f8ba1, 0xff818da3, 0xff778399, 0xff677389, 0xff606c82, 0xff606f86, 0xff95a4bb, 0xff7989a3, 0xff5b6a87, 0xff4c5b7c, 0xff4a587b, 0xff4c5a7f, 0xff4d5b82, 0xff4e5b85, 0xff616e9b, 0xff6f7aa8, 0xff707ba8, 0xff626f99, 0xff626f99, 0xff7886ad, 0xff8b99be, 0xff8f9fc3, 0xff92a2c4, 0xff8e9ec0, 0xff8698bc, 0xff8395b9, 0xff8397bc, 0xff8599be, 0xff879ac2, 0xff889bc3, 0xff8194be, 0xff7e94bd, 0xff8093bd, 0xff8396c0, 0xff8699c3, 0xff889bc5, 0xff879ac4, 0xff8699c3, 0xff8a9dc7, 0xff8da0c8, 0xff8c9fc9, 0xff879ac2, 0xff8798c3, 0xff8a9cc4, 0xff8b9cc7, 0xff899bc3, 0xff8a9cc4, 0xff8e9dc4, 0xff90a0c4, 0xff94a4c6, 0xff9faecd, 0xffafbfd9, 0xffc0cfe6, 0xffc9d6e9, 0xffdae5f7, 0xffdfebfb, 0xffe9f2ff, 0xffeef8ff, 0xfff2faff, 0xfff4fcff, 0xfff7fdff, 0xfff7ffff, 0xfff5ffff, 0xffeefefe, 0xffebfdff, 0xfff2ffff, 0xffedfdff, 0xffc9d8ed, 0xffb9c9e3, 0xffceddfc, 0xffc0cff0, 0xffbccaed, 0xffb7c7e9, 0xffb5c5e7, 0xffb4c4e5, 0xffb5c6e4, 0xffb9cae6, 0xffbccde9, 
    0xff596782, 0xff5b6984, 0xff5c6a85, 0xff586681, 0xff55637e, 0xff5a6883, 0xff697794, 0xff7785a2, 0xff727f9f, 0xff667393, 0xff5e6b8b, 0xff647191, 0xff6b789a, 0xff6d7a9c, 0xff707d9f, 0xff7782a0, 0xff838ea4, 0xff7b8797, 0xff768292, 0xff7b8797, 0xff8b96a8, 0xffa4b1c2, 0xffc3d0e3, 0xffd8e7fa, 0xffbccbe2, 0xff9babc4, 0xff7a89a6, 0xff7383a4, 0xff8090b2, 0xff90a0c4, 0xff98a7ce, 0xff9baad5, 0xff9ba7db, 0xffa0abe1, 0xffa2aee2, 0xff9fabdd, 0xff9ca8da, 0xff9dabda, 0xff9fadda, 0xff9faed9, 0xff99a8d1, 0xff95a7cf, 0xff91a2cd, 0xff8fa0cb, 0xff8c9fc9, 0xff8a9dc7, 0xff8699c4, 0xff8396c0, 0xff8497bf, 0xff8093bb, 0xff7e90b8, 0xff7c8eb6, 0xff7d8fb7, 0xff8193bb, 0xff8496be, 0xff8597bf, 0xff8799c1, 0xff8b9dc3, 0xff8c9ec6, 0xff889ac0, 0xff8799c1, 0xff8c9ec4, 0xff90a2ca, 0xff93a2c9, 0xff99a6c9, 0xffa8b2d5, 0xffb7c2e2, 0xffc5d0ee, 0xffd7e1fc, 0xffe6f1ff, 0xffecf4ff, 0xffeaf3ff, 0xfff4fbff, 0xfff8feff, 0xfffbffff, 0xfffbffff, 0xfff8faff, 0xfff4f6ff, 0xfff0f2ff, 0xffecf3fd, 0xffeffdfe, 0xffe6f8f8, 0xffdceef2, 0xffd9eaf2, 0xffd7e7f4, 0xffc5d4e7, 0xffb6c6dd, 0xffbbcbe5, 0xffb4c5e1, 0xffb4c5e3, 0xffb4c5e3, 0xffb2c3e1, 0xffb0c4df, 0xffb1c5de, 0xffb1c5dd, 0xffb0c4dc, 
    0xff4b5974, 0xff52607b, 0xff5e6c87, 0xff66748f, 0xff66748f, 0xff62708d, 0xff63718e, 0xff687693, 0xff677494, 0xff5c6989, 0xff586585, 0xff5e6b8b, 0xff5f6c8e, 0xff5e6b8d, 0xff6a7799, 0xff7e89a7, 0xff808b9f, 0xff929baa, 0xffabb7c5, 0xffc6d2e0, 0xffd6e2f2, 0xffd9e4f6, 0xffd1deef, 0xffc8d7ea, 0xffe9f8ff, 0xffc7d7f1, 0xffa2b3d1, 0xff93a3c5, 0xff96a6ca, 0xff9aa9d0, 0xff97a8d3, 0xff94a5d3, 0xffa2afe6, 0xffa0aae7, 0xffa1abe6, 0xffa4afe7, 0xffa3b0e5, 0xff9fadde, 0xffa0aedd, 0xffa4b2df, 0xff98a9d4, 0xff9bacd7, 0xff9fb0db, 0xffa2b3de, 0xff9eb1dc, 0xff95a8d3, 0xff889bc6, 0xff7f90bb, 0xff8c9ec4, 0xff8696ba, 0xff7b8baf, 0xff6f7fa3, 0xff6a7a9e, 0xff6e7ea2, 0xff7888ac, 0xff7e90b4, 0xff8a9abe, 0xff8fa1c5, 0xff91a3c9, 0xff8d9fc3, 0xff8b9dc3, 0xff8c9ec4, 0xff8ea0c6, 0xff90a0c4, 0xffa6b0d3, 0xffb0b9d8, 0xffbcc5e2, 0xffc9d3ee, 0xffe1e8ff, 0xfff5fdff, 0xfff8ffff, 0xfff5fcff, 0xfff2f8ff, 0xfff6faff, 0xfff7fbff, 0xfff7f9ff, 0xfff3f5ff, 0xfff1f3ff, 0xfff1f0fe, 0xffeef2fd, 0xffdbe9ec, 0xffd7e9eb, 0xffc8d9e0, 0xffbecfd7, 0xffc4d4e3, 0xffc2d3e5, 0xffb8c8df, 0xffb5c5de, 0xffb5c6e2, 0xffb8c9e5, 0xffbacbe7, 0xffb8c9e5, 0xffb6cae5, 0xffb7cbe4, 0xffb6cae2, 0xffb2c7dc, 
    0xff505e7b, 0xff52607d, 0xff596784, 0xff62708d, 0xff616f8c, 0xff586585, 0xff536080, 0xff536080, 0xff556284, 0xff556284, 0xff536082, 0xff505d7f, 0xff536083, 0xff647194, 0xff8491b4, 0xffa1acca, 0xffc1cce0, 0xffc9d2e3, 0xffd1daeb, 0xffd9e2f3, 0xffdfeafc, 0xffe5f0ff, 0xffe7f4ff, 0xffe5f4ff, 0xffbbc9e3, 0xffa9b8d5, 0xff97a7c8, 0xff92a2c6, 0xff93a5cd, 0xff95a6d1, 0xff96a7d5, 0xff97a7da, 0xff96a2dc, 0xff95a1dd, 0xff94a0da, 0xff95a2d9, 0xff96a3d8, 0xff98a6d7, 0xff9aa8d7, 0xff9ba9d6, 0xffa2b3de, 0xff9dafd7, 0xff95a7cf, 0xff90a2ca, 0xff8d9ec9, 0xff8c9dc8, 0xff8b9cc7, 0xff8b9dc5, 0xff7583a8, 0xff707ea1, 0xff667497, 0xff576588, 0xff4c5a7d, 0xff4c5a7d, 0xff566487, 0xff5f6f91, 0xff7280a3, 0xff7c8cae, 0xff8696ba, 0xff8797b9, 0xff8696ba, 0xff8595b9, 0xff8494b8, 0xff8391b4, 0xff8590b0, 0xff8a93b2, 0xff939ab7, 0xffa2a9c5, 0xffc4cbe5, 0xffe7efff, 0xfff7feff, 0xfff5fcff, 0xffedf3ff, 0xffebf1ff, 0xffe6ecfa, 0xffe0e4f0, 0xffd8dbea, 0xffd2d5e4, 0xffd0d2e1, 0xffcdd3e1, 0xffc1ced7, 0xffc0d1d9, 0xffb4c6d0, 0xffaec0ce, 0xffb8c9d9, 0xffbccde1, 0xffb9c9e2, 0xffbacbe5, 0xffb7c8e4, 0xffbbccea, 0xffbacdeb, 0xffb7cbe6, 0xffb7cbe6, 0xffbacee7, 0xffbacee7, 0xffb5c9e1, 
    0xff505e7b, 0xff485673, 0xff44526f, 0xff475572, 0xff495676, 0xff495676, 0xff4d5a7a, 0xff546181, 0xff485577, 0xff404d6f, 0xff344163, 0xff384568, 0xff5f6c8f, 0xff9aa7ca, 0xffc5d2f5, 0xffd7e2ff, 0xffdce6ff, 0xffe7efff, 0xffeef6ff, 0xffe7f2ff, 0xffd4dff3, 0xffbbc7dd, 0xffa3afc7, 0xff92a0ba, 0xff909ebb, 0xff8b9abb, 0xff8997bc, 0xff8a99c2, 0xff8997c4, 0xff8596c4, 0xff8797c8, 0xff8b9bce, 0xff8794cb, 0xff8d99d3, 0xff8e9bd2, 0xff8996ca, 0xff8997c8, 0xff8e9cc9, 0xff8e9dc8, 0xff8897c0, 0xff808fb6, 0xff8395b9, 0xff8d9dc1, 0xff91a3c7, 0xff90a0c4, 0xff8395b9, 0xff7584ab, 0xff69799d, 0xff495678, 0xff4d5878, 0xff4a5575, 0xff404b6b, 0xff333e5e, 0xff2b3858, 0xff303d5d, 0xff374665, 0xff414e70, 0xff526182, 0xff667596, 0xff7080a1, 0xff7686a8, 0xff7a8aac, 0xff7a8aac, 0xff7987aa, 0xff7f89ac, 0xff838eac, 0xff8891b0, 0xff959fba, 0xffb0bad5, 0xffc9d4ea, 0xffccd4e9, 0xffbec6d9, 0xffbcc3d5, 0xffbbc2d2, 0xffb8bfd1, 0xffb4bbcd, 0xffb1b8ca, 0xffb1b6c9, 0xffb4b9cd, 0xffb5bdd0, 0xffb0bdcd, 0xffafbfce, 0xffa6b7c7, 0xffa5b6c8, 0xffacbcd3, 0xffabbbd4, 0xffa9bad6, 0xffb4c5e3, 0xffb0c1df, 0xffb4c4e5, 0xffb1c4e4, 0xffacbfdf, 0xffacbfdd, 0xffb1c4e2, 0xffb2c6e1, 0xffafc3de, 
    0xff4a5777, 0xff455272, 0xff3f4c6c, 0xff3b4868, 0xff3a4769, 0xff3d4a6c, 0xff424f71, 0xff455274, 0xff445174, 0xff505d80, 0xff4d5a7d, 0xff445076, 0xff616d93, 0xffa1add3, 0xffd4e0ff, 0xffe6f0ff, 0xffd0d9f6, 0xffc7d1ea, 0xffb9c3de, 0xffabb5d0, 0xff9fa9c4, 0xff98a4be, 0xff96a1bd, 0xff94a2bf, 0xff98a5c7, 0xff94a2c5, 0xff95a3ca, 0xff99a8d3, 0xff9aa8d7, 0xff95a3d4, 0xff95a2d6, 0xff99a6da, 0xff98a5d9, 0xff97a5d6, 0xff93a1d0, 0xff8e9cc9, 0xff8e9dc8, 0xff909fc6, 0xff8d9bc0, 0xff8695b6, 0xff8e9dbc, 0xff8c9dbb, 0xff8c9bba, 0xff8091af, 0xff647392, 0xff40516f, 0xff314061, 0xff324160, 0xff2f3a56, 0xff39435e, 0xff39425f, 0xff2a3350, 0xff1f2845, 0xff232e4a, 0xff2f3a58, 0xff33415e, 0xff2e3959, 0xff2f3c5c, 0xff323f5f, 0xff404f6e, 0xff637293, 0xff8998b9, 0xff98a7c8, 0xff92a0c3, 0xff9ca9cc, 0xff8e9bbd, 0xff93a0c2, 0xff9ba9c6, 0xff98a6c3, 0xffa2b0ca, 0xffa7b6cd, 0xff97a6bb, 0xff9da9bf, 0xff9ba8bb, 0xff98a4ba, 0xff95a1b7, 0xff919db5, 0xff909ab3, 0xff8e98b1, 0xff8c98b0, 0xff8d9cb3, 0xff8c9cb3, 0xff8c9cb5, 0xff8c9cb6, 0xff8b9cb8, 0xff8b9cba, 0xff8b9bbc, 0xff8b9bbd, 0xff899cbd, 0xff8a9dbe, 0xff8b9ebf, 0xff8c9fc0, 0xff8da1c2, 0xff8ea2c3, 0xff90a5c4, 0xff90a5c4, 
    0xff3f4c6c, 0xff3d4a6a, 0xff394666, 0xff364365, 0xff374466, 0xff3c496b, 0xff445173, 0xff495679, 0xff4b587b, 0xff556285, 0xff576389, 0xff566288, 0xff6b779d, 0xff929ec4, 0xffacb8de, 0xffb1bee1, 0xff949fbf, 0xff939ebc, 0xff939cbb, 0xff939ebc, 0xff96a1bf, 0xff9aa5c3, 0xff9ca9c9, 0xff9facce, 0xff96a3c5, 0xff939fc5, 0xff93a1c8, 0xff98a5d1, 0xff99a6d3, 0xff95a3d2, 0xff97a3d5, 0xff9aa8d9, 0xff8f9dca, 0xff92a1ca, 0xff93a2c9, 0xff91a1c5, 0xff909ec1, 0xff91a0bf, 0xff94a3c0, 0xff96a6c0, 0xff8795af, 0xff909fb6, 0xff7f8ea5, 0xff54637a, 0xff34435a, 0xff2c3a54, 0xff25334d, 0xff19253f, 0xff1b223c, 0xff262c44, 0xff2b314b, 0xff252c46, 0xff222943, 0xff252f48, 0xff2c3651, 0xff2d3953, 0xff313c58, 0xff2f3a56, 0xff2c3755, 0xff2f3d5a, 0xff485673, 0xff7281a0, 0xff99a8c7, 0xffafbedf, 0xffb4c2e7, 0xffadbde1, 0xffb3c1e4, 0xffb7c7e8, 0xffb2c1e0, 0xffafc0dc, 0xffb6c6e0, 0xffbacae4, 0xffb4c4dd, 0xffb2c2db, 0xffb0c0d9, 0xffaebed8, 0xffacbbd8, 0xffaab9d6, 0xffaab8d5, 0xffa9b7d4, 0xff99a8c5, 0xff98a7c4, 0xff96a5c4, 0xff95a4c5, 0xff92a2c3, 0xff92a2c4, 0xff92a2c6, 0xff92a2c6, 0xff8fa1c5, 0xff8fa1c5, 0xff90a2c6, 0xff90a2c6, 0xff90a4c7, 0xff91a5c8, 0xff91a5c8, 0xff91a5c6, 
    0xff3e4b6d, 0xff3f4c6e, 0xff404d6f, 0xff3f4c6f, 0xff3e4b6e, 0xff414e71, 0xff475477, 0xff4c587e, 0xff566288, 0xff58648a, 0xff5d6991, 0xff67739b, 0xff7884ac, 0xff8894bc, 0xff8f9bc3, 0xff8f9bc1, 0xff909cc2, 0xff919ec1, 0xff949ec2, 0xff93a0c3, 0xff93a0c2, 0xff93a0c2, 0xff919ec0, 0xff909dbf, 0xff9daacc, 0xff9aa7ca, 0xff9aa6cc, 0xff9da9d1, 0xff9da9d3, 0xff9aa7d3, 0xff9ba6d4, 0xff9daad6, 0xff98a6c9, 0xff9baac9, 0xff9daccb, 0xff9aaac4, 0xff94a2bc, 0xff909fb6, 0xff95a4b9, 0xff9eadc0, 0xffbfccdd, 0xffa8b5c6, 0xff758293, 0xff3e4b5c, 0xff283546, 0xff2b384b, 0xff222f42, 0xff0e192d, 0xff0f152b, 0xff161b31, 0xff1c2137, 0xff21273d, 0xff272d45, 0xff2e364d, 0xff333a54, 0xff313b54, 0xff2f3954, 0xff2b3751, 0xff27324e, 0xff22304b, 0xff2a3853, 0xff4d5b78, 0xff8593b0, 0xffb2c1e2, 0xffb0c0e4, 0xffaec0e4, 0xffafbfe3, 0xffb1c4e5, 0xffb5c5e6, 0xffabbedc, 0xffadbedc, 0xffbbcce8, 0xffb5c6e2, 0xffb4c5e1, 0xffb4c5e3, 0xffb3c4e2, 0xffb2c2e3, 0xffb1c1e3, 0xffb0c0e2, 0xffb0c0e2, 0xffaabadc, 0xffa8b8da, 0xffa4b4d8, 0xffa1b1d5, 0xff9fafd3, 0xff9eaed2, 0xff9caed4, 0xff9caed4, 0xff99abd1, 0xff99abd1, 0xff98acd1, 0xff98acd1, 0xff98acd1, 0xff98acd1, 0xff96acd1, 0xff96adcf, 
    0xff414e70, 0xff4a5779, 0xff546184, 0xff5a678a, 0xff5b688b, 0xff5b688b, 0xff5d698f, 0xff5f6b91, 0xff576389, 0xff4d597f, 0xff515d85, 0xff67739b, 0xff7e8ab2, 0xff8793bb, 0xff8b97bf, 0xff909cc4, 0xff8f9dc4, 0xff909ec5, 0xff93a1c6, 0xff96a4c9, 0xff98a6c9, 0xff99a6c8, 0xff99a6c6, 0xff99a6c6, 0xff99a6c6, 0xff98a5c5, 0xff98a5c5, 0xff9ba5c8, 0xff9aa4c8, 0xff99a3c7, 0xff99a2c9, 0xff98a5c7, 0xff93a1bc, 0xff98a7be, 0xffa5b4cb, 0xffb6c5da, 0xffc6d3e6, 0xffd2dfef, 0xffdfecfc, 0xffebf7ff, 0xffe0ecf8, 0xff909ca8, 0xff454f5b, 0xff303948, 0xff353e4d, 0xff2f3849, 0xff21293c, 0xff1a2134, 0xff212437, 0xff1a1d30, 0xff15182b, 0xff151a2d, 0xff1b2034, 0xff20263c, 0xff262c42, 0xff283047, 0xff2b334a, 0xff28324b, 0xff28324b, 0xff25314b, 0xff1f2a46, 0xff283651, 0xff55637e, 0xff8493b2, 0xffa5b5d7, 0xffb0c2e6, 0xffadbde1, 0xffaabdde, 0xffbacaeb, 0xffb6c9e7, 0xffafc0de, 0xffb4c7e5, 0xffb3c6e4, 0xffb3c6e4, 0xffb4c7e7, 0xffb4c7e7, 0xffb4c6ea, 0xffb4c6ea, 0xffb4c6ec, 0xffb4c6ec, 0xffb3c3e7, 0xffb1c1e5, 0xffacbce0, 0xffa8b8dc, 0xffa6b5dc, 0xffa5b5d9, 0xffa5b7db, 0xffa6b8dc, 0xffa5b7db, 0xffa5b7db, 0xffa5b9dc, 0xffa5b9dc, 0xffa5b9dc, 0xffa6badd, 0xffa4bbdd, 0xffa4bbdd, 
    0xff384568, 0xff465376, 0xff586588, 0xff667396, 0xff6c789e, 0xff6b779d, 0xff677399, 0xff657197, 0xff4c5880, 0xff3d4971, 0xff414d77, 0xff626e98, 0xff7e8ab4, 0xff8692bc, 0xff8b97c1, 0xff94a1cb, 0xff8a99c2, 0xff8a99c0, 0xff8b9bbf, 0xff8d9dbf, 0xff8f9fc0, 0xff94a3c2, 0xff97a6c3, 0xff9aa8c3, 0xff95a3bd, 0xff9aa6be, 0xff9da9c1, 0xffa1abc4, 0xffa4aec9, 0xffa7b1cc, 0xffa8b2cd, 0xffa8b2cb, 0xffbbc7dd, 0xffbbc8db, 0xffc1cedf, 0xffd0dded, 0xffe6f2ff, 0xfff3ffff, 0xfff1fdff, 0xffebf5ff, 0xffb7c1cb, 0xff5e6872, 0xff1d2530, 0xff272f3c, 0xff414858, 0xff3d4454, 0xff383d50, 0xff434659, 0xff4c4d5f, 0xff393a4c, 0xff262739, 0xff1d2031, 0xff1b1e31, 0xff1a1f32, 0xff21263a, 0xff292f45, 0xff2b3348, 0xff263147, 0xff29334c, 0xff29354d, 0xff1d2943, 0xff17233d, 0xff2c3a54, 0xff4c5a77, 0xff8493b4, 0xffa9b9da, 0xffb5c4e5, 0xffabbcda, 0xffb3c2e1, 0xffb4c5e1, 0xffadbcd9, 0xffaebfdb, 0xffacbdd9, 0xffadbedc, 0xffaebedf, 0xffb0c0e1, 0xffb0c0e4, 0xffb0c0e4, 0xffb0bfe6, 0xffafbee5, 0xffacbce0, 0xffaabade, 0xffa6b6da, 0xffa3b3d5, 0xff9fb1d5, 0xffa0b3d4, 0xffa2b5d6, 0xffa3b6d7, 0xffa6bbda, 0xffa6bbda, 0xffa7bcdb, 0xffa8bddc, 0xffa7bedd, 0xffa9c0df, 0xffaac1e0, 0xffaac1e0, 
    0xff546184, 0xff5d6a8d, 0xff6a779a, 0xff7582a5, 0xff7884aa, 0xff727ea4, 0xff68749a, 0xff616d93, 0xff47537b, 0xff39456d, 0xff434f79, 0xff67739d, 0xff8490ba, 0xff8894be, 0xff8a96c0, 0xff909dc7, 0xff95a7cf, 0xff91a5ca, 0xff8ea2c5, 0xff8da0c1, 0xff8fa2c0, 0xff95a6c0, 0xff9cacc5, 0xff9fb0c4, 0xffb7c4d7, 0xffbecbdc, 0xffc6d2e2, 0xffcdd6e7, 0xffd3dced, 0xffd9e0f0, 0xffdbe2f4, 0xffd7e0f1, 0xffafbbcb, 0xffb0bdcd, 0xffb7c3d1, 0xffcad6e2, 0xffebf7ff, 0xfff7ffff, 0xfff3fdff, 0xffd7e0e9, 0xff707982, 0xff434a54, 0xff212733, 0xff292f3d, 0xff3f4253, 0xff46495c, 0xff4d5063, 0xff58596d, 0xff504f5f, 0xff3e3d4d, 0xff302f3f, 0xff2a2c3b, 0xff252638, 0xff1e2132, 0xff26293c, 0xff33384c, 0xff30374a, 0xff283045, 0xff242f45, 0xff263049, 0xff1e2a44, 0xff16223c, 0xff1c2a44, 0xff2d3b56, 0xff4b5976, 0xff8491b1, 0xffaebbdb, 0xffacbad7, 0xffa0aecb, 0xff9ba9c4, 0xff9fadc8, 0xffa5b5ce, 0xffa3b3cd, 0xffa4b3d0, 0xffa6b5d4, 0xffa7b6d7, 0xffa7b5d8, 0xffa7b5da, 0xffa5b3d8, 0xffa4b4d8, 0xffa3b3d7, 0xffa2b2d4, 0xff9fafd1, 0xff9dadce, 0xff9baece, 0xff9baece, 0xff9db0ce, 0xff9eb2cd, 0xff9bb0cb, 0xff9cb1cc, 0xff9db2cd, 0xff9fb4cf, 0xff9fb7d1, 0xffa0b8d2, 0xffa1b9d3, 0xffa2bad4, 
    0xff727ea4, 0xff707ca2, 0xff707ca2, 0xff717da3, 0xff707ca2, 0xff6a769c, 0xff5f6b93, 0xff57638b, 0xff45517b, 0xff3c4872, 0xff47537d, 0xff6b77a1, 0xff8691be, 0xff8a95c2, 0xff8994c1, 0xff8d9ac4, 0xff879bc0, 0xff879ec0, 0xff8ba2c2, 0xff96abc8, 0xffa5b9d2, 0xffb7c9df, 0xffc7d8ea, 0xffd0e2f0, 0xffe0eefb, 0xffe8f6ff, 0xffeefbff, 0xfff0faff, 0xfff2fbff, 0xfff4fbff, 0xffeff6fe, 0xffe7f0f9, 0xffc0cad6, 0xffc0ccda, 0xffbbc4d3, 0xffbcc6d2, 0xffd1dbe7, 0xffdbe3ee, 0xffb3bcc5, 0xff7d848e, 0xff3b414d, 0xff2e323e, 0xff222534, 0xff252837, 0xff2e2f41, 0xff343549, 0xff393a4f, 0xff403e53, 0xff313040, 0xff2c2b39, 0xff31303e, 0xff373946, 0xff303241, 0xff1e2132, 0xff1e2132, 0xff292e41, 0xff30374a, 0xff262e43, 0xff1d283e, 0xff1e293f, 0xff1d2941, 0xff1c2840, 0xff1d2c43, 0xff23314b, 0xff2e3754, 0xff5e6784, 0xff9da8c4, 0xffbac6e0, 0xffadb9d3, 0xffa1adc5, 0xffa8b4cc, 0xffb2bed6, 0xffb4c0da, 0xffb3c1db, 0xffb5c3de, 0xffb6c4e1, 0xffb5c2e2, 0xffb4c1e3, 0xffb2bfe2, 0xffafbde0, 0xffa9b9da, 0xffa8b9d7, 0xffa7b8d6, 0xffa5b6d4, 0xffa2b6d1, 0xffa2b6d1, 0xffa1b7cf, 0xffa2b8cf, 0xffa0b6cd, 0xffa0b6cb, 0xff9fb8cc, 0xffa0b9cd, 0xffa1bace, 0xffa2bbcf, 0xffa3bcd2, 0xffa3bcd2, 
    0xff4c587e, 0xff445076, 0xff3b476d, 0xff39456b, 0xff3b476d, 0xff3b476d, 0xff364268, 0xff303c62, 0xff404c74, 0xff38446c, 0xff424e76, 0xff636f97, 0xff7e8ab4, 0xff8591bb, 0xff8894be, 0xff8d9bc2, 0xff8496ba, 0xff869abb, 0xff90a3c3, 0xff9db0ce, 0xffb2c3dd, 0xffc8d8ef, 0xffdaebfd, 0xffe6f6ff, 0xffeaf8ff, 0xfff1feff, 0xfff1feff, 0xffecf7fd, 0xffe5eef3, 0xffdfe9eb, 0xffd4dcdf, 0xffc7d0d7, 0xff98a4b0, 0xff9ca9b9, 0xff96a2b2, 0xff8f9bab, 0xffa1aabb, 0xffa9b0c2, 0xff777e8e, 0xff353b49, 0xff333645, 0xff232532, 0xff201f2d, 0xff2d2c3a, 0xff373543, 0xff343041, 0xff312c40, 0xff362f41, 0xff413b49, 0xff46404c, 0xff57545f, 0xff64626d, 0xff555360, 0xff353340, 0xff272634, 0xff2e2d3b, 0xff313040, 0xff27283c, 0xff202237, 0xff1c223a, 0xff1f2640, 0xff212b44, 0xff1e2c46, 0xff202c46, 0xff212a47, 0xff3b425f, 0xff8089a6, 0xffbec8e3, 0xffc0cae5, 0xffb1bbd6, 0xffb4bed7, 0xffb7c1da, 0xffbbc7e1, 0xffbcc8e2, 0xffbec9e5, 0xffbec9e7, 0xffbdc8e6, 0xffb9c6e6, 0xffb6c3e5, 0xffb3c2e3, 0xffb7c6e5, 0xffb5c6e4, 0xffb5c4e1, 0xffb3c4e0, 0xffb2c3dd, 0xffaec2db, 0xffadc1d9, 0xffadc1d9, 0xffb5cadf, 0xffb5cadf, 0xffb4cadf, 0xffb4cadf, 0xffb5cadf, 0xffb5cadf, 0xffb5c9e1, 0xffb5c9e1, 
    0xff404e71, 0xff3e4c6f, 0xff3d4b6e, 0xff324063, 0xff313f62, 0xff475578, 0xff152245, 0xff273457, 0xff3b486b, 0xff404d70, 0xff4f597d, 0xff636d91, 0xff7680a4, 0xff7e88ac, 0xff7b85a9, 0xff747ea2, 0xff6e7b9e, 0xff7481a3, 0xff7481a3, 0xff6f7c9c, 0xff7b86a2, 0xff9ca8c2, 0xffc5d1e9, 0xffe0edff, 0xffdce7f9, 0xffd0dcec, 0xffc1cddb, 0xffb6c2ce, 0xffafb9c3, 0xffa3adb6, 0xff97a1aa, 0xff8d9aa3, 0xff7d8b98, 0xff748494, 0xff7b879d, 0xff758199, 0xff818aa7, 0xff6e7893, 0xff383e58, 0xff34394f, 0xff27283a, 0xff191826, 0xff26232e, 0xff3b363d, 0xff362c35, 0xff2c202c, 0xff3b2d3a, 0xff52424f, 0xff6b5b66, 0xff8e818a, 0xff867b83, 0xff6d636b, 0xff71686d, 0xff625c60, 0xff41373f, 0xff352b34, 0xff2f232f, 0xff322536, 0xff2f263b, 0xff28233a, 0xff20203c, 0xff1e2541, 0xff222d49, 0xff273552, 0xff293251, 0xff232949, 0xff5a6080, 0xff9ca3c0, 0xffa1aac7, 0xff99a2bf, 0xffa0a9c6, 0xff9da6c3, 0xffa2adc9, 0xffa3aeca, 0xffa1afcc, 0xffa2b0cd, 0xffa2b0cd, 0xffa0afcc, 0xffa0afce, 0xff9faecd, 0xffa1b0cd, 0xffa1b0cd, 0xffa3b1ce, 0xffa4b3d0, 0xffa5b4d1, 0xffa6b7d3, 0xffa7b8d2, 0xffa7b8d2, 0xffa5b6d0, 0xffa6b7d1, 0xffa8b8d2, 0xffaabad4, 0xffadbbd6, 0xffafbdd8, 0xffb0bed9, 0xffb1bfda, 
    0xff405072, 0xff405072, 0xff455376, 0xff3e4c6f, 0xff3f4e6f, 0xff536283, 0xff243153, 0xff323f61, 0xff424f71, 0xff4e5b7d, 0xff626c8f, 0xff717b9e, 0xff7882a5, 0xff7680a3, 0xff71799d, 0xff6b7397, 0xff6b7397, 0xff727a9e, 0xff7881a2, 0xff7a83a4, 0xff838cab, 0xff949dba, 0xffa2acc7, 0xffa8b2cb, 0xff949fb5, 0xff8d98ac, 0xff8994a6, 0xff8c98a8, 0xff909caa, 0xff8e9aa6, 0xff8a96a2, 0xff86949f, 0xff8797a4, 0xff7e8f9f, 0xff8291a8, 0xff808ea9, 0xff838eac, 0xff626b8a, 0xff282d4a, 0xff2a2c43, 0xff212032, 0xff27242f, 0xff413a41, 0xff51464a, 0xff433437, 0xff3e2e31, 0xff5f4c52, 0xff836e75, 0xff9d888f, 0xffb4a1a5, 0xffa39396, 0xff897d7d, 0xff928686, 0xff897e7c, 0xff665a5a, 0xff58494c, 0xff3b2730, 0xff35212d, 0xff2d1c2e, 0xff2b1f35, 0xff26223b, 0xff1e223d, 0xff18233f, 0xff192742, 0xff222d4b, 0xff1e2746, 0xff47506f, 0xff838cab, 0xff9aa5c3, 0xff9ba6c4, 0xff9da8c6, 0xff9ca7c5, 0xff97a5c2, 0xff97a5c2, 0xff97a6c3, 0xff97a6c3, 0xff97a6c3, 0xff96a7c3, 0xff95a6c2, 0xff95a6c2, 0xff91a0bd, 0xff91a0bd, 0xff909fbc, 0xff909fbc, 0xff8f9ebb, 0xff8d9eba, 0xff8c9db9, 0xff8c9db9, 0xff8d9eba, 0xff8e9dba, 0xff8f9ebb, 0xff909ebb, 0xff939ebc, 0xff939ebc, 0xff949fbd, 0xff949fbd, 
    0xff3b4b6d, 0xff3b4b6d, 0xff445275, 0xff404e71, 0xff435174, 0xff515f82, 0xff2a3759, 0xff2f3c5e, 0xff515e80, 0xff606d8f, 0xff737da0, 0xff7b85a8, 0xff7782a2, 0xff707b9b, 0xff6d7697, 0xff6e7798, 0xff6a7296, 0xff70789c, 0xff7880a4, 0xff8089aa, 0xff8891b0, 0xff8b94b1, 0xff848ea9, 0xff7a849d, 0xff778298, 0xff747f93, 0xff788395, 0xff8692a2, 0xff929eac, 0xff96a2b0, 0xff96a2b0, 0xff95a3ae, 0xff95a7b1, 0xff8b9da9, 0xff8f9eb1, 0xff93a2b9, 0xff8b97b1, 0xff575e78, 0xff1f243a, 0xff292a3c, 0xff44424f, 0xff544f55, 0xff706866, 0xff786d69, 0xff695a55, 0xff6d5a56, 0xff917c7b, 0xffb19c9b, 0xffbea9a8, 0xffcab7b3, 0xffb8a6a2, 0xffa69790, 0xffb2a59d, 0xffac9f97, 0xff897a75, 0xff756361, 0xff574345, 0xff422d34, 0xff301f29, 0xff302432, 0xff2d293a, 0xff212437, 0xff152034, 0xff14233a, 0xff15233e, 0xff162441, 0xff2c3a57, 0xff62708d, 0xff99a8c5, 0xffadbcd9, 0xffabbad7, 0xffaebdda, 0xffadbeda, 0xffadbeda, 0xffaebfdb, 0xffacc0db, 0xffacc0db, 0xffacc0db, 0xffabbfda, 0xffabbfda, 0xffa9bad6, 0xffa8b9d5, 0xffa7b8d4, 0xffa4b8d3, 0xffa2b6d1, 0xffa1b5d0, 0xff9fb3ce, 0xff9fb3ce, 0xffa1b5d0, 0xffa3b4d0, 0xffa2b3cf, 0xffa1b2ce, 0xffa2b1ce, 0xffa1b0cd, 0xffa0afcc, 0xffa0afcc, 
    0xff445476, 0xff405072, 0xff49577a, 0xff445275, 0xff455376, 0xff4b597c, 0xff2d3a5c, 0xff283557, 0xff2d3a5c, 0xff384567, 0xff454f72, 0xff465073, 0xff414c6c, 0xff3e4969, 0xff434c6d, 0xff495273, 0xff555d81, 0xff575f83, 0xff5a6286, 0xff626a8e, 0xff6a7394, 0xff6c7594, 0xff666f8c, 0xff5e6883, 0xff6c768f, 0xff6a758b, 0xff737e92, 0xff8893a5, 0xff97a3b3, 0xff9ba7b5, 0xff9ba7b7, 0xff9ba9b6, 0xff9eb1b8, 0xff94a8b1, 0xff96a8b6, 0xffa4b1c4, 0xff929db3, 0xff4f556b, 0xff232639, 0xff3b3a48, 0xff6b666c, 0xff746c6a, 0xff877c76, 0xff92847b, 0xff917f75, 0xff9d8980, 0xffb69d98, 0xffc1a8a3, 0xffc4aba6, 0xffcbb4ac, 0xffc1ada4, 0xffb9a99c, 0xffc6b6a9, 0xffbeb0a3, 0xffa18f83, 0xff8a786e, 0xff826a66, 0xff614c4b, 0xff4a353a, 0xff47383f, 0xff403845, 0xff2d2c3a, 0xff1f2734, 0xff202d3e, 0xff13233c, 0xff162743, 0xff1a2b47, 0xff495a76, 0xff97abc6, 0xffb9cde8, 0xffb3c7e2, 0xffb8cce7, 0xffb5c9e4, 0xffb5c9e4, 0xffb6cae5, 0xffb7cbe6, 0xffb6cbe6, 0xffb5cae5, 0xffb4c9e4, 0xffb4c9e4, 0xffb4c8e3, 0xffb4c8e3, 0xffb4c8e3, 0xffb4c8e3, 0xffb3c8e3, 0xffb4c9e4, 0xffb4c9e4, 0xffb4c9e4, 0xffb5c9e4, 0xffb4c8e3, 0xffb3c7e2, 0xffb3c7e2, 0xffb1c5e0, 0xffb1c5e0, 0xffb2c3df, 0xffafc3de, 
    0xff69799d, 0xff607094, 0xff677598, 0xff5d6b8e, 0xff5b698c, 0xff566487, 0xff435072, 0xff334062, 0xff354264, 0xff3c496b, 0xff465171, 0xff485373, 0xff475272, 0xff475272, 0xff4b5475, 0xff4e5778, 0xff4b5377, 0xff4a5277, 0xff4c5478, 0xff525a7e, 0xff586182, 0xff5b6483, 0xff5e6784, 0xff606a85, 0xff626c85, 0xff606b81, 0xff6d788e, 0xff8792a6, 0xff9aa5b7, 0xff9eaaba, 0xff9ea9bb, 0xffa1afbc, 0xffa1b5bc, 0xff9bafb6, 0xff99abb7, 0xffa9b6c6, 0xff9199ac, 0xff44495c, 0xff292b38, 0xff4f4e56, 0xff676161, 0xff6d645d, 0xff827469, 0xff948373, 0xff9d8877, 0xffad9686, 0xffbb9f93, 0xffb99d92, 0xffbda196, 0xffc3a99c, 0xffc0a99b, 0xffbca998, 0xffc2af9e, 0xffbdac9a, 0xffaf9a89, 0xffa18b7d, 0xff9d8277, 0xff836a63, 0xff725a58, 0xff6e5c5c, 0xff5e5359, 0xff3f3c43, 0xff2a2f35, 0xff24303c, 0xff1c2e44, 0xff1b304b, 0xff122742, 0xff3a4f6a, 0xff90a5c0, 0xffb3c8e3, 0xffa9bed9, 0xffafc4df, 0xffb1c6e1, 0xffb2c7e2, 0xffb2c7e2, 0xffb3c8e3, 0xffb3c8e3, 0xffb2c7e2, 0xffb2c7e2, 0xffb1c6e1, 0xffafc4df, 0xffafc4df, 0xffb0c5e0, 0xffb1c6e1, 0xffb2c7e2, 0xffb3c8e3, 0xffb4c9e4, 0xffb5cae5, 0xffb2c7e2, 0xffb2c7e2, 0xffb2c7e2, 0xffb2c7e2, 0xffb2c7e2, 0xffb1c6e1, 0xffb2c6e1, 0xffb1c6e1, 
    0xff8b9bbf, 0xff7f8fb3, 0xff8593b8, 0xff7a88ab, 0xff7684a7, 0xff69779a, 0xff606d90, 0xff455274, 0xff384567, 0xff404d6f, 0xff4c5777, 0xff535e7e, 0xff545f7f, 0xff505b7b, 0xff4d5677, 0xff4b5475, 0xff545c80, 0xff535b80, 0xff575f83, 0xff5c6488, 0xff5f6889, 0xff60698a, 0xff646d8c, 0xff6a7390, 0xff6a748f, 0xff67718a, 0xff737d96, 0xff8f9ab0, 0xffa1acc0, 0xffa1acbe, 0xffa0abbd, 0xffa4b1c1, 0xffa0b4bb, 0xffa0b4bb, 0xff99abb7, 0xffa8b5c5, 0xff8790a1, 0xff373a4b, 0xff2c2c38, 0xff5b565c, 0xff695f5d, 0xff72655c, 0xff8c7b6b, 0xff9f8a77, 0xffa38a76, 0xffaa8f7c, 0xffb39686, 0xffb49487, 0xffb49487, 0xffb49789, 0xffb49a89, 0xffb09987, 0xffaa9580, 0xffa7927d, 0xffa6907b, 0xffa58c78, 0xff9d8173, 0xff94786d, 0xff927672, 0xff907b78, 0xff7b6d6d, 0xff524c4e, 0xff2f3034, 0xff1c262f, 0xff1e3044, 0xff18304a, 0xff132843, 0xff3c516c, 0xff8ea3be, 0xffb0c5e0, 0xffa9bed9, 0xffb0c5e0, 0xffb6cbe6, 0xffb7cce7, 0xffb8cce7, 0xffb9cde8, 0xffb9cde8, 0xffb8cce7, 0xffb8cce7, 0xffb7cbe6, 0xffb7cbe6, 0xffb7cbe6, 0xffb7cbe6, 0xffb7cbe6, 0xffb7cbe6, 0xffb7cbe6, 0xffb9cae6, 0xffb7cbe6, 0xffb8c9e5, 0xffb6cae5, 0xffb6cae5, 0xffb7cbe6, 0xffb7cbe6, 0xffb7cbe6, 0xffb7cbe6, 0xffb6cbe6, 
    0xff8f9ec5, 0xff8393b7, 0xff8e9cc1, 0xff8694b9, 0xff8492b7, 0xff707ea1, 0xff6f7c9f, 0xff4c597c, 0xff3e4b6d, 0xff485577, 0xff556080, 0xff5c6787, 0xff5b6684, 0xff56617f, 0xff535c7b, 0xff525b7c, 0xff575f83, 0xff535b7f, 0xff545c80, 0xff5a6286, 0xff5c6586, 0xff5b6485, 0xff5c6584, 0xff616a87, 0xff6a748f, 0xff656f88, 0xff717b94, 0xff8c97ad, 0xff9ca7bd, 0xff9aa5b9, 0xff98a3b7, 0xff9ca9ba, 0xff9cb0b9, 0xffa4b8c1, 0xff9caebc, 0xffa7b4c5, 0xff82899c, 0xff303143, 0xff312d3b, 0xff645a62, 0xff7e706f, 0xff827167, 0xff9a8574, 0xffb19884, 0xffb49986, 0xffb69785, 0xffbc9b8c, 0xffbb9a8b, 0xffb59387, 0xffb49487, 0xffb89d8c, 0xffb69c8b, 0xffa78e7a, 0xff9e856f, 0xff9c836d, 0xff9a7f6c, 0xff9a7a6d, 0xff987a6f, 0xff9b7e78, 0xff9a8280, 0xff867676, 0xff5f5657, 0xff333335, 0xff151c24, 0xff18293d, 0xff10263e, 0xff142841, 0xff445871, 0xff8ea2bb, 0xffacc0d9, 0xffaebfd9, 0xffb8c9e3, 0xffb5c6e0, 0xffb6c7e1, 0xffb7c7e1, 0xffb8c8e2, 0xffb9c7e2, 0xffb8c6e1, 0xffb8c6e1, 0xffb7c5e0, 0xffb8c8e2, 0xffb8c8e2, 0xffb9c7e2, 0xffb8c6e1, 0xffb8c6e1, 0xffb7c5e0, 0xffb9c4e0, 0xffb7c5e0, 0xffbac5e1, 0xffb8c6e1, 0xffb8c6e1, 0xffb6c6e0, 0xffb6c6e0, 0xffb5c6e0, 0xffb5c6e0, 0xffb3c7e0, 
    0xff8594bb, 0xff7b8ab1, 0xff8a98bf, 0xff8795ba, 0xff8795ba, 0xff717fa4, 0xff7380a3, 0xff4b587b, 0xff404d6f, 0xff495676, 0xff556080, 0xff576280, 0xff545f7d, 0xff525d7b, 0xff565f7e, 0xff5b6483, 0xff666f90, 0xff5d6589, 0xff596185, 0xff5d6687, 0xff616a8b, 0xff616a89, 0xff626b8a, 0xff656e8b, 0xff68728d, 0xff636d88, 0xff707a93, 0xff8e98b1, 0xffa1acc2, 0xffa1acc2, 0xffa1acc0, 0xffa7b4c5, 0xff98abb9, 0xffa7bac8, 0xff9fb0c0, 0xffaab5c9, 0xff82879d, 0xff323045, 0xff383141, 0xff6a5f67, 0xff857575, 0xff7e6a63, 0xff90796b, 0xffb29786, 0xffc4a593, 0xffc9a899, 0xffcba79b, 0xffc6a198, 0xffc4a298, 0xffc4a499, 0xffcfb1a6, 0xffd2b6a8, 0xffbda392, 0xffaa917d, 0xffa08572, 0xff977a6a, 0xff9a7a6d, 0xff9a7970, 0xff997a77, 0xff947a79, 0xff857274, 0xff645a5b, 0xff383639, 0xff171b24, 0xff192639, 0xff0d1e38, 0xff162741, 0xff495a74, 0xff8898b2, 0xffa2b0cb, 0xffa4b2cd, 0xffafbad6, 0xffbcc7e3, 0xffbdc8e4, 0xffbfc8e5, 0xffbfc8e5, 0xffc1c8e5, 0xffc1c8e5, 0xffc0c7e4, 0xffbfc6e3, 0xffbcc5e2, 0xffbcc5e2, 0xffbec5e2, 0xffbec5e2, 0xffc0c5e3, 0xffc0c5e3, 0xffc0c5e3, 0xffc0c5e3, 0xffc2c7e5, 0xffc1c6e4, 0xffc0c7e4, 0xffbdc6e3, 0xffbbc6e2, 0xffb9c7e2, 0xffb8c6e1, 0xffb7c7e1, 
    0xff8594bd, 0xff8392b9, 0xff808eb5, 0xff7e8cb3, 0xff7d8bb0, 0xff7583a8, 0xff626f92, 0xff4f5c7f, 0xff414e70, 0xff414e6e, 0xff545f7f, 0xff616c8a, 0xff5b6684, 0xff56617d, 0xff5a6380, 0xff59627f, 0xff5a6384, 0xff5a6384, 0xff5e6788, 0xff646d8c, 0xff656e8d, 0xff616a89, 0xff606988, 0xff626b88, 0xff636d88, 0xff69738e, 0xff667089, 0xff939db6, 0xff9da8be, 0xffa2adc3, 0xffa1acc2, 0xffa1aec1, 0xff9baebf, 0xff9eb1c2, 0xff9cabc0, 0xffa4aec7, 0xff8589a2, 0xff2c2a40, 0xff463d50, 0xff574956, 0xff4c373c, 0xff523a36, 0xff563b32, 0xff7d5f54, 0xffa5857a, 0xffb8968c, 0xffc7a29c, 0xffbd9794, 0xffbc9693, 0xffc5a29c, 0xffc8a7a0, 0xffc8aca1, 0xffccb2a5, 0xffc0a699, 0xffab8f83, 0xffa18378, 0xffa28178, 0xff9b7a75, 0xff917270, 0xff8f7576, 0xff826f73, 0xff574c52, 0xff312c33, 0xff252733, 0xff192438, 0xff0d1b35, 0xff1f2b45, 0xff45516b, 0xff9da7c2, 0xffaeb8d3, 0xffb1b8d4, 0xffb4b9d6, 0xffb1b6d3, 0xffaeb2cf, 0xffa8a9c7, 0xffa6a7c5, 0xffc1c0df, 0xffcbcae9, 0xffbebbda, 0xffaeadcc, 0xffaaabc9, 0xffb9bad8, 0xffc0bfde, 0xffbdbcdb, 0xffbebbda, 0xffbbb6d6, 0xffb8b3d3, 0xffbdb8d8, 0xffc4bfdf, 0xffc2bfde, 0xffb3b2d1, 0xffbcbddb, 0xffc1c5e2, 0xffbdc2df, 0xffc2c9e5, 0xffb6c0db, 
    0xff8493bc, 0xff8291ba, 0xff7f8cb6, 0xff7c8ab1, 0xff7a88af, 0xff7280a5, 0xff5e6b8e, 0xff4a5779, 0xff404d6f, 0xff435070, 0xff586381, 0xff67728e, 0xff626d89, 0xff5c6882, 0xff5d6782, 0xff5a647f, 0xff586180, 0xff586180, 0xff5b6483, 0xff616a87, 0xff666f8c, 0xff68718e, 0xff68718e, 0xff69738e, 0xff6a748f, 0xff6f7992, 0xff6b758e, 0xff96a1b7, 0xff9da8be, 0xffa0abc1, 0xff9da8be, 0xff9da9bf, 0xffa1b3c7, 0xffa4b6cc, 0xffa8b6d0, 0xffb5bcd8, 0xff8f90ae, 0xff2f2b44, 0xff45394f, 0xff5c4b5b, 0xff5a454c, 0xff614748, 0xff563933, 0xff5a3c34, 0xff694841, 0xff85625e, 0xffaf8c8a, 0xffb99595, 0xffa68282, 0xffa17d7d, 0xff967774, 0xff8d706a, 0xff886d66, 0xff8d7269, 0xff9b7e76, 0xffa98b83, 0xffa98883, 0xffa17f7e, 0xff937376, 0xff886d72, 0xff735f68, 0xff473c44, 0xff24212a, 0xff20222f, 0xff1b2338, 0xff1a243f, 0xff202a45, 0xff545b77, 0xffa3aac6, 0xffb9bedb, 0xffbfc3e0, 0xffbcbddb, 0xff81809f, 0xff615e7d, 0xff625d7d, 0xff8883a3, 0xffa8a1c2, 0xffa49dbe, 0xff786e90, 0xff7d7395, 0xff8780a1, 0xff908bab, 0xffa099ba, 0xff998fb1, 0xff807597, 0xff7b7092, 0xff7e7395, 0xff73688a, 0xffa69bbd, 0xff9086a8, 0xff797293, 0xff847f9f, 0xff8c8baa, 0xff9e9fbd, 0xffc0c4e1, 0xffc2c7e4, 
    0xff8897c2, 0xff8594bd, 0xff828fb9, 0xff7e8cb3, 0xff7a88af, 0xff707ea3, 0xff5b688b, 0xff465375, 0xff3d4a6c, 0xff3f4c6c, 0xff525d7b, 0xff606b87, 0xff5c6783, 0xff59657f, 0xff5c6681, 0xff58627d, 0xff5e6883, 0xff5d6683, 0xff5b6481, 0xff5b6580, 0xff5f6984, 0xff636d88, 0xff636d88, 0xff5f6982, 0xff667089, 0xff6c778d, 0xff687389, 0xff96a1b7, 0xff9faac0, 0xffa4afc3, 0xffa2adc1, 0xffa3afc5, 0xffa6b6cd, 0xffaabad3, 0xffaeb9d5, 0xffb6bdda, 0xff8b8aa9, 0xff352f4b, 0xff493b54, 0xff665464, 0xff816a72, 0xff917778, 0xff8c706d, 0xff7c5d58, 0xff634441, 0xff6c4a49, 0xff977478, 0xffa48187, 0xff8e6b6f, 0xff735054, 0xff5f4141, 0xff583c39, 0xff513834, 0xff59403b, 0xff6c514a, 0xff755854, 0xff9d7b7a, 0xff99797a, 0xff8e6f74, 0xff7e656b, 0xff63525c, 0xff3a303b, 0xff201e2b, 0xff242736, 0xff1e243c, 0xff272e4a, 0xff262d49, 0xff6d728f, 0xffacb0cd, 0xffc2c3e1, 0xffc8c7e6, 0xffc2bfde, 0xffa6a1c1, 0xff6c6586, 0xff655b7d, 0xff9388aa, 0xffb4a6c9, 0xffc7b9dc, 0xff87779b, 0xff9a8caf, 0xff9e93b5, 0xffa196b8, 0xffc0b2d5, 0xffb1a3c6, 0xff7b6b8f, 0xff7e6d91, 0xff9281a5, 0xff77668a, 0xffa695b9, 0xff7b6b8f, 0xff625477, 0xff6e6486, 0xff716c8c, 0xff8e8baa, 0xffc0c1df, 0xffc4c8e5, 
    0xff8d9cc7, 0xff8b9ac5, 0xff8794be, 0xff828fb9, 0xff7e8cb3, 0xff7280a5, 0xff5b678d, 0xff465375, 0xff3e4b6d, 0xff3e4c69, 0xff4d5876, 0xff56617d, 0xff535f79, 0xff54607a, 0xff5a647d, 0xff566079, 0xff5e6881, 0xff5d6780, 0xff59637c, 0xff57617a, 0xff5b657e, 0xff5f6a80, 0xff5d6780, 0xff586379, 0xff646f85, 0xff6a7589, 0xff667187, 0xff949fb3, 0xff9ea9bd, 0xffa3aec2, 0xffa2adc1, 0xffa2afc2, 0xffa2b3c7, 0xffa8b8cf, 0xffa5b0cc, 0xffa1a6c3, 0xff797695, 0xff433a55, 0xff54465d, 0xff6d5b6b, 0xff614a52, 0xff765c5d, 0xff886c69, 0xff8d706c, 0xff705250, 0xff6a4c4c, 0xff917277, 0xff9f8086, 0xff8f6e75, 0xff6a4a4f, 0xff5d4042, 0xff705655, 0xff7f6765, 0xff846c68, 0xff785f5a, 0xff644844, 0xff785859, 0xff816365, 0xff82656a, 0xff765f67, 0xff5c4c57, 0xff342c37, 0xff1f1f2b, 0xff242a3a, 0xff1e263d, 0xff29334e, 0xff2e3551, 0xff8c91ae, 0xffb6bad7, 0xffbebfdd, 0xffc2c1e0, 0xffc2bfde, 0xffa29dbd, 0xff766f90, 0xff6c6183, 0xff807295, 0xff9789ac, 0xffd0c0e4, 0xff857498, 0xff8b7b9f, 0xff8e80a3, 0xff8c7ea1, 0xffb7a9cc, 0xffab9bbf, 0xff68577b, 0xff79658a, 0xffa08ab0, 0xff7c668c, 0xffae9abf, 0xff7c6b8f, 0xff6b5b7f, 0xff7e7395, 0xff7a7394, 0xff918ead, 0xffc1c2e0, 0xffbbbfdc, 
    0xff8f9ec9, 0xff8d9cc7, 0xff8996c2, 0xff8491bb, 0xff808eb5, 0xff7381a6, 0xff5c688e, 0xff465375, 0xff404d6f, 0xff43516e, 0xff545f7d, 0xff5d6884, 0xff59657f, 0xff58647e, 0xff58627b, 0xff4e5871, 0xff505b71, 0xff4c576d, 0xff4b566c, 0xff4f5a70, 0xff556076, 0xff5a6579, 0xff5c677d, 0xff5d687c, 0xff697488, 0xff6e798b, 0xff687387, 0xff939eb0, 0xff99a4b6, 0xff9ba6b8, 0xff97a2b4, 0xff97a4b5, 0xff99aabc, 0xffa6b7c9, 0xffa1abc4, 0xff9197af, 0xff6e6b86, 0xff585067, 0xff605365, 0xff6c5a66, 0xff553e44, 0xff523a38, 0xff604742, 0xff785c58, 0xff674b48, 0xff674a4c, 0xff957a7f, 0xffab8e93, 0xff86676d, 0xff6a4b50, 0xff604647, 0xff7e6664, 0xff9f8a87, 0xffa9948f, 0xff95817a, 0xff7c655f, 0xff674b4a, 0xff795c5e, 0xff82686b, 0xff796469, 0xff5c4f58, 0xff322c36, 0xff171925, 0xff1a222f, 0xff162336, 0xff202f46, 0xff3e4a62, 0xffa3adc6, 0xffbdc4de, 0xffb7bdd7, 0xffb8b9d5, 0xffc2c2de, 0xff8b86a4, 0xff7b7493, 0xff817897, 0xff897e9e, 0xff9587a8, 0xffc9badb, 0xff746385, 0xff6c5d7e, 0xff756a8a, 0xff786d8d, 0xffad9fc0, 0xffaa9bbc, 0xff6d5c7e, 0xff7f6b8e, 0xffa48eb2, 0xff7d678b, 0xffa38fb2, 0xff766587, 0xff695b7c, 0xff7d7493, 0xff797492, 0xff8f8fab, 0xffbec2dd, 0xffb7bdd7, 
    0xff8e9dc8, 0xff8c9bc6, 0xff8996c2, 0xff8592bc, 0xff808db7, 0xff7482a7, 0xff5c688e, 0xff465375, 0xff394668, 0xff414f6c, 0xff586381, 0xff646f8b, 0xff5e6a84, 0xff57637b, 0xff4c566f, 0xff384359, 0xff404b5f, 0xff354052, 0xff333e52, 0xff3b4658, 0xff3e495d, 0xff3d485a, 0xff455064, 0xff535e70, 0xff626d7f, 0xff677284, 0xff636e80, 0xff8f9aac, 0xff97a2b4, 0xff9ba6b8, 0xff99a4b6, 0xff99a6b6, 0xff94a6b4, 0xffa5b7c5, 0xffa4afc1, 0xff989db1, 0xff77758a, 0xff70677a, 0xff6c5e6d, 0xff73626a, 0xff776163, 0xff634b47, 0xff684f48, 0xff7f665f, 0xff725955, 0xff786060, 0xffa78e92, 0xffaf969a, 0xff7f6267, 0xff73565a, 0xff584040, 0xff543f3c, 0xff6b5852, 0xff76635c, 0xff715e57, 0xff746059, 0xff7a615d, 0xff8b7170, 0xff8e787a, 0xff807073, 0xff62575d, 0xff333238, 0xff151922, 0xff101d26, 0xff0e1f31, 0xff1e3046, 0xff5f6f86, 0xffaebdd4, 0xffbdc9e1, 0xffb8bfd9, 0xffb5bbd5, 0xffc7c8e4, 0xff8e8eaa, 0xff85809e, 0xff8f88a7, 0xff9e95b4, 0xffa196b6, 0xffb5a7c8, 0xff736485, 0xff77698a, 0xff706786, 0xff7b7291, 0xffa79cbc, 0xffac9ebf, 0xff847395, 0xff8c789b, 0xff9c88ab, 0xff7b678a, 0xffac98bb, 0xff897a9b, 0xff746989, 0xff7b7493, 0xff787894, 0xff8f93ae, 0xffbec4de, 0xffbbc2dc, 
    0xff8e9cc9, 0xff8c9ac7, 0xff8a97c3, 0xff8794be, 0xff8390ba, 0xff7785aa, 0xff606c92, 0xff4a5779, 0xff354264, 0xff3d4b68, 0xff525d7b, 0xff5b6682, 0xff535f79, 0xff4b576f, 0xff3d4760, 0xff263147, 0xff343f51, 0xff202c3c, 0xff1a2537, 0xff232f3f, 0xff232e40, 0xff1c2838, 0xff2d384a, 0xff4a5666, 0xff566272, 0xff5d6979, 0xff5c6878, 0xff8c98a8, 0xff98a4b4, 0xffa0acbc, 0xffa0acbc, 0xffa2b0bd, 0xff99aab4, 0xffa3b4be, 0xffa6b2c0, 0xffa6acbc, 0xff848393, 0xff7e7685, 0xff796c76, 0xff927f83, 0xff816e6a, 0xff77635a, 0xff826c61, 0xff927c71, 0xff816a64, 0xff8d7875, 0xffb39fa0, 0xffa38d90, 0xff997f82, 0xff9e8485, 0xff715c59, 0xff4a3932, 0xff58473f, 0xff5e4d43, 0xff5c4b41, 0xff736157, 0xff866f67, 0xff8f7773, 0xff887473, 0xff746666, 0xff595053, 0xff323335, 0xff171f22, 0xff14232a, 0xff102332, 0xff2b4156, 0xff8ea3b8, 0xffafc1d7, 0xffb4c4db, 0xffbbc7df, 0xffb7c1da, 0xffc3c9e3, 0xffbabed9, 0xffb0b0cc, 0xffb3b0cd, 0xffbab5d3, 0xffc1b8d7, 0xffc1b6d6, 0xffa69bbb, 0xffb6abcb, 0xff9f98b7, 0xffa9a4c2, 0xffbeb7d6, 0xffc1b8d7, 0xffb5a7c8, 0xffb6a7c8, 0xffb7a6c8, 0xffa796b8, 0xffbfb0d1, 0xffb5a7c8, 0xff9d96b5, 0xff9996b3, 0xff9498b3, 0xffa2a9c3, 0xffbfc9e2, 0xffb9c5dd, 
    0xff909fca, 0xff8f9ec9, 0xff8c9bc4, 0xff8b98c2, 0xff8794be, 0xff7c8ab1, 0xff647096, 0xff4e5a80, 0xff3b486b, 0xff3f4c6e, 0xff4d5878, 0xff4f5a78, 0xff46516d, 0xff424c67, 0xff37415c, 0xff232d46, 0xff2b364c, 0xff111c30, 0xff0c1429, 0xff182337, 0xff192438, 0xff131e30, 0xff2d384a, 0xff566173, 0xff586474, 0xff5f6b7b, 0xff5d6979, 0xff8c98a8, 0xff98a4b4, 0xff9eaab8, 0xff9fabb9, 0xffa0aeb9, 0xff9eb0ba, 0xff9eafb9, 0xffa2aeba, 0xffacb4bf, 0xff898995, 0xff817b85, 0xff84777e, 0xffb4a2a2, 0xffa7948e, 0xffa69289, 0xffab958a, 0xffa28c81, 0xff7e6a63, 0xff8a7572, 0xffa69293, 0xff826e6f, 0xffa38e8d, 0xffb8a39e, 0xff8b7871, 0xff625149, 0xff79685e, 0xff7e6e61, 0xff746256, 0xff8c786d, 0xff7b645c, 0xff7e6761, 0xff705d59, 0xff5a4a4a, 0xff463c3d, 0xff2d282c, 0xff1d1e22, 0xff1d262d, 0xff1a2a39, 0xff405364, 0xffb6c9da, 0xffaec0d4, 0xffacbdd1, 0xffbfcee5, 0xffbac6de, 0xffbbc5de, 0xffbac1dd, 0xffbdc2df, 0xffbfc0de, 0xffbcbbda, 0xffc7c2e0, 0xffc5bedd, 0xffc0b9d8, 0xffc5c0de, 0xffbabbd7, 0xffbfc3de, 0xffbfc0dc, 0xffbcbcd8, 0xffc2bfdc, 0xffc4bfdd, 0xffc3bcdb, 0xffc5bedd, 0xffbdb6d5, 0xffc7c4e3, 0xffbbbcda, 0xffb7bbd8, 0xffb6bdd9, 0xffb8c4de, 0xffc0cee8, 0xffb3c1db, 
    0xff8fa1c5, 0xff8a9cc0, 0xff8b9dc3, 0xff8c9bc2, 0xff8190b9, 0xff7786af, 0xff66739d, 0xff4d5983, 0xff323e66, 0xff4d567d, 0xff555d84, 0xff4f577c, 0xff535b7f, 0xff555b7d, 0xff474d6f, 0xff3c4262, 0xff202744, 0xff222946, 0xff1a1f3c, 0xff171e3a, 0xff262d49, 0xff343b55, 0xff424a61, 0xff545c71, 0xff6b7688, 0xff626e7e, 0xff5c6979, 0xff8f9daa, 0xff97a5b2, 0xff9fadb8, 0xffa1afba, 0xff9cadb7, 0xff9baebc, 0xff9fb1bf, 0xffa2b0bd, 0xffa5adb8, 0xffa4a4ac, 0xff969197, 0xff9a8e90, 0xffb3a4a1, 0xffb19e98, 0xffbaa69f, 0xffbea79f, 0xff9e8781, 0xff836e69, 0xffa08b88, 0xffa49091, 0xff705c5b, 0xff8f7c76, 0xffb09e94, 0xffb8a69a, 0xff968478, 0xff7d695e, 0xff877368, 0xff9c867b, 0xffa18b80, 0xff785f58, 0xff644c48, 0xff5c4442, 0xff665253, 0xff523f43, 0xff26171e, 0xff251821, 0xff352f39, 0xff3c404c, 0xff38424e, 0xffb0bcca, 0xffb9c5d5, 0xffb8c5d8, 0xffb5c1d7, 0xffc2cee6, 0xffafbbd5, 0xff808ba7, 0xff939cb9, 0xff9097b4, 0xff898eac, 0xff969ab7, 0xff999ab8, 0xff8f8fab, 0xff8e92ad, 0xff8f9bb1, 0xffabbcd0, 0xffb6c7db, 0xffc2d1e6, 0xff919db3, 0xff8d99af, 0xff939fb7, 0xff8c96af, 0xff909ab5, 0xff8a95b1, 0xff8994b0, 0xff8492ad, 0xff8b99b6, 0xffa4b3d0, 0xffb6c5e2, 0xffb0bfdc, 
    0xff91a4c5, 0xff8b9ebf, 0xff8d9fc3, 0xff8c9ec4, 0xff8392bb, 0xff7a89b2, 0xff6976a2, 0xff4f5c86, 0xff3b446f, 0xff4b547d, 0xff4d547e, 0xff495178, 0xff585d83, 0xff60668a, 0xff555b7d, 0xff494f71, 0xff323757, 0xff303555, 0xff272c4c, 0xff2b304e, 0xff414865, 0xff535a76, 0xff5d647e, 0xff697188, 0xff697486, 0xff657181, 0xff62707d, 0xff92a0ad, 0xff96a4af, 0xff9ba9b4, 0xff9aa8b1, 0xff91a2ac, 0xff98abba, 0xff9cadbd, 0xffa0aebb, 0xffa5aeb7, 0xffa3a3ab, 0xff908b8f, 0xff8c8281, 0xff9f908b, 0xffa18e88, 0xff907c75, 0xff87706a, 0xff89716d, 0xffa28a88, 0xffc4afac, 0xffb5a09f, 0xff7a6763, 0xff9e8b84, 0xffb6a699, 0xffccbaae, 0xffcfbcae, 0xffccb6ab, 0xffc8b2a7, 0xffbfa79d, 0xffb39a93, 0xff967d78, 0xff826965, 0xff786060, 0xff745e60, 0xff554045, 0xff352228, 0xff3b2730, 0xff433640, 0xff4d4d57, 0xff4d535f, 0xffb6becb, 0xffb8c1d0, 0xffbcc7d9, 0xffbac6dc, 0xffc6d5ec, 0xffbecce6, 0xff8e9cb9, 0xff929dbb, 0xff929bba, 0xff969cbc, 0xff9ca1bf, 0xff9599b6, 0xff8f90ae, 0xff9096b0, 0xff8192a6, 0xffa3b8c9, 0xffabc0d3, 0xffb3c8db, 0xff8b9db1, 0xff8294a8, 0xff8496ac, 0xff8092a8, 0xff8292ab, 0xff7c8ca6, 0xff7a8aa4, 0xff7785a2, 0xff7e8ca9, 0xff9aa8c5, 0xffb1bfdc, 0xffb0bedb, 
    0xff93a5c9, 0xff8ea0c4, 0xff8fa1c7, 0xff8ea0c6, 0xff8695be, 0xff7d8cb5, 0xff6d7aa4, 0xff54618b, 0xff3f4873, 0xff49527b, 0xff464e73, 0xff464e72, 0xff5c6284, 0xff676d8d, 0xff5b627f, 0xff4d5471, 0xff333856, 0xff2e3350, 0xff232845, 0xff2a304a, 0xff444b65, 0xff555d74, 0xff596178, 0xff5e667b, 0xff626d7f, 0xff626e7e, 0xff606e7b, 0xff8b99a6, 0xff8d9ba6, 0xff96a4af, 0xff98a6b1, 0xff90a0ad, 0xff91a2b2, 0xff95a5b5, 0xff9aa6b4, 0xff9fa8b1, 0xff9c9ca4, 0xff817c80, 0xff706665, 0xff796a67, 0xff6a5552, 0xff68504c, 0xff6b524e, 0xff7f6763, 0xffa68e8c, 0xffc2adaa, 0xffbda8a7, 0xffa4918d, 0xff938079, 0xff96847a, 0xffa49087, 0xffb9a59a, 0xffcbb4ac, 0xffcab1aa, 0xffb89f98, 0xffa78e87, 0xff9d847f, 0xff89706c, 0xff826a6a, 0xff6f595c, 0xff4d383d, 0xff453238, 0xff513d46, 0xff4d414b, 0xff4c4f58, 0xff5d6771, 0xffb6c0cc, 0xffb5c1d1, 0xffc0cbdd, 0xffb8c4da, 0xffb8c7de, 0xffb7c5df, 0xffb2bddb, 0xffa7b2d0, 0xffacb5d4, 0xffb8bede, 0xffb3b6d5, 0xffadaecc, 0xffb0b1cf, 0xffb3b7d2, 0xffa1adc3, 0xffbdcee2, 0xffb8c7dc, 0xffbdcce1, 0xffb0bfd4, 0xffabbacf, 0xffaab6ce, 0xffa8b4cc, 0xffaab4cd, 0xffaab4cf, 0xffaeb8d3, 0xffaeb5d2, 0xffaab1ce, 0xffb4bbd8, 0xffb9c0dd, 0xffafb6d3, 
    0xff95a7cb, 0xff8fa1c5, 0xff90a2c8, 0xff8fa1c7, 0xff8796bf, 0xff7e8db6, 0xff6f7ca6, 0xff56648b, 0xff414a73, 0xff475175, 0xff41496d, 0xff3f4867, 0xff525976, 0xff585f7b, 0xff49506a, 0xff3c435d, 0xff323852, 0xff30364e, 0xff292f47, 0xff31374f, 0xff495166, 0xff575f74, 0xff586073, 0xff5c6477, 0xff606c7c, 0xff616d7d, 0xff5e6c79, 0xff8795a2, 0xff8896a1, 0xff93a1ac, 0xff9aa8b3, 0xff97a5b2, 0xff91a1b1, 0xff94a4b4, 0xff99a5b3, 0xffa0a6b2, 0xff9a99a1, 0xff777175, 0xff594d4f, 0xff584946, 0xff46312e, 0xff5d4541, 0xff684f4b, 0xff69504c, 0xff765c5b, 0xff836b69, 0xff8c7474, 0xff937e7b, 0xff8a7570, 0xff86726b, 0xff88746d, 0xff9b847c, 0xffb59c95, 0xffc5aca5, 0xffc0a7a0, 0xffb39a95, 0xff8e7571, 0xff79615f, 0xff766160, 0xff614d4f, 0xff4b383c, 0xff5c4b51, 0xff6b5a62, 0xff60565f, 0xff696d78, 0xff838d99, 0xffbfcbd9, 0xffbac6d6, 0xffc8d3e7, 0xffbdc9df, 0xffb9c5dd, 0xffbfcbe5, 0xffc7d0ed, 0xffbcc5e4, 0xffc6cbeb, 0xffc6c9ea, 0xffb3b4d3, 0xffb5b4d4, 0xffcac7e6, 0xffcbcae9, 0xffb9c0da, 0xffcbd6ec, 0xffb7c2d8, 0xffb9c1d8, 0xffc5cde4, 0xffc5cde4, 0xffc3c9e3, 0xffbfc5df, 0xffc0c4df, 0xffbcc0db, 0xffbdbeda, 0xffb9bad6, 0xffb4b4d0, 0xffbfbfdb, 0xffcacae6, 0xffc6c6e2, 
    0xff96a8ce, 0xff8fa1c7, 0xff90a2c8, 0xff8ea0c6, 0xff8695bc, 0xff7e8db4, 0xff6e7ca3, 0xff566489, 0xff444e72, 0xff454f72, 0xff363f5e, 0xff29334e, 0xff303751, 0xff313950, 0xff272f44, 0xff222a3d, 0xff1f2639, 0xff232a3c, 0xff22293b, 0xff282f41, 0xff3b4455, 0xff454e5f, 0xff485162, 0xff50596a, 0xff4d5967, 0xff505c6a, 0xff556370, 0xff8a98a5, 0xff8f9daa, 0xff95a3b0, 0xff99a7b4, 0xff97a4b4, 0xff9ca9ba, 0xff9facbd, 0xffa3acbb, 0xffa8aeba, 0xff9f9ea6, 0xff777175, 0xff534749, 0xff4f3d3d, 0xff55403f, 0xff674f4d, 0xff604743, 0xff4c302d, 0xff492d2c, 0xff4f3534, 0xff53393a, 0xff533b3b, 0xff59413f, 0xff654d49, 0xff6d5551, 0xff735a55, 0xff856c67, 0xff9d817d, 0xffa08480, 0xff937a76, 0xff886f6b, 0xff715957, 0xff705b5a, 0xff5b4749, 0xff554548, 0xff736469, 0xff796a71, 0xff716a72, 0xff8b8f9b, 0xffa2abba, 0xffbfc8d7, 0xffb1bdcd, 0xffbdc8dc, 0xffbbc6dc, 0xffbbc5de, 0xffc7ceea, 0xffbfc4e2, 0xffbfc4e2, 0xffc1c2e1, 0xffa8a7c7, 0xff8884a5, 0xff928cae, 0xffaaa3c4, 0xffa8a3c3, 0xffacadc9, 0xffc3c7e2, 0xffb9bdd8, 0xffb6b7d3, 0xffc3c4e0, 0xffbebeda, 0xffc4c1dc, 0xffc3c0db, 0xffc8c4df, 0xffc5c1dc, 0xffcbc5e1, 0xffcbc2df, 0xffc0b5d3, 0xffbcb1cf, 0xffb6abc9, 0xffa89dbb, 
    0xff98aad0, 0xff91a3c9, 0xff91a3c9, 0xff8ea0c6, 0xff8594bb, 0xff7c8bb2, 0xff6d7ba0, 0xff556386, 0xff475174, 0xff414c6a, 0xff26304b, 0xff0d182e, 0xff0c1429, 0xff0c1526, 0xff0c1526, 0xff111a29, 0xff070f1c, 0xff0e1623, 0xff0c1421, 0xff0c1421, 0xff131d29, 0xff141e2a, 0xff151f2b, 0xff1d2635, 0xff172331, 0xff1e2a38, 0xff354350, 0xff84929f, 0xff97a5b2, 0xff9aa8b5, 0xff9aa8b5, 0xff9aa7b7, 0xff9eabbc, 0xffa3aec0, 0xffa7aebe, 0xffacb0bc, 0xffa09fa7, 0xff777077, 0xff54484c, 0xff513e40, 0xff5d4847, 0xff68504e, 0xff674d4c, 0xff573b38, 0xff4e3231, 0xff5b3f3e, 0xff674a4c, 0xff654b4c, 0xff6b5150, 0xff7b6160, 0xff7e6463, 0xff6e5551, 0xff6d514e, 0xff7c605d, 0xff866a67, 0xff816766, 0xff826a68, 0xff695453, 0xff665253, 0xff504041, 0xff5a4b50, 0xff73666d, 0xff6b6068, 0xff76707a, 0xff979aa9, 0xffafb6c8, 0xffc1c8da, 0xffbcc3d6, 0xffc2c8de, 0xffbfc5dd, 0xffb8bed8, 0xffb3b7d4, 0xffb1b2d1, 0xffbab9d9, 0xffb2aecf, 0xff8c85a7, 0xff6d6386, 0xff75698d, 0xff85779a, 0xff7f7496, 0xff8984a4, 0xffaaaac6, 0xffbebbd8, 0xffbbb8d5, 0xffbfbad8, 0xffb2abca, 0xffc7bedb, 0xffc9bedc, 0xffb4a9c7, 0xffafa2be, 0xffb1a2bf, 0xffaf9fbc, 0xff9e8eab, 0xff907d99, 0xff7f6c88, 0xff6a5773, 
    0xff9badd5, 0xff93a5cd, 0xff92a4cc, 0xff8ea0c8, 0xff8594bb, 0xff7c8bb2, 0xff6d7ba0, 0xff556386, 0xff444f6f, 0xff3a4561, 0xff1e2841, 0xff040f23, 0xff030c1d, 0xff030d19, 0xff030d19, 0xff0a141e, 0xff09121b, 0xff0e1720, 0xff09121b, 0xff040d16, 0xff07111b, 0xff030d17, 0xff000a14, 0xff06101c, 0xff000511, 0xff000713, 0xff1a2835, 0xff7b8996, 0xff98a6b3, 0xff99a7b4, 0xff99a6b6, 0xff9facbc, 0xff9ca7b9, 0xff9fabbb, 0xffa4abbb, 0xffa9adb9, 0xff9e9ca7, 0xff787179, 0xff5a4d54, 0xff5a4a4d, 0xff503c3d, 0xff543c3c, 0xff674d4c, 0xff6e5251, 0xff604242, 0xff694b4b, 0xff836567, 0xff8a6d6f, 0xff86696b, 0xff896c6e, 0xff816466, 0xff6c504f, 0xff5b3f3e, 0xff5e4241, 0xff694f4e, 0xff745a59, 0xff775f5f, 0xff624e4f, 0xff5e4b4d, 0xff4b3c3f, 0xff61545b, 0xff71676f, 0xff625b63, 0xff8d8a95, 0xffb1b2c4, 0xffc1c6da, 0xffc6cbdf, 0xffc6c8df, 0xffc1c3dc, 0xffc0c0da, 0xffb9b6d3, 0xffa5a0be, 0xffa39cbd, 0xffafa5c7, 0xffa599bd, 0xff9080a5, 0xff87759b, 0xff89749b, 0xff88739a, 0xff87769a, 0xff7e7496, 0xff9089aa, 0xffa59ebf, 0xffa39cbd, 0xffa89fbe, 0xff9c91b1, 0xffb5a8c6, 0xffa697b6, 0xff736380, 0xff63506e, 0xff5c4766, 0xff5d4663, 0xff5c4562, 0xff644b68, 0xff684f6c, 0xff604764, 
    0xff9dafd7, 0xff96a8d0, 0xff94a6ce, 0xff90a2ca, 0xff8695bc, 0xff7c8cb0, 0xff6d7ba0, 0xff556485, 0xff3d4868, 0xff35415b, 0xff1d283e, 0xff0b1628, 0xff0d1625, 0xff0c1620, 0xff061019, 0xff081319, 0xff050e15, 0xff091219, 0xff050e15, 0xff030c13, 0xff0a141d, 0xff0a141d, 0xff07111b, 0xff0d1721, 0xff0c1824, 0xff030f1b, 0xff1a2835, 0xff7b8996, 0xff97a5b2, 0xff93a1ae, 0xff94a1b1, 0xffa0adbd, 0xff9ca7b9, 0xffa2abbc, 0xffa6adbd, 0xffaaaeba, 0xffa19faa, 0xff7e777f, 0xff64595f, 0xff68575d, 0xff513d3f, 0xff3a2426, 0xff4e3435, 0xff674b4a, 0xff5a3c3c, 0xff583a3a, 0xff67494b, 0xff654749, 0xff7f6163, 0xff7b5e60, 0xff785b5d, 0xff735658, 0xff664a49, 0xff5b3f3e, 0xff614746, 0xff705858, 0xff705a5c, 0xff614e50, 0xff5f5053, 0xff51464c, 0xff71676f, 0xff7d787f, 0xff6f6c77, 0xffb5b3c1, 0xffc4c5da, 0xffc2c2da, 0xffaeaec8, 0xff9f9fb9, 0xff9490ab, 0xff9f99b5, 0xffa89fbe, 0xff978cac, 0xff9183a6, 0xff9c8cb0, 0xff9785ab, 0xff9984ab, 0xffaa94bb, 0xffa990b8, 0xffa085ae, 0xffa690b7, 0xffa496ba, 0xff958bad, 0xff968cae, 0xff9085a7, 0xff998bac, 0xff9082a3, 0xff9f90b1, 0xff786787, 0xff513e5c, 0xff533e5d, 0xff6a5370, 0xff846b89, 0xff8b728f, 0xff856b88, 0xff715772, 0xff583e59, 
    0xff9dafd7, 0xffa0b2da, 0xff9caed6, 0xff91a3cb, 0xff8796bd, 0xff7c8cb0, 0xff6a789d, 0xff596889, 0xff404b69, 0xff404c66, 0xff263147, 0xff071323, 0xff06101c, 0xff0d1721, 0xff0b161c, 0xff071216, 0xff0e171c, 0xff0a1318, 0xff060f14, 0xff040d12, 0xff051016, 0xff071218, 0xff07111a, 0xff050f18, 0xff08151e, 0xff020e1a, 0xff212f3c, 0xff82909d, 0xff97a4b4, 0xff98a5b5, 0xff99a6b6, 0xff98a5b5, 0xff9faabc, 0xff9fa8b9, 0xffadb4c4, 0xffa8abba, 0xffa3a1ae, 0xff857f89, 0xff5e545c, 0xff796a71, 0xff675458, 0xff644e50, 0xff795f60, 0xffa78a8c, 0xffc6a8a8, 0xffbc9e9e, 0xff977778, 0xff785a5a, 0xff644648, 0xff4e3032, 0xff492b2d, 0xff604244, 0xff6e5153, 0xff674d4e, 0xff614949, 0xff654f51, 0xff645153, 0xff645457, 0xff4a3f45, 0xff6b646b, 0xff838089, 0xff94929d, 0xffc2c4d1, 0xffb6b7c9, 0xffc7c5dd, 0xffb9b6d1, 0xff9691af, 0xff8e89a7, 0xff7a7190, 0xff867b9b, 0xff7f7192, 0xff867798, 0xff89789c, 0xff89759a, 0xff877197, 0xff856c93, 0xff836891, 0xff836891, 0xff866995, 0xff866d95, 0xff9688ac, 0xffb2a8cb, 0xffbaafd1, 0xff9e93b5, 0xff8c7ea1, 0xff9688a9, 0xffa293b4, 0xff9f8eae, 0xffa592b0, 0xff9a85a4, 0xff86728e, 0xff735c79, 0xff6b526f, 0xff735a77, 0xff876e8b, 0xff987f9c, 
    0xff9caed6, 0xff9badd5, 0xff98aad2, 0xff92a4cc, 0xff8b9ac1, 0xff7d8db1, 0xff6b799e, 0xff5b6a8b, 0xff444f6d, 0xff434f69, 0xff283347, 0xff091525, 0xff07111d, 0xff0e1821, 0xff0b161c, 0xff081317, 0xff0b1419, 0xff081116, 0xff040d12, 0xff040d12, 0xff051016, 0xff071218, 0xff07111a, 0xff061019, 0xff09161f, 0xff020e1a, 0xff202e3b, 0xff808e9b, 0xff95a2b2, 0xff97a4b4, 0xff99a6b6, 0xff99a6b6, 0xff9da8ba, 0xff9da6b7, 0xffa8afbf, 0xffa7aab9, 0xffa9a9b5, 0xff918e97, 0xff665f67, 0xff70636a, 0xff5f4f52, 0xff6b5759, 0xff82696c, 0xff977d7e, 0xff9e8183, 0xff9b7d7d, 0xffa28283, 0xffae9090, 0xff846666, 0xff785a5a, 0xff755759, 0xff7c5f61, 0xff7a5d5f, 0xff694f50, 0xff5d4749, 0xff5d4a4c, 0xff5c4c4f, 0xff615559, 0xff655e65, 0xff95929b, 0xffaeaeba, 0xffaeb1c0, 0xffc3cada, 0xffbdc2d6, 0xffc3c3dd, 0xffb3aece, 0xff8883a3, 0xff766f8e, 0xff5d5473, 0xff655a7a, 0xff615273, 0xff685779, 0xff614d72, 0xff644e74, 0xff664d74, 0xff624970, 0xff60456e, 0xff634871, 0xff6d527d, 0xff725b85, 0xffac9ec2, 0xffb3acce, 0xffa49abc, 0xff6f6587, 0xff453a5c, 0xff423759, 0xff544667, 0xff5d4f70, 0xff4a3b5a, 0xff544562, 0xff655572, 0xff796684, 0xff8d7a96, 0xffa591ad, 0xffbda9c5, 0xffccb8d4, 
    0xff9caed6, 0xff97a9d1, 0xff94a6ce, 0xff94a6ce, 0xff8f9ec5, 0xff7e8eb2, 0xff6c7a9f, 0xff606f90, 0xff4e5977, 0xff49556f, 0xff2b364c, 0xff0a1527, 0xff081120, 0xff0d1721, 0xff0b151e, 0xff0a151b, 0xff0a131a, 0xff081118, 0xff060f16, 0xff060f16, 0xff07111a, 0xff09131c, 0xff09131d, 0xff09131d, 0xff0a1622, 0xff05111d, 0xff23313e, 0xff808e9b, 0xff94a1b1, 0xff96a3b3, 0xff99a6b6, 0xff98a5b5, 0xff9ca8b8, 0xff9ea7b8, 0xffa4abbb, 0xffa3a9b7, 0xffacaebb, 0xff9f9da8, 0xff726c76, 0xff655a62, 0xff635459, 0xff675458, 0xff735d5f, 0xff836b6b, 0xff8e7173, 0xff907473, 0xff977979, 0xffa08280, 0xff886a68, 0xff8c706d, 0xff927675, 0xff907473, 0xff7f6564, 0xff6c5454, 0xff624e4f, 0xff605051, 0xff4f4347, 0xff60565e, 0xff84818a, 0xffb2b4c0, 0xffc4cad8, 0xffbcc5d6, 0xffb8c5d6, 0xffbcc7dd, 0xffc2c6e3, 0xffb5b4d4, 0xff9793b4, 0xff8984a4, 0xff787192, 0xff807698, 0xff807293, 0xff877899, 0xff857496, 0xff8b779a, 0xff8d799e, 0xff89759a, 0xff867198, 0xff8a759c, 0xff9782ab, 0xffa18fb7, 0xffb7add0, 0xffa09abc, 0xff797395, 0xff514b6d, 0xff3f3a5a, 0xff443f5f, 0xff4f4869, 0xff534c6b, 0xff786f8e, 0xff867d9a, 0xff9b90ae, 0xffaca1bf, 0xffb6abc9, 0xffbcb1cf, 0xffc0b6d1, 0xffc2b8d3, 
    0xff9fb1d9, 0xff97a9d1, 0xff94a6ce, 0xff94a6ce, 0xff909fc6, 0xff7e8db4, 0xff6e7ca1, 0xff667596, 0xff576280, 0xff4e5a74, 0xff2c364f, 0xff091428, 0xff040d1e, 0xff08121e, 0xff08121c, 0xff0a141d, 0xff0b141d, 0xff09121b, 0xff08111a, 0xff08111a, 0xff07111a, 0xff08121c, 0xff09131d, 0xff09131d, 0xff081420, 0xff07131f, 0xff263441, 0xff82909d, 0xff94a1b1, 0xff97a4b4, 0xff99a6b6, 0xff96a3b3, 0xff9da9b9, 0xffa0acbc, 0xffa2abba, 0xffa0a8b5, 0xffa9adb9, 0xffa5a5b1, 0xff807d86, 0xff635c64, 0xff6b6066, 0xff5d4e51, 0xff564345, 0xff634d4f, 0xff755b5c, 0xff7a5e5d, 0xff705453, 0xff664a47, 0xff745854, 0xff866a66, 0xff947874, 0xff89706c, 0xff745c58, 0xff624e4d, 0xff5f4d4d, 0xff5c5052, 0xff4b4247, 0xff6d6a71, 0xff9fa1ad, 0xffb3bbc8, 0xffb8c4d4, 0xffb4c3d6, 0xffaabcd2, 0xffb8c8e1, 0xffbdc6e5, 0xffbcc1e1, 0xffb4b7d8, 0xffb5b6d5, 0xffb3b2d1, 0xffbab5d5, 0xffbab3d2, 0xffbdb4d3, 0xffbaafcf, 0xffbcb1d1, 0xffbdb2d4, 0xffbaafd1, 0xffb7abcf, 0xffb9add1, 0xffc1b5db, 0xffc5bde2, 0xffc3bfe0, 0xffa8a9c8, 0xff908faf, 0xff8889a8, 0xff9091af, 0xff9b9cba, 0xffa2a1c0, 0xffa3a2c1, 0xffb2b1d0, 0xffb7b6d5, 0xffbcbbda, 0xffbcbbda, 0xffb6b5d4, 0xffaeadcc, 0xffa7a7c3, 0xffa3a3bf, 
    0xff9eb0d8, 0xff9aacd4, 0xff96a8d0, 0xff94a6ce, 0xff8d9cc3, 0xff7e8db4, 0xff717fa4, 0xff6a789b, 0xff596484, 0xff4d5874, 0xff2a344f, 0xff081329, 0xff020a1d, 0xff030c1b, 0xff040e1a, 0xff07111b, 0xff08101b, 0xff08101b, 0xff08101b, 0xff070f1a, 0xff050f19, 0xff040e1a, 0xff050f1b, 0xff050f1b, 0xff020e1c, 0xff04101e, 0xff263441, 0xff818f9c, 0xff93a1ae, 0xff98a6b3, 0xff9ba8b8, 0xff97a4b4, 0xff9ca9b9, 0xffa4b0c0, 0xffa2aebc, 0xffa2acb8, 0xffa5abb7, 0xffa8aab6, 0xff9795a0, 0xff76707a, 0xff6c626a, 0xff5f5357, 0xff504043, 0xff483435, 0xff4a3232, 0xff4f3534, 0xff5a3e3d, 0xff624642, 0xff745952, 0xff836a63, 0xff8b726b, 0xff7b645e, 0xff624d48, 0xff53413d, 0xff4f3f3f, 0xff4c4243, 0xff4c474d, 0xff8b8b95, 0xffb9bfcd, 0xffafbbcb, 0xffa9b8cd, 0xffaec3d8, 0xffaac2da, 0xffb7cce7, 0xffb5c6e4, 0xffb6c3e3, 0xffbcc7e7, 0xffbdc6e5, 0xffc4cbe8, 0xffc2c6e3, 0xffc4c5e1, 0xffc1c1dd, 0xffc3c3dd, 0xffc1c1dd, 0xffbfbedd, 0xffbebfdd, 0xffbebfde, 0xffbcbfde, 0xffbbbedf, 0xffb9bede, 0xffbdc2e0, 0xffb9c0dd, 0xffbbc0de, 0xffbcc3e0, 0xffb8bfdc, 0xffb3bad7, 0xffbac1de, 0xffc6cdea, 0xffb4bdda, 0xffb4bdda, 0xffb2bbd8, 0xffafb8d5, 0xffa9b2d1, 0xffa5aecd, 0xffa1acca, 0xffa1acca, 
    0xff92a4cc, 0xff98aad2, 0xff9aacd4, 0xff93a5cd, 0xff8a99c0, 0xff7f8eb5, 0xff7482a9, 0xff6a789b, 0xff566083, 0xff4c5775, 0xff2d3752, 0xff101a33, 0xff0c1429, 0xff0a1225, 0xff07101f, 0xff0a1420, 0xff09111e, 0xff0b1320, 0xff0c1421, 0xff0b1320, 0xff091221, 0xff08121e, 0xff09131f, 0xff0a1420, 0xff081422, 0xff0b1725, 0xff2a3845, 0xff818f9c, 0xff919fac, 0xff98a6b3, 0xff9daaba, 0xff9aa7b7, 0xff9aa7b7, 0xffa3b0c0, 0xffa2aebc, 0xffa4b0be, 0xffa4acb9, 0xffa8aeba, 0xffadadb9, 0xff8c8b93, 0xff6a636a, 0xff665b5f, 0xff5d4e51, 0xff554244, 0xff533d3f, 0xff5c4442, 0xff6d5352, 0xff7a615c, 0xff826a60, 0xff846e63, 0xff7e685d, 0xff6c584f, 0xff59463f, 0xff4d3e39, 0xff4b3d3c, 0xff483f42, 0xff4f4c53, 0xffa1a5b0, 0xffc3ccdb, 0xffacbbce, 0xffa5bacf, 0xffacc4de, 0xffb0cbe6, 0xffb1cce9, 0xffb2c9e8, 0xffb1c6e5, 0xffb7cae8, 0xffb1c2de, 0xffb9c9e3, 0xffb3c1db, 0xffbbc5de, 0xffb8c2db, 0xffc1c9e0, 0xffbcc7dd, 0xffbac4dd, 0xffbbc7e1, 0xffbdcbe6, 0xffbbcbe5, 0xffb7c6e5, 0xffb2c1de, 0xffb5c3de, 0xffb4bfdb, 0xffb5c0dc, 0xffb8c3df, 0xffb3c1dc, 0xffabb9d4, 0xffaab8d5, 0xffaebdda, 0xffb7c6e3, 0xffb5c6e4, 0xffb3c4e2, 0xffafc2e0, 0xffacbfdf, 0xffa9bcdc, 0xffa5bad9, 0xffa4b9d8, 
    0xff7d8fb5, 0xff92a4ca, 0xff9dafd7, 0xff93a5cd, 0xff8897c0, 0xff8190b7, 0xff7684ab, 0xff667499, 0xff555f83, 0xff505b7b, 0xff3a4360, 0xff27314c, 0xff252d44, 0xff1f273c, 0xff161e31, 0xff172031, 0xff171e30, 0xff1a2131, 0xff1d2434, 0xff1e2535, 0xff1c2536, 0xff1c2534, 0xff1e2736, 0xff212a39, 0xff263240, 0xff25313f, 0xff3c4a57, 0xff8896a3, 0xff919fac, 0xff97a5b2, 0xff9eacb9, 0xff9ba9b6, 0xff99a9b8, 0xff9eaebd, 0xff9ba9b6, 0xffa5b1bf, 0xffa1abb7, 0xffa3abb6, 0xffb1b3bf, 0xff919199, 0xff68636a, 0xff6a6166, 0xff6b5f63, 0xff726263, 0xff7d696a, 0xff88706e, 0xff8e7473, 0xff8f7671, 0xff8f776d, 0xff7e685b, 0xff6a5449, 0xff5b473e, 0xff53423a, 0xff4e413b, 0xff4d4240, 0xff4b4547, 0xff5c5d62, 0xffafb6c0, 0xffb9c5d5, 0xffabbdd1, 0xffa9c1d9, 0xffa7c2dd, 0xffb1ceec, 0xffa9c8e7, 0xffb0cdeb, 0xffabc9e5, 0xffb4cfea, 0xffa9c3dc, 0xffb5cde5, 0xffadc3da, 0xffbacce2, 0xffb8cade, 0xffb5c7db, 0xffb4c6da, 0xffb2c7dc, 0xffb2c8df, 0xffb2cae2, 0xffb1cbe2, 0xffaec9e4, 0xffadc7e0, 0xffbbcfe8, 0xffb3c3dc, 0xffadbdd7, 0xffb2c2dc, 0xffbacbe5, 0xffbbcce6, 0xffb2c6e1, 0xffabc0dd, 0xffa5bdd9, 0xffa4bddb, 0xffa5bedc, 0xffa7c0de, 0xffa8c3e1, 0xffa9c4e2, 0xffa7c4e2, 0xffa7c4e2, 
    0xff6d7fa5, 0xff8c9ec4, 0xff9fb1d7, 0xff94a6cc, 0xff8897be, 0xff8392b9, 0xff7684ab, 0xff637196, 0xff546183, 0xff566181, 0xff475270, 0xff3b4761, 0xff3a445d, 0xff323d53, 0xff253044, 0xff222d41, 0xff242f41, 0xff283345, 0xff2d384a, 0xff31394c, 0xff31394c, 0xff323a4d, 0xff363f50, 0xff394253, 0xff485162, 0xff414d5d, 0xff4f5b6b, 0xff919dad, 0xff939faf, 0xff96a4b1, 0xff9ca9b9, 0xff9aa8b5, 0xff9cacb9, 0xff9cacb9, 0xff94a4b1, 0xffa2b0bd, 0xff9ba7b5, 0xff9ba5b1, 0xffa8aeba, 0xff888a96, 0xff605f67, 0xff6b666d, 0xff766d72, 0xff7b6f73, 0xff827273, 0xff8d7978, 0xff9a8582, 0xffa28d88, 0xff97837a, 0xff78665c, 0xff59463f, 0xff4c3b34, 0xff493c36, 0xff473e39, 0xff463e3c, 0xff434144, 0xff70737a, 0xffb9c3cd, 0xffadbdcd, 0xffa9bed3, 0xffb0c8e2, 0xffa4bfdd, 0xffb2d0f2, 0xffa8c6e8, 0xffaac7e7, 0xffa7c4e2, 0xffb4cfec, 0xffa8c2dd, 0xffb5cde7, 0xffa9bfd7, 0xffb2c8df, 0xffaec3d8, 0xffb2c7dc, 0xffb3c9de, 0xffb3c9e0, 0xffafc8de, 0xffacc6dd, 0xffabc7df, 0xffadc9e1, 0xffb1cbe4, 0xffaec2db, 0xffb2c3dd, 0xffb3c4de, 0xffb1c2dc, 0xffabbfd8, 0xffaabed9, 0xffa9bed9, 0xffa9c1dd, 0xffb0c8e4, 0xffacc5e3, 0xffaac3e1, 0xffa8c3e0, 0xffa8c3e1, 0xffaac5e3, 0xffa9c6e4, 0xffa9c6e4, 
    0xff7789ad, 0xff94a6ca, 0xff9aacd0, 0xff9aacd0, 0xff90a0c4, 0xff8292b4, 0xff7d8daf, 0xff697899, 0xff536281, 0xff4e5c79, 0xff485671, 0xff45536e, 0xff46526c, 0xff424e66, 0xff3a465e, 0xff314057, 0xff3d4e62, 0xff3e5064, 0xff3a4b5f, 0xff3b4a5f, 0xff455066, 0xff464e65, 0xff434b60, 0xff4c5268, 0xff555c6f, 0xff545b6e, 0xff5c6376, 0xff8890a3, 0xffa0a8bb, 0xff95a1b1, 0xff99a6b7, 0xff98a5b5, 0xff95a6b0, 0xffa4b6c0, 0xff9aacb6, 0xff98a8b5, 0xff97a5b2, 0xffa3afbd, 0xff9fa8b9, 0xff5c6272, 0xff414453, 0xff4d4d59, 0xff605d66, 0xff817a81, 0xffa89da1, 0xff8e8282, 0xff877978, 0xff756663, 0xff5b4c49, 0xff554545, 0xff4f4140, 0xff4a3f3d, 0xff443c3a, 0xff3f3b3a, 0xff413f40, 0xff44474c, 0xff848d96, 0xffb2becc, 0xffafc0d4, 0xffaabed7, 0xff9fb6d5, 0xffafc7eb, 0xff9fb8e0, 0xffa7c0e8, 0xffa7bbe0, 0xffb4c6ea, 0xffb9cbef, 0xffb1c4e5, 0xffafbfe0, 0xffb6c6e7, 0xffb9cae8, 0xffb5c6e2, 0xffb6c7e3, 0xffb6c7e3, 0xffb6c7e1, 0xffb6c7e1, 0xffb4c8e3, 0xffb4c8e3, 0xffb4c8e3, 0xffb4c8e1, 0xffb4c5e1, 0xffb4c5e1, 0xffb3c4e0, 0xffb3c4e0, 0xffb2c3df, 0xffb1c2de, 0xffaec2dd, 0xffaec2dd, 0xffb0c4df, 0xffafc2e0, 0xffafc2e0, 0xffadc2df, 0xffadc2df, 0xffacc1de, 0xffacc1de, 0xffacc1de, 
    0xff7b8db1, 0xff96a8cc, 0xff99abcf, 0xff99abcf, 0xff91a1c3, 0xff8494b6, 0xff7e8eaf, 0xff687997, 0xff566584, 0xff51607d, 0xff4b5b75, 0xff4a5a74, 0xff4b5973, 0xff495771, 0xff42506a, 0xff3b4b64, 0xff43586d, 0xff4b6075, 0xff4d5f75, 0xff516178, 0xff5c6880, 0xff5a647d, 0xff565d77, 0xff5c627a, 0xff5a6078, 0xff575d73, 0xff5c6278, 0xff878fa4, 0xffa0a8bd, 0xff97a2b4, 0xff9ba8b9, 0xff98a8b7, 0xff95a6b0, 0xff9bacb6, 0xff92a3ad, 0xffa2b0bd, 0xffa8b4c2, 0xff97a0b1, 0xff71788a, 0xff2c3242, 0xff333647, 0xff2a2c39, 0xff3c3a45, 0xff625f66, 0xff7e797d, 0xff5b5557, 0xff574f4d, 0xff483e3d, 0xff483c3c, 0xff453939, 0xff433739, 0xff43393a, 0xff42393a, 0xff3f3b3c, 0xff424244, 0xff474a4f, 0xff9097a1, 0xffb6c2d0, 0xffb8c7dc, 0xffbbcce6, 0xffa6badb, 0xff94acd0, 0xff6b84ac, 0xff687fa8, 0xff98aad2, 0xffacbce0, 0xffb8c8ec, 0xffb6c6ea, 0xffb4c2e5, 0xffb7c5e8, 0xffb6c5e6, 0xffb1c0e1, 0xffb7c6e5, 0xffb7c6e5, 0xffb7c6e3, 0xffb7c6e3, 0xffb7c6e3, 0xffb7c6e3, 0xffb7c7e1, 0xffb7c7e1, 0xffb4c5e1, 0xffb3c4e0, 0xffb3c4e0, 0xffb2c3df, 0xffb1c2de, 0xffb0c1dd, 0xffb0c1dd, 0xffb0c1dd, 0xffb1c2de, 0xffb1c2de, 0xffb0c1dd, 0xffb0c1dd, 0xffafc0dc, 0xffafc0dc, 0xffafc0dc, 0xffaebfdb, 
    0xff8294b8, 0xff99abcf, 0xff98aace, 0xff97a9cd, 0xff91a1c3, 0xff8696b8, 0xff8090b1, 0xff697a98, 0xff536281, 0xff52617e, 0xff54627d, 0xff576580, 0xff596781, 0xff53617b, 0xff47556f, 0xff3c4c65, 0xff4e6078, 0xff596b83, 0xff5c6e86, 0xff606e88, 0xff69758f, 0xff67718a, 0xff646b85, 0xff686f89, 0xff656c86, 0xff60687f, 0xff60687f, 0xff8590a4, 0xff9daabd, 0xff96a5b8, 0xff9aa9bc, 0xff97a7b7, 0xff9caab7, 0xffa4b0be, 0xff9ca5b4, 0xff9fa8b7, 0xff969cac, 0xff6f7281, 0xff4f5160, 0xff393846, 0xff21202e, 0xff14141e, 0xff35343c, 0xff5c5b61, 0xff5c5c5e, 0xff2f2f2f, 0xff3d3d3d, 0xff3f3b3a, 0xff3e3433, 0xff3d3230, 0xff3f3333, 0xff433737, 0xff443a39, 0xff443c3a, 0xff484244, 0xff4c4b50, 0xff7d8087, 0xff969ea9, 0xff9ea9bb, 0xffafbfd6, 0xffa3b8d5, 0xff8098ba, 0xff4f6b90, 0xff506a8f, 0xff8ca2c7, 0xffa3b5d9, 0xffb2c4e8, 0xffb3c6e7, 0xffb2c2e4, 0xffb3c3e5, 0xffb4c4e5, 0xffb2c2e3, 0xffb7c6e5, 0xffb7c6e5, 0xffb7c6e3, 0xffb7c6e3, 0xffb8c6e1, 0xffb8c6e1, 0xffb8c6e1, 0xffb7c7e1, 0xffb3c4e0, 0xffb3c4e0, 0xffb2c3df, 0xffb1c2de, 0xffb0c1dd, 0xffb0c1dd, 0xffafc0dc, 0xffafc0dc, 0xffafc0dc, 0xffafc0dc, 0xffafc0dc, 0xffafc0dc, 0xffaebfdb, 0xffaebfdb, 0xffaebfdb, 0xffaebfdb, 
    0xff889abe, 0xff9caed2, 0xff98aace, 0xff95a7cb, 0xff91a1c3, 0xff8797b9, 0xff8292b3, 0xff6a7b99, 0xff4c5b7a, 0xff505f7c, 0xff5a6883, 0xff616f8a, 0xff616f89, 0xff54627c, 0xff3f4d67, 0xff2e3c56, 0xff4b5b74, 0xff596982, 0xff5c6c86, 0xff5c6a84, 0xff636f89, 0xff636d88, 0xff606a85, 0xff667089, 0xff6d7790, 0xff667089, 0xff616d85, 0xff8291a6, 0xff99a8bd, 0xff94a5b7, 0xff97a9bd, 0xff93a4b6, 0xff97a2b4, 0xffa1aabb, 0xff8f96a6, 0xff7a8090, 0xff666877, 0xff403e4c, 0xff2c2836, 0xff34313c, 0xff16101a, 0xff06030a, 0xff29272c, 0xff4b4a4f, 0xff424345, 0xff1d2122, 0xff393f3f, 0xff393b3a, 0xff3f3a36, 0xff423733, 0xff453a36, 0xff4a3f3b, 0xff4e403d, 0xff4e403f, 0xff504444, 0xff554c4f, 0xff605d64, 0xff656773, 0xff6b7282, 0xff8493a8, 0xff92a6bf, 0xff7992b0, 0xff5f7e9d, 0xff708eb0, 0xff93abcd, 0xffa3bada, 0xffb0c4e5, 0xffaec2e3, 0xffaabedf, 0xffadc0e0, 0xffb3c6e6, 0xffb9cae8, 0xffb5c6e4, 0xffb6c5e4, 0xffb6c5e2, 0xffb6c5e2, 0xffb7c5e0, 0xffb7c5e0, 0xffb7c5e0, 0xffb7c5e0, 0xffb3c2df, 0xffb1c2de, 0xffb1c2de, 0xffb0c1dd, 0xffafc0dc, 0xffaebfdb, 0xffaebfdb, 0xffaebfdb, 0xffadbeda, 0xffadbeda, 0xffadbeda, 0xffadbeda, 0xffadbeda, 0xffadbeda, 0xffadbeda, 0xffadbeda, 
    0xff8d9dc1, 0xff9fafd3, 0xff9aaace, 0xff97a7cb, 0xff91a1c3, 0xff8898ba, 0xff8493b4, 0xff6d7c9b, 0xff4e5d7c, 0xff51607d, 0xff586681, 0xff5d6b86, 0xff5a6882, 0xff495771, 0xff333f59, 0xff1f2d47, 0xff3a4863, 0xff4f5d78, 0xff5a6885, 0xff5d6884, 0xff626d89, 0xff616c88, 0xff5e6985, 0xff616d87, 0xff67738d, 0xff616f89, 0xff5d6d86, 0xff7d8fa5, 0xff94a6bc, 0xff90a5b8, 0xff94aabf, 0xff8fa4b7, 0xff9ca9bc, 0xffa1a8bb, 0xff8a8fa2, 0xff6c6d7f, 0xff605e6c, 0xff49434f, 0xff291f28, 0xff22191e, 0xff392e34, 0xff231a1d, 0xff302c2d, 0xff3d3d3d, 0xff38393b, 0xff252a2d, 0xff414b4d, 0xff373d3d, 0xff44413c, 0xff483f38, 0xff4c413b, 0xff534640, 0xff584642, 0xff594642, 0xff5c4a48, 0xff605051, 0xff655a60, 0xff5a545e, 0xff545663, 0xff657082, 0xff7f94a9, 0xff728ea6, 0xff6e90ab, 0xff8aacc8, 0xff9bb6d4, 0xffa7c0de, 0xffb0c9e7, 0xffaec7e5, 0xffaac1e0, 0xffacc1de, 0xffb1c6e3, 0xffb5c8e6, 0xffb3c4e2, 0xffb3c4e0, 0xffb4c3e0, 0xffb4c3e0, 0xffb5c3de, 0xffb7c2de, 0xffb7c2de, 0xffb5c3de, 0xffb1c0dd, 0xffb0c1dd, 0xffb0c1dd, 0xffafc0dc, 0xffaebfdb, 0xffadbeda, 0xffadbeda, 0xffacbdd9, 0xffabbcd8, 0xffabbcd8, 0xffabbcd8, 0xffabbcd8, 0xffabbcd8, 0xffabbcd8, 0xffabbcd8, 0xffabbcd8, 
    0xff8a9abe, 0xff9fafd3, 0xff9babcf, 0xff98a8cc, 0xff90a0c2, 0xff8797b9, 0xff8594b5, 0xff707f9e, 0xff556483, 0xff546380, 0xff54627d, 0xff53617c, 0xff4d5b75, 0xff3f4d67, 0xff2c3852, 0xff1d2844, 0xff2a3553, 0xff4a5573, 0xff5f6a88, 0xff66718f, 0xff6c7795, 0xff6a7591, 0xff616f8a, 0xff616f8a, 0xff586882, 0xff596b83, 0xff5c6e86, 0xff7a90a7, 0xff90a6bd, 0xff8ea7bb, 0xff94afc2, 0xff8fa8bc, 0xff9aaac1, 0xffa0acc2, 0xff9aa2b5, 0xff777a8b, 0xff656370, 0xff605b62, 0xff4c4145, 0xff483c3e, 0xff584a4a, 0xff524646, 0xff5b5351, 0xff514d4c, 0xff3a3a3c, 0xff1e2327, 0xff414a4f, 0xff484d50, 0xff46433c, 0xff4a4237, 0xff51443c, 0xff5a4941, 0xff5f4a45, 0xff624a46, 0xff674f4d, 0xff6c5658, 0xff756266, 0xff685d65, 0xff5f5d6a, 0xff626979, 0xff8194a5, 0xff7591a6, 0xff779bb1, 0xff8db1c9, 0xff97b7d0, 0xffa2c0da, 0xffaccae6, 0xffb1cce7, 0xffafc9e4, 0xffacc4e0, 0xffaac2de, 0xffadc2dd, 0xffafc3de, 0xffb1c2de, 0xffb2c1de, 0xffb2c1de, 0xffb3c1dc, 0xffb5c0dc, 0xffb5c0dc, 0xffb3c1dc, 0xffb0bfdc, 0xffafc0dc, 0xffaebfdb, 0xffaebfdb, 0xffadbeda, 0xffacbdd9, 0xffabbcd8, 0xffabbcd8, 0xffa9bad6, 0xffa9bad6, 0xffa9bad6, 0xffa9bad6, 0xffaabbd7, 0xffaabbd7, 0xffaabbd7, 0xffaabbd7, 
    0xff8696ba, 0xff9dadd1, 0xff9dadd1, 0xff99a9cd, 0xff909ec1, 0xff8694b7, 0xff8594b5, 0xff7281a0, 0xff566383, 0xff556380, 0xff57627e, 0xff57627e, 0xff515d77, 0xff414d67, 0xff2c3852, 0xff1c2743, 0xff293452, 0xff4a5575, 0xff606b8b, 0xff646f8d, 0xff677592, 0xff677592, 0xff61708d, 0xff61728c, 0xff546881, 0xff5c728a, 0xff637991, 0xff809bb0, 0xff93aec3, 0xff90acc1, 0xff95b4c8, 0xff92aec3, 0xff92abc1, 0xff90a5ba, 0xff99aabc, 0xff798291, 0xff5c5f68, 0xff605e63, 0xff605657, 0xff695b5a, 0xff584946, 0xff5f504d, 0xff6b605e, 0xff6c6364, 0xff5c575b, 0xff2c2b33, 0xff3c3f48, 0xff494a4f, 0xff4a4740, 0xff4e4639, 0xff54473e, 0xff5e4c42, 0xff654c47, 0xff694d49, 0xff705250, 0xff745a5b, 0xff745e61, 0xff736469, 0xff6f6c75, 0xff676f7a, 0xff899caa, 0xff829faf, 0xff82a7b9, 0xff88b1c3, 0xff91b5cb, 0xff96b9cf, 0xff9fbfd8, 0xffa5c5dc, 0xffa8c6de, 0xffa9c5dd, 0xffa8c2db, 0xffa9c1d9, 0xffadc1da, 0xffafc0da, 0xffb0c0da, 0xffb0c0da, 0xffb1bfda, 0xffb1bfda, 0xffb3beda, 0xffb1bfda, 0xffafbedb, 0xffaebfdb, 0xffaebfdb, 0xffadbeda, 0xffacbdd9, 0xffabbcd8, 0xffabbcd8, 0xffaabbd7, 0xffa7b8d4, 0xffa7b8d4, 0xffa8b9d5, 0xffa8b9d5, 0xffa9bad6, 0xffa9bad6, 0xffa9bad6, 0xffaabbd7, 
    0xff8393b7, 0xff9cacd0, 0xff9eaed2, 0xff9aaace, 0xff8f9dc0, 0xff8593b6, 0xff8594b5, 0xff7483a2, 0xff4e5b7b, 0xff53617e, 0xff5c6783, 0xff616c88, 0xff5c6882, 0xff49556f, 0xff2d3953, 0xff192440, 0xff333c5b, 0xff4e5979, 0xff5c6787, 0xff566383, 0xff586683, 0xff5b6a87, 0xff5c6d89, 0xff5f738e, 0xff5b7189, 0xff647e95, 0xff6d879e, 0xff8ba7bd, 0xff97b6ca, 0xff91b2c5, 0xff97b8cb, 0xff8fb2c5, 0xffa7cade, 0xff8cabbf, 0xff96b0bf, 0xff8698a4, 0xff717c82, 0xff747577, 0xff625d5a, 0xff5a4f4b, 0xff6a5b56, 0xff5c4d48, 0xff645454, 0xff8b7c7f, 0xffa79ca4, 0xff6f6772, 0xff534f5d, 0xff444148, 0xff524d47, 0xff534b3e, 0xff594b40, 0xff614f45, 0xff674e47, 0xff6c4f49, 0xff735451, 0xff795b5b, 0xff70575a, 0xff78686b, 0xff79747b, 0xff69727b, 0xff8ea2ad, 0xff87a7b4, 0xff88afbe, 0xff85b0c0, 0xff8fb5c8, 0xff8eb3c6, 0xff8fb3c9, 0xff95b8cc, 0xff9dbdd4, 0xffa3c2d7, 0xffaac6dc, 0xffaec8df, 0xffabc1d9, 0xffacc0d9, 0xffaebfd9, 0xffafbfd9, 0xffafbfd9, 0xffb0bed9, 0xffb2bdd9, 0xffb0bed9, 0xffafbedb, 0xffaebfdb, 0xffadbeda, 0xffacbdd9, 0xffabbcd8, 0xffabbcd8, 0xffaabbd7, 0xffaabbd7, 0xffa6b7d3, 0xffa7b8d4, 0xffa7b8d4, 0xffa8b9d5, 0xffa8b9d5, 0xffa9bad6, 0xffa9bad6, 0xffa9bad6, 
    0xff8694b9, 0xffa2b0d5, 0xffa4b2d7, 0xff9dabd0, 0xff919fc2, 0xff8896b9, 0xff8794b6, 0xff717e9e, 0xff596484, 0xff576280, 0xff57627e, 0xff57627e, 0xff515b76, 0xff444e69, 0xff39435e, 0xff363f5c, 0xff4d5876, 0xff545f7f, 0xff5c6787, 0xff616e8e, 0xff667592, 0xff657692, 0xff5b708b, 0xff516983, 0xff657f96, 0xff7594a9, 0xff8dacc1, 0xff9cbcd1, 0xff9dc0d3, 0xff9bc0d2, 0xff9cc1d3, 0xff9dc4d5, 0xff97c5d5, 0xff98c3d3, 0xff92b8c3, 0xff86a4ac, 0xff7a8d91, 0xff6f7978, 0xff656661, 0xff5e5751, 0xff5b4e48, 0xff63544f, 0xff645252, 0xff715e62, 0xff907f89, 0xffa2929f, 0xff5d4f60, 0xff534751, 0xff534c46, 0xff534b40, 0xff5a4c41, 0xff625046, 0xff695049, 0xff6c4f49, 0xff725350, 0xff775957, 0xff786060, 0xff7e6f72, 0xff737176, 0xff7d868d, 0xff98aeb9, 0xff8fb1bb, 0xff80aab6, 0xff93c1ce, 0xff99c3d3, 0xff9dc4d5, 0xff9cc2d5, 0xff98bdcf, 0xff99bcd0, 0xff9fc0d3, 0xffa0bfd3, 0xffa0bcd1, 0xffa8c1d7, 0xffabc1d8, 0xffadc1d9, 0xffb0c2da, 0xffb0c0da, 0xffafbfd9, 0xffaebcd7, 0xffadbbd6, 0xffadbcd9, 0xffabbcd8, 0xffaabbd7, 0xffa9bad6, 0xffa9bad6, 0xffa9bad6, 0xffa9bad6, 0xffaabbd7, 0xffa9bad6, 0xffa9bad6, 0xffa9bad6, 0xffa8b9d5, 0xffa8b9d5, 0xffa8b9d5, 0xffa7b8d4, 0xffa7b8d4, 
    0xff8694b9, 0xffa2b0d5, 0xffa3b1d6, 0xff9dabd0, 0xff919fc2, 0xff8997ba, 0xff8895b7, 0xff717e9e, 0xff535e7e, 0xff525d7b, 0xff545f7b, 0xff56617d, 0xff535d78, 0xff49536e, 0xff424c67, 0xff404966, 0xff4d5876, 0xff566383, 0xff637090, 0xff687794, 0xff637490, 0xff5d728d, 0xff5f7791, 0xff637f95, 0xff8faec3, 0xff97b7cc, 0xff9fc2d6, 0xffa2c7d9, 0xffa0c5d7, 0xff9fc4d6, 0xffa0c7d8, 0xffa0cbdb, 0xff98ccda, 0xff94c8d5, 0xff8ebdc5, 0xff85acb1, 0xff7e9c9e, 0xff758987, 0xff68716e, 0xff5c5d58, 0xff625a57, 0xff635554, 0xff584849, 0xff5c494f, 0xff5f4b56, 0xff776271, 0xff644f62, 0xff62505c, 0xff564b49, 0xff564c42, 0xff5c4e45, 0xff64514a, 0xff68514b, 0xff6c504c, 0xff725652, 0xff775e5a, 0xff7a6667, 0xff85797b, 0xff7d7c81, 0xff7f8c92, 0xff98b1b8, 0xff91b6bf, 0xff85b1ba, 0xff95c4ce, 0xff99c5d2, 0xff9ec8d6, 0xffa1c8d7, 0xff9fc6d5, 0xffa1c6d8, 0xffa5c8db, 0xffa6c7da, 0xffa4c3d7, 0xffa4bfd4, 0xffa6bfd5, 0xffa9bfd6, 0xffabbfd7, 0xffadbed8, 0xffadbed8, 0xffaebed8, 0xffaebed8, 0xffabbcd8, 0xffabbcd8, 0xffaabbd7, 0xffaabbd7, 0xffa9bad6, 0xffa9bad6, 0xffa9bad6, 0xffa9bad6, 0xffa9bad6, 0xffa9bad6, 0xffa8b9d5, 0xffa8b9d5, 0xffa8b9d5, 0xffa7b8d4, 0xffa7b8d4, 0xffa7b8d4, 
    0xff8694b9, 0xffa1afd4, 0xffa3afd5, 0xff9da9cf, 0xff93a0c3, 0xff8b98bb, 0xff8a94b7, 0xff737e9e, 0xff545f7f, 0xff535e7c, 0xff57607d, 0xff5b6481, 0xff5b6580, 0xff56607b, 0xff525c77, 0xff525d79, 0xff596784, 0xff546380, 0xff556481, 0xff5a6b87, 0xff5d728d, 0xff677f97, 0xff7d99af, 0xff92b2c7, 0xff9fc2d6, 0xffa1c6d8, 0xffa4c9db, 0xffa5ccdd, 0xffa5ccdd, 0xffa5ccdb, 0xffa6cddc, 0xffa3d1e0, 0xff9ed4e0, 0xff96d1d9, 0xff91c6cc, 0xff8dbabf, 0xff89afb0, 0xff819e9c, 0xff708381, 0xff626b68, 0xff585856, 0xff585252, 0xff53474b, 0xff5c4b53, 0xff382430, 0xff483141, 0xff60495b, 0xff5c4854, 0xff5a4c4b, 0xff5a5047, 0xff61524b, 0xff67564f, 0xff69544f, 0xff6c5450, 0xff745c58, 0xff7b6864, 0xff807272, 0xff8f898b, 0xff898e92, 0xff86979e, 0xff97b4bc, 0xff98bdc6, 0xff8fbbc6, 0xff98c7d1, 0xff98c4cf, 0xff9fc9d5, 0xffa5cdd9, 0xffa6ceda, 0xffa9cede, 0xffacd0e0, 0xffadcedf, 0xffaac9db, 0xffa3c0d2, 0xffa4bfd2, 0xffa4bfd4, 0xffa5bed4, 0xffa8bed5, 0xffa9bfd6, 0xffabbfd7, 0xffacc0d9, 0xffabbcd8, 0xffabbcd8, 0xffaabbd7, 0xffaabbd7, 0xffa9bad6, 0xffa9bad6, 0xffa9bad6, 0xffa9bad6, 0xffa8b9d5, 0xffa8b9d5, 0xffa8b9d5, 0xffa7b8d4, 0xffa7b8d4, 0xffa7b8d4, 0xffa6b7d3, 0xffa6b7d3, 
    0xff8593b8, 0xffa0aed3, 0xffa1add3, 0xff9ca8ce, 0xff94a1c4, 0xff8c99bc, 0xff8b95b8, 0xff747f9f, 0xff566181, 0xff55607e, 0xff58617e, 0xff5d6683, 0xff5e6883, 0xff5b6580, 0xff5a647f, 0xff5b6781, 0xff62728c, 0xff51627c, 0xff4c5d79, 0xff5a7088, 0xff7088a0, 0xff839fb5, 0xff96b6cb, 0xffa6c9dc, 0xff9cc1d3, 0xff9ec5d6, 0xffa5ccdd, 0xffaad1e0, 0xffadd4e3, 0xffabd3df, 0xffa6cddc, 0xff9fcbd8, 0xff9fd5df, 0xff9cd5dc, 0xff9acfd5, 0xff96c6ca, 0xff93bcbe, 0xff8aaeae, 0xff7b9596, 0xff6d7f7f, 0xff606a6b, 0xff5e6265, 0xff5e5c61, 0xff71676f, 0xff382835, 0xff3d2b39, 0xff6c5766, 0xff604c57, 0xff5e5050, 0xff5f514e, 0xff645653, 0xff695a55, 0xff6b5955, 0xff6d5b57, 0xff756663, 0xff7e7371, 0xff868080, 0xff97989a, 0xff949fa3, 0xff8ea2a9, 0xff98b6be, 0xff9cc2cd, 0xff98c4d1, 0xff9bc9d6, 0xff99c5ce, 0xffa0cbd4, 0xffa5cdd7, 0xffa6ceda, 0xffa9cfdc, 0xffadd3e0, 0xffaed2e2, 0xffadcedf, 0xffa8c7d9, 0xffa8c5d7, 0xffa6c2d7, 0xffa4c0d5, 0xffa4bfd4, 0xffa4bfd4, 0xffa6bfd5, 0xffa9bfd6, 0xffa8bcd7, 0xffaabbd7, 0xffaabbd7, 0xffaabbd7, 0xffaabbd7, 0xffa9bad6, 0xffa8b9d5, 0xffa8b9d5, 0xffa7b8d4, 0xffa7b8d4, 0xffa7b8d4, 0xffa7b8d4, 0xffa6b7d3, 0xffa6b7d3, 0xffa6b7d3, 0xffa5b6d2, 
    0xff8692b8, 0xffa0acd2, 0xffa0acd2, 0xff9ca8ce, 0xff94a1c4, 0xff8e9bbe, 0xff8d97ba, 0xff747f9f, 0xff555e7f, 0xff535c7b, 0xff545d7a, 0xff59627f, 0xff5b627e, 0xff585f7b, 0xff585f7b, 0xff57637d, 0xff596982, 0xff576b83, 0xff657b93, 0xff7f98ae, 0xff94afc4, 0xff98b7cb, 0xff98bbce, 0xff9abfcf, 0xffa9d0df, 0xffa9d3e1, 0xffacd6e4, 0xffb0dae6, 0xffb2dae6, 0xffadd5df, 0xffa4cad7, 0xff9ac4d0, 0xff9bcbd5, 0xff9ed0d7, 0xffa0cfd5, 0xff9bc8ce, 0xff93bec4, 0xff8ab1b6, 0xff7e9fa4, 0xff749094, 0xff677c81, 0xff5b6a6f, 0xff565f64, 0xff6f6f77, 0xff3b353f, 0xff403641, 0xff6b5d6a, 0xff61515b, 0xff605154, 0xff605252, 0xff645655, 0xff695b5a, 0xff6b5d5c, 0xff6e635f, 0xff776f6c, 0xff807c79, 0xff8e908f, 0xff97a1a2, 0xff98aaae, 0xff91acb3, 0xff98b8c3, 0xff9ec6d2, 0xff9ccad9, 0xff9ccad7, 0xff9fcad3, 0xffa4ccd4, 0xffa6ccd5, 0xffa5cbd6, 0xffa7cdd8, 0xffacd2dd, 0xffafd3e1, 0xffafd0df, 0xffaecfe0, 0xffadccde, 0xffa9c8da, 0xffa5c4d6, 0xffa4c0d5, 0xffa2bed3, 0xffa1bdd2, 0xffa2bdd2, 0xffa7bbd6, 0xffa9bad6, 0xffaabbd7, 0xffaabbd7, 0xffaabbd7, 0xffa9bad6, 0xffa7b8d4, 0xffa7b8d4, 0xffa7b8d4, 0xffa6b7d3, 0xffa6b7d3, 0xffa6b7d3, 0xffa5b6d2, 0xffa5b6d2, 0xffa5b6d2, 0xffa5b6d2, 
    0xff8692b8, 0xff9fabd1, 0xff9eaad0, 0xff9ba7cd, 0xff95a2c5, 0xff8f9cbf, 0xff8e98bb, 0xff747f9f, 0xff565f80, 0xff545d7c, 0xff565f7c, 0xff5b6481, 0xff5d6480, 0xff5a617d, 0xff59607c, 0xff58647e, 0xff54667e, 0xff677d94, 0xff839cb2, 0xff96b1c6, 0xff9bbace, 0xff9fc0d1, 0xffa3c8d8, 0xffa9d0df, 0xffb0dae8, 0xffaed8e4, 0xffadd7e3, 0xffacd7e0, 0xffadd5df, 0xffabd3dd, 0xffa9cfda, 0xffa7cdd8, 0xff9ac2cc, 0xff9dc8cf, 0xffa0c8d0, 0xff97c2c9, 0xff8fbac1, 0xff88b0b8, 0xff7ea4ad, 0xff789aa3, 0xff66848c, 0xff58737a, 0xff5a6d73, 0xff79868c, 0xff63686e, 0xff65646c, 0xff69646b, 0xff5f555d, 0xff64555a, 0xff635356, 0xff645558, 0xff695d5d, 0xff6f6564, 0xff746f6c, 0xff7c7b77, 0xff838884, 0xff929c9b, 0xff93a3a3, 0xff94acb0, 0xff94b2ba, 0xff96bac6, 0xff9bc5d3, 0xff9dcbdb, 0xff9bc9d8, 0xffa3ced7, 0xffa8ced7, 0xffa7cdd6, 0xffa4cad3, 0xffa4cad5, 0xffa9cfda, 0xffabcfdd, 0xffaacedc, 0xffaccdde, 0xffaacbdc, 0xffa7c8d9, 0xffa4c5d6, 0xffa0c1d4, 0xff9dbed1, 0xff9dbcd0, 0xff9ebacf, 0xffa5bad5, 0xffa9bad6, 0xffaabbd7, 0xffaabbd7, 0xffaabbd7, 0xffa9bad6, 0xffa7b8d4, 0xffa6b7d3, 0xffa6b7d3, 0xffa6b7d3, 0xffa5b6d2, 0xffa5b6d2, 0xffa5b6d2, 0xffa4b5d1, 0xffa4b5d1, 0xffa4b5d1, 
    0xff8591b7, 0xff9eaad0, 0xff9da9cf, 0xff9aa6cc, 0xff98a2c6, 0xff929cc0, 0xff8f99bc, 0xff7580a0, 0xff555e7f, 0xff555e7d, 0xff58617e, 0xff5f6885, 0xff636a86, 0xff616884, 0xff606783, 0xff5f6b85, 0xff62778c, 0xff7a93a7, 0xff91acbf, 0xff97b4c6, 0xff98b9ca, 0xffa4c8d8, 0xffb2d9e8, 0xffb8e2ee, 0xffa9d3df, 0xffa7d3dc, 0xffa5d1da, 0xffa5d0d9, 0xffa7cfd9, 0xffa9d1d9, 0xffacd2db, 0xffb0d5de, 0xffa0c0cb, 0xffa1c1cc, 0xff9dbfc9, 0xff96bbc4, 0xff90b8c2, 0xff8bb5c1, 0xff82acb8, 0xff79a4ad, 0xff769ca5, 0xff6c8e97, 0xff6f8c92, 0xff90a8ac, 0xff9cabb0, 0xff9da7a9, 0xff74797c, 0xff767477, 0xff6e6168, 0xff6c5b61, 0xff6b5c61, 0xff716768, 0xff7c7374, 0xff81807e, 0xff8a8c89, 0xff8e9794, 0xff95a5a4, 0xff8ba0a1, 0xff8ca8ac, 0xff95b6bf, 0xff96bcc7, 0xff99c3d3, 0xff9bc8db, 0xff9bc6d7, 0xffa3cbd5, 0xffa8cdd5, 0xffa7ccd4, 0xffa3c8d0, 0xffa2c7d0, 0xffa3c8d1, 0xffa2c6d2, 0xff9ec2ce, 0xff9ec2d0, 0xff9ec2d0, 0xff9ec2d2, 0xff9dc1d1, 0xff9cbfd2, 0xff9bbed1, 0xff9bbccf, 0xff9bbace, 0xffa4b9d4, 0xffa8b9d5, 0xffaabbd7, 0xffaabbd7, 0xffaabbd7, 0xffa8b9d5, 0xffa6b7d3, 0xffa5b6d2, 0xffa5b6d2, 0xffa5b6d2, 0xffa5b6d2, 0xffa4b5d1, 0xffa4b5d1, 0xffa4b5d1, 0xffa3b4d0, 0xffa3b4d0, 
    0xff8591b7, 0xff9caacf, 0xff9caacf, 0xff99a7cc, 0xff96a2c8, 0xff919ec1, 0xff8d9abd, 0xff7380a2, 0xff4e5979, 0xff4f5a78, 0xff545f7d, 0xff5d6884, 0xff626b88, 0xff606a85, 0xff606a85, 0xff606e88, 0xff7389a0, 0xff87a4b6, 0xff98b7c9, 0xff9abbcc, 0xffa0c4d4, 0xffaed3e3, 0xffb0d8e4, 0xffa5cfdb, 0xffa7d1dd, 0xffa8d3dc, 0xffaad5de, 0xffa8d3dc, 0xffa8d0da, 0xffa6ced6, 0xffa7cdd6, 0xffaacfd8, 0xffa7c5d0, 0xffa3c1cb, 0xff9bbbc6, 0xff94b9c2, 0xff95bbc6, 0xff92bcc8, 0xff88b4bf, 0xff7aa9b3, 0xff7ca8b1, 0xff6c949c, 0xff63858e, 0xff79989d, 0xff99b2b7, 0xffa3b6ba, 0xff6d7c7f, 0xff828a8d, 0xff737077, 0xff6e676f, 0xff6a676e, 0xff737278, 0xff7f8386, 0xff899193, 0xff929d9f, 0xff94a6a6, 0xff92aaaa, 0xff819fa1, 0xff84a5aa, 0xff91b8bf, 0xff94bfc8, 0xff96c2cf, 0xff99c7d7, 0xff99c4d4, 0xff9ec6d0, 0xffa3cad1, 0xffa4cad3, 0xffa0c6cf, 0xff9dc3cc, 0xff9cc2cd, 0xff98bec9, 0xff92b8c3, 0xff91b7c4, 0xff93b9c6, 0xff95baca, 0xff98bdcd, 0xff99bed0, 0xff99bed0, 0xff99bed0, 0xff9cbbcf, 0xffa5b9d2, 0xffa9b9d3, 0xffabbad7, 0xffacbcd6, 0xffabbad7, 0xffa9b9d3, 0xffa6b7d3, 0xffa5b6d0, 0xffa5b6d2, 0xffa5b6d0, 0xffa4b5d1, 0xffa4b5cf, 0xffa4b5d1, 0xffa1b5ce, 0xffa3b4d0, 0xffa1b5ce, 
    0xff7886ad, 0xff99a8cf, 0xffa0afd6, 0xff9cacd0, 0xff93a3c7, 0xff8d9dbf, 0xff8898ba, 0xff6c7c9d, 0xff516182, 0xff50617f, 0xff495875, 0xff51617b, 0xff5b6984, 0xff4a5873, 0xff4e5975, 0xff6e8098, 0xff8fabc0, 0xff8fb2c5, 0xff90b3c6, 0xff98bbce, 0xffb8dded, 0xffa3c8d8, 0xffa3c8d8, 0xffa6ceda, 0xffa3cbd7, 0xffacd4e0, 0xffabd3df, 0xffa5cdd7, 0xffa3c9d4, 0xff9cc2cd, 0xff9ec4cf, 0xffadd2db, 0xffa4c6d0, 0xffa3c5cf, 0xff90b5be, 0xff8db3bc, 0xff9ec6ce, 0xff96c1ca, 0xff84b0b9, 0xff84b3bb, 0xff89b6bc, 0xff7ba6ad, 0xff7197a0, 0xff5d828a, 0xff1e3f48, 0xff67848c, 0xff7c97a0, 0xff728991, 0xff8798a2, 0xff8899a3, 0xff8a9ba5, 0xff8b9da7, 0xff859ca4, 0xff819aa1, 0xff849fa6, 0xff8baaaf, 0xff93b6bc, 0xff91b6bc, 0xff8fb8be, 0xff8eb9c0, 0xff8ebdc3, 0xff92c1c9, 0xff95c5cf, 0xff98c7cf, 0xffa0cbd4, 0xffa1c9d1, 0xff9ec6d0, 0xff9ac2cc, 0xff9ac2cc, 0xff98c0cc, 0xff8fb7c3, 0xff85adb9, 0xff82a9b8, 0xff97becd, 0xff87aebd, 0xff8fb6c5, 0xffa0c7d8, 0xff8db4c5, 0xff8bb2c3, 0xffa1c2d5, 0xffa9bbd3, 0xffaebad4, 0xffacbad5, 0xffadbbd5, 0xffadbbd6, 0xffacbad4, 0xffabbbd5, 0xffaabad3, 0xffa4b5cf, 0xffa4b6ce, 0xffa1b5ce, 0xffa1b5cd, 0xffa0b4cd, 0xff9fb5cc, 0xff9fb3cc, 0xff9eb4cb, 
    0xff7786ad, 0xff98a7ce, 0xff9caed4, 0xff98aad0, 0xff90a2c6, 0xff8b9dc1, 0xff879abb, 0xff6c7fa0, 0xff4d6080, 0xff4f6280, 0xff50617f, 0xff5b6c88, 0xff64748e, 0xff55657f, 0xff596782, 0xff7589a2, 0xff89a8bc, 0xff84a9bb, 0xff88adbf, 0xff93b8ca, 0xffb4d9e9, 0xff9bc0d0, 0xff96bbcb, 0xff99bece, 0xff9bc1ce, 0xffa2c8d5, 0xffa1c7d4, 0xff9ec4d1, 0xff9fc5d0, 0xff98bec9, 0xff98bec9, 0xffa5cbd6, 0xffa2c7d0, 0xffa1c6cf, 0xff92b8c1, 0xff8fb7bf, 0xff9ac5cc, 0xff93bec5, 0xff85b2b8, 0xff87b6bc, 0xff7aa7ad, 0xff83aeb5, 0xff6d959d, 0xff4f757e, 0xff11363f, 0xff6a8c96, 0xff7896a1, 0xff6a8893, 0xff65818d, 0xff67838f, 0xff6d8995, 0xff73919c, 0xff74949f, 0xff7597a1, 0xff7ca1aa, 0xff85abb4, 0xff98bec7, 0xff95bdc5, 0xff91bcc3, 0xff90bdc3, 0xff8fbec4, 0xff91c2c7, 0xff95c6cb, 0xff99c8ce, 0xff9dc9d2, 0xff9ec9d2, 0xff9bc6cf, 0xff98c3cc, 0xff98c2ce, 0xff97c1cd, 0xff8eb8c4, 0xff84aeba, 0xff7da7b5, 0xff8fb9c7, 0xff80aab8, 0xff89b3c1, 0xff99c3d3, 0xff87b1c1, 0xff85afbf, 0xff9abbce, 0xffa6b8d0, 0xffabb7d1, 0xffabb7d1, 0xffaab8d2, 0xffacb8d2, 0xffa9b7d1, 0xffa8b8d1, 0xffa7b7d0, 0xffa5b7cf, 0xffa5b7cf, 0xffa3b7cf, 0xffa3b7cf, 0xffa1b7ce, 0xffa1b7ce, 0xffa0b6cd, 0xffa0b6cd, 
    0xff7786ad, 0xff98a7ce, 0xff9badd3, 0xff96a8ce, 0xff8fa1c5, 0xff8b9dc1, 0xff899cbd, 0xff6e81a2, 0xff4e6181, 0xff4f6280, 0xff516280, 0xff5a6b87, 0xff62728c, 0xff5b6b85, 0xff667690, 0xff8296af, 0xff85a4b8, 0xff759aac, 0xff80a5b7, 0xff90b5c7, 0xffaed3e3, 0xff92b7c7, 0xff84a9b9, 0xff85aaba, 0xff8eb4c1, 0xff92b8c5, 0xff91b7c4, 0xff93b9c6, 0xff99bfca, 0xff93b9c4, 0xff91b7c2, 0xff9bc1cc, 0xffa4c9d2, 0xffa0c5ce, 0xff94bac3, 0xff92bac2, 0xff99c4cb, 0xff92bdc4, 0xff87b4ba, 0xff8bb8be, 0xff89b4bb, 0xff95c0c7, 0xff79a1a9, 0xff5e848d, 0xff345962, 0xff82a4ae, 0xff85a3ae, 0xff82a0ab, 0xff7997a2, 0xff7b99a4, 0xff809ea9, 0xff85a5b0, 0xff87a7b2, 0xff88aab4, 0xff8cb1ba, 0xff94bac3, 0xff9dc3cc, 0xff99c1c9, 0xff94bfc6, 0xff90bdc3, 0xff90bdc3, 0xff91c0c6, 0xff95c4ca, 0xff97c6cc, 0xff9bc7d0, 0xff9ec9d2, 0xff9cc7d0, 0xff9ac5ce, 0xff99c3cf, 0xff96c0cc, 0xff8db7c3, 0xff83adb9, 0xff7ca6b4, 0xff8ab4c2, 0xff7da7b5, 0xff87b1bf, 0xff95bfcf, 0xff85afbf, 0xff84aebe, 0xff93b6c9, 0xffa5b9d2, 0xffa9b7d2, 0xffaab8d3, 0xffa9b9d3, 0xffaab8d3, 0xffa9b9d3, 0xffa8b8d2, 0xffa7b8d2, 0xffa6b7d1, 0xffa6b7d1, 0xffa3b7d0, 0xffa3b7d0, 0xffa3b7d0, 0xffa2b6cf, 0xffa2b6cf, 0xffa2b6cf, 
    0xff7685ac, 0xff97a6cd, 0xff9aacd2, 0xff95a7cd, 0xff8ea0c4, 0xff8b9dc1, 0xff8a9dbe, 0xff7083a4, 0xff516484, 0xff4c5f7d, 0xff4b5c7a, 0xff4e5f7b, 0xff53637d, 0xff596983, 0xff70809a, 0xff8aa0b8, 0xff81a2b5, 0xff698ea0, 0xff7da2b4, 0xff8fb4c6, 0xffa9cede, 0xff8db2c2, 0xff779cac, 0xff759aaa, 0xff81a7b4, 0xff83a9b6, 0xff82a8b5, 0xff8ab0bd, 0xff95bbc6, 0xff90b6c1, 0xff8cb2bd, 0xff97bdc8, 0xffa7ccd5, 0xff9bc1ca, 0xff90b8c0, 0xff94bcc4, 0xff9ac5cc, 0xff94bfc6, 0xff8bb8be, 0xff8bb8be, 0xff8ab5bc, 0xff8bb6bd, 0xff85abb4, 0xff7aa0a9, 0xff789aa4, 0xff91b3bd, 0xff80a0ab, 0xff82a2ad, 0xff86a6b1, 0xff85a7b1, 0xff88aab4, 0xff8caeb8, 0xff8dafb9, 0xff8aafb8, 0xff8eb3bc, 0xff93b9c2, 0xff9cc4cc, 0xff99c1c9, 0xff92bdc4, 0xff8eb9c0, 0xff8eb9c0, 0xff90bdc3, 0xff95c2c8, 0xff98c5cb, 0xff9dc8d1, 0xff9fcad3, 0xff9fcad3, 0xff9cc7d0, 0xff99c3cf, 0xff95bfcb, 0xff8bb5c1, 0xff80aab6, 0xff7fa9b7, 0xff89b3c1, 0xff7da7b5, 0xff89b3c1, 0xff92bccc, 0xff83adbd, 0xff86b0c0, 0xff92b5c8, 0xffa1b9d1, 0xffa6b7d1, 0xffa7b8d2, 0xffa8b9d3, 0xffa8b9d3, 0xffa7b8d2, 0xffa6b7d1, 0xffa6b7d1, 0xffa5b6d0, 0xffa5b6d0, 0xffa3b7d0, 0xffa3b7d0, 0xffa3b7d0, 0xffa3b7d0, 0xffa3b7d0, 0xffa3b7d0, 
    0xff7685ac, 0xff96a5cc, 0xff9baad1, 0xff96a5cc, 0xff8f9fc3, 0xff8c9cc0, 0xff8b9bbd, 0xff7282a4, 0xff566687, 0xff4d5e7c, 0xff4a5b79, 0xff4b5c78, 0xff4d5e78, 0xff5b6c86, 0xff7687a1, 0xff869eb6, 0xff7fa0b3, 0xff618698, 0xff80a5b7, 0xff90b5c7, 0xffa4c9d9, 0xff8db2c2, 0xff7398a8, 0xff6e93a3, 0xff799fac, 0xff799fac, 0xff799fac, 0xff85abb8, 0xff93b9c4, 0xff90b6c1, 0xff8db3be, 0xff99bfca, 0xffa8ced9, 0xff94bac5, 0xff8cb4be, 0xff97bfc9, 0xff9cc7d0, 0xff98c3cc, 0xff90bbc4, 0xff89b4bd, 0xff87b2bb, 0xff85b0b9, 0xff91b7c2, 0xff7ba1ac, 0xff9cbeca, 0xff9bbdc9, 0xff89a9b6, 0xff7a9aa7, 0xff7ca0ac, 0xff7ca0ac, 0xff80a4b0, 0xff87abb7, 0xff8aaeba, 0xff8ab0bb, 0xff8db3be, 0xff93b9c4, 0xff9ac2cc, 0xff97bfc9, 0xff93bbc5, 0xff90b8c2, 0xff91b9c3, 0xff92bdc6, 0xff98c3cc, 0xff9bc6cf, 0xff9ec9d2, 0xffa2cdd6, 0xffa2cdd6, 0xff9ec9d2, 0xff99c3cf, 0xff93bdc9, 0xff88b2be, 0xff7da7b3, 0xff80aab8, 0xff86b0be, 0xff7ea8b6, 0xff89b3c1, 0xff8bb5c5, 0xff7ea8b8, 0xff87b1c1, 0xff91b6c8, 0xff9db7ce, 0xffa1b5d0, 0xffa2b6d1, 0xffa2b6d1, 0xffa2b6d1, 0xffa2b6d1, 0xffa1b5d0, 0xffa1b5d0, 0xffa6b7d3, 0xffa6b7d3, 0xffa6b7d3, 0xffa6b7d3, 0xffa6b7d3, 0xffa6b7d3, 0xffa6b7d3, 0xffa6b7d3, 
    0xff7584ab, 0xff96a5cc, 0xff9cabd2, 0xff97a6cd, 0xff90a0c4, 0xff8c9cc0, 0xff8a9abc, 0xff6f7fa1, 0xff566687, 0xff4d5e7c, 0xff50617f, 0xff546581, 0xff566781, 0xff657992, 0xff7b8fa8, 0xff7e96ae, 0xff7a9bae, 0xff5d8294, 0xff86abbd, 0xff8eb3c5, 0xff9ec3d3, 0xff91b6c6, 0xff779cac, 0xff6d92a2, 0xff749aa7, 0xff7399a6, 0xff759ba8, 0xff84aab7, 0xff94bac5, 0xff91b7c2, 0xff91b7c2, 0xffa1c7d2, 0xffa3c9d4, 0xff8db3be, 0xff8bb3bd, 0xff9bc3cd, 0xff9cc7d0, 0xff98c3cc, 0xff92bdc6, 0xff8ab5be, 0xff90bbc4, 0xff91b9c3, 0xff89afba, 0xff4a707b, 0xff6d919d, 0xff92b4c0, 0xff9cbcc9, 0xff84a6b2, 0xff82a8b3, 0xff80a8b2, 0xff84acb6, 0xff8bb3bd, 0xff8fb7c1, 0xff8fb7c1, 0xff92b8c3, 0xff95bdc7, 0xff9ac2cc, 0xff97bfc9, 0xff94bcc6, 0xff92bac4, 0xff93bbc5, 0xff97bfc9, 0xff9dc3ce, 0xff9fc7d1, 0xff9cc7d0, 0xffa2cdd6, 0xffa4cfd8, 0xffa0cbd4, 0xff9ac4d0, 0xff92bcc8, 0xff86b0bc, 0xff7ca6b2, 0xff7da7b5, 0xff81abb9, 0xff7da7b5, 0xff86b0be, 0xff80aaba, 0xff759faf, 0xff86b0c0, 0xff90b5c8, 0xff9db9d1, 0xffa1b6d1, 0xffa2b7d2, 0xffa2b7d2, 0xffa3b7d2, 0xffa3b7d2, 0xffa2b6d1, 0xffa2b6d1, 0xffa4b5d1, 0xffa4b5d1, 0xffa5b6d2, 0xffa5b6d2, 0xffa5b6d2, 0xffa7b6d3, 0xffa7b6d3, 0xffa7b6d3, 
    0xff7385ab, 0xff95a7cd, 0xff9dacd3, 0xff99a8cf, 0xff92a0c5, 0xff8c9abf, 0xff8997ba, 0xff6d7b9e, 0xff576687, 0xff4b5a79, 0xff4e5f7d, 0xff546581, 0xff556982, 0xff6a8098, 0xff7e94ac, 0xff7892a9, 0xff7295a8, 0xff598091, 0xff8ab1c2, 0xff8ab1c2, 0xff94bbca, 0xff93bac9, 0xff7aa1b0, 0xff6d94a3, 0xff7098a4, 0xff7098a4, 0xff739ba7, 0xff83abb7, 0xff93bbc5, 0xff90b8c2, 0xff94bcc6, 0xffa9d1db, 0xff99c1cb, 0xff86aeb8, 0xff8fb7c1, 0xffa1c9d3, 0xff9cc4ce, 0xff94bcc6, 0xff95bdc7, 0xff90b8c2, 0xff8eb6c0, 0xff91b9c3, 0xff769ca7, 0xff3a606b, 0xff3e626e, 0xff81a5b1, 0xff96b8c4, 0xff8aaeba, 0xff83abb5, 0xff7faab3, 0xff81acb5, 0xff87b2bb, 0xff89b4bd, 0xff88b3bc, 0xff89b1bb, 0xff8bb3bd, 0xff98c0ca, 0xff96bec8, 0xff95bbc6, 0xff94bac5, 0xff95bbc6, 0xff97bdc8, 0xff9cc0cc, 0xff9dc3ce, 0xff9ac2cc, 0xff9fcad3, 0xffa4cfd8, 0xffa1ccd5, 0xff9bc5d1, 0xff93bdc9, 0xff88b2be, 0xff7ea8b4, 0xff7ba5b3, 0xff7ea8b6, 0xff7da7b5, 0xff84aebc, 0xff76a0b0, 0xff6c96a6, 0xff85afbf, 0xff91b6c9, 0xff9ab6ce, 0xff9cb4d0, 0xff9db5d1, 0xff9db5d1, 0xff9fb4d1, 0xff9fb4d1, 0xff9fb2d0, 0xff9fb2d0, 0xff9daecc, 0xff9daecc, 0xff9eadcc, 0xff9faecd, 0xff9faecd, 0xffa1aece, 0xffa1aece, 0xffa1aece, 
    0xff7385ab, 0xff95a7cd, 0xff9eadd4, 0xff9aa9d0, 0xff92a0c5, 0xff8c9abf, 0xff8795b8, 0xff6b799c, 0xff586788, 0xff475675, 0xff455674, 0xff485c77, 0xff4b6179, 0xff667e96, 0xff7e96ae, 0xff7692a8, 0xff6c91a3, 0xff577e8f, 0xff8cb3c4, 0xff86adbe, 0xff8eb5c4, 0xff94bbca, 0xff7ea5b4, 0xff6e95a4, 0xff6f97a3, 0xff6f97a3, 0xff729aa6, 0xff83abb7, 0xff92bac4, 0xff90b8c2, 0xff97bfc9, 0xffaed6e0, 0xff91b9c3, 0xff83abb5, 0xff93bbc5, 0xffa6ced8, 0xff99c1cb, 0xff8fb7c1, 0xff95bdc7, 0xff94bcc6, 0xff8cb4be, 0xff9ac0cb, 0xff8ab0bb, 0xff7ba1ac, 0xff5e828e, 0xff9abeca, 0xff96b8c4, 0xff93b9c4, 0xff81acb5, 0xff7cabb3, 0xff81adb6, 0xff88b4bd, 0xff8db9c2, 0xff8eb9c2, 0xff8eb9c2, 0xff90bbc4, 0xff96bec8, 0xff94bcc6, 0xff94bac5, 0xff92b8c3, 0xff93b7c3, 0xff95b9c5, 0xff97bbc7, 0xff97bdc8, 0xff96bec8, 0xff9dc8d1, 0xffa2cdd6, 0xffa1ccd5, 0xff9cc6d2, 0xff95bfcb, 0xff8ab4c0, 0xff81abb7, 0xff7ca6b4, 0xff7fa9b7, 0xff7ea8b6, 0xff84aebc, 0xff729cac, 0xff6993a3, 0xff86b0c0, 0xff94b9cc, 0xff8fabc3, 0xff92aac6, 0xff92aac6, 0xff93abc7, 0xff95aac7, 0xff94a9c6, 0xff95a8c6, 0xff94a7c5, 0xff95a6c4, 0xff95a6c4, 0xff96a5c4, 0xff97a6c5, 0xff98a5c5, 0xff99a6c6, 0xff99a6c6, 0xff9aa7c7, 
    0xff6f81a7, 0xff98aad0, 0xff99a8cf, 0xff8f9ec5, 0xff909ec3, 0xff909cc2, 0xff8794b7, 0xff6a779a, 0xff525f81, 0xff4c5b7a, 0xff485977, 0xff4e627d, 0xff5d738b, 0xff6c849c, 0xff758fa6, 0xff7695aa, 0xff6c91a3, 0xff578191, 0xff81abbb, 0xff91bbcb, 0xff8ab4c2, 0xff95bfcd, 0xff82acba, 0xff6c96a4, 0xff719ba7, 0xff6a94a0, 0xff638d99, 0xff7ea8b4, 0xff83aeb7, 0xff89b4bd, 0xffa6d1da, 0xff92bdc6, 0xff7ba5b1, 0xff8db7c3, 0xff9dc7d3, 0xff9ec8d4, 0xff98c0cc, 0xff93bbc7, 0xff92b8c5, 0xff92b8c5, 0xff8cb2bf, 0xff9cc0ce, 0xff8fb3c1, 0xffadd1df, 0xffb4d8e6, 0xff8db1bf, 0xff8cb0be, 0xff9bc1ce, 0xff81adb8, 0xff7fafb9, 0xff83b2bc, 0xff85b4be, 0xff87b6c0, 0xff89b5c0, 0xff8ab4c0, 0xff89b3bf, 0xff92bac6, 0xff94bcc8, 0xff94bac7, 0xff92b8c5, 0xff94b8c6, 0xff98bcca, 0xff99bac9, 0xff93b7c5, 0xff9ac2cc, 0xff99c4cd, 0xff9ac5ce, 0xff9ac5ce, 0xff9cc6d2, 0xff99c3cf, 0xff8bb5c1, 0xff7ca6b2, 0xff7fa9b7, 0xff82acba, 0xff7ea8b6, 0xff80aab8, 0xff729cac, 0xff6f99a9, 0xff88b2c2, 0xff8fb4c7, 0xff92aec6, 0xff99aec9, 0xff9bb0cb, 0xff99aec9, 0xff98acc7, 0xff98acc7, 0xff9daeca, 0xffa2b3cf, 0xffa0afcc, 0xffa0afcc, 0xffa1afcc, 0xffa2b0cd, 0xffa5b0ce, 0xffa6b1cf, 0xffa6b1cf, 0xffa6b1cf, 
    0xff4f6187, 0xff7284aa, 0xff7180a7, 0xff6c7aa1, 0xff7884aa, 0xff838fb5, 0xff848eb2, 0xff6c769a, 0xff566083, 0xff4f5c7c, 0xff4c5b7a, 0xff526681, 0xff627890, 0xff708aa1, 0xff7995ab, 0xff7999ae, 0xff668d9e, 0xff5c8696, 0xff84aebe, 0xff8bb5c5, 0xff84aebc, 0xff99c3d1, 0xff8cb6c4, 0xff6f99a7, 0xff6c96a2, 0xff6a94a0, 0xff6c96a2, 0xff79a3af, 0xff7faab3, 0xff91bcc5, 0xffa0cbd4, 0xff89b4bd, 0xff81abb7, 0xff91bbc7, 0xff9ec8d4, 0xff9dc7d3, 0xff96beca, 0xff92bac6, 0xff92b8c5, 0xff92b8c5, 0xff94b8c6, 0xff95b9c7, 0xff8fb3c1, 0xff7094a2, 0xff8cb0be, 0xff8aaebc, 0xff8fb3c1, 0xff93bbc7, 0xff7faeb8, 0xff7eaeb8, 0xff7fafb9, 0xff81b1bb, 0xff84b3bd, 0xff85b4be, 0xff87b3be, 0xff88b2be, 0xff8fb7c3, 0xff91b9c5, 0xff92b8c5, 0xff90b6c3, 0xff92b6c4, 0xff96bac8, 0xff95b9c7, 0xff91b5c3, 0xff99c1cb, 0xff98c3cc, 0xff99c4cd, 0xff9ac5ce, 0xff9cc6d2, 0xff99c3cf, 0xff8cb6c2, 0xff7ea8b4, 0xff7ca6b4, 0xff7ca6b4, 0xff749eac, 0xff76a0ae, 0xff6b95a5, 0xff6d97a7, 0xff89b3c3, 0xff8fb4c6, 0xff98b0c8, 0xff9eb2cd, 0xffa3b7d2, 0xffa5b9d4, 0xffa8b9d5, 0xffa7b8d4, 0xffa8b7d4, 0xffa8b7d4, 0xffaab9d6, 0xffabb9d6, 0xffaab8d5, 0xffabb6d4, 0xffabb6d4, 0xffaab5d3, 0xffaab3d2, 0xffaab3d2, 
    0xff3c4e74, 0xff596b91, 0xff516087, 0xff4d5b82, 0xff616d93, 0xff767fa6, 0xff7e86ab, 0xff6b7599, 0xff535d80, 0xff4c5979, 0xff4a5978, 0xff516580, 0xff607890, 0xff708aa1, 0xff7594a9, 0xff7598ac, 0xff608798, 0xff5d8797, 0xff7ba5b5, 0xff759faf, 0xff66909e, 0xff81abb9, 0xff86b0be, 0xff6d97a5, 0xff658f9b, 0xff66909c, 0xff76a0ac, 0xff77a1ad, 0xff81acb5, 0xff9ac5ce, 0xff95c0c9, 0xff81acb5, 0xff89b5c0, 0xff96c2cd, 0xff9fc9d5, 0xff9bc5d1, 0xff94bcc8, 0xff91b7c4, 0xff93b7c5, 0xff94b8c6, 0xff9dbecd, 0xff84a5b4, 0xff7094a2, 0xff1f4351, 0xff5a808d, 0xff88aebb, 0xff95bbc8, 0xff8cb4c0, 0xff80afb9, 0xff7dadb7, 0xff7babb5, 0xff7cacb6, 0xff80afb9, 0xff83b2bc, 0xff85b1bc, 0xff85b1bc, 0xff8ab4c0, 0xff8eb8c4, 0xff91b9c5, 0xff8eb6c2, 0xff90b6c3, 0xff93b9c6, 0xff93b9c6, 0xff90b6c3, 0xff97bfc9, 0xff97c2cb, 0xff98c3cc, 0xff99c4cd, 0xff9bc5d1, 0xff99c3cf, 0xff8eb8c4, 0xff81abb7, 0xff7ca6b4, 0xff78a2b0, 0xff6c96a4, 0xff6d97a5, 0xff6791a1, 0xff6e98a8, 0xff8cb6c6, 0xff92b5c8, 0xff9fb5cd, 0xffa5b5cf, 0xffa7b7d1, 0xffaabad4, 0xffacbcd6, 0xffacbcd6, 0xffacbad5, 0xffabb9d4, 0xffaab8d3, 0xffacb7d3, 0xffacb7d3, 0xffacb5d2, 0xffabb4d1, 0xffaab3d0, 0xffaab3d0, 0xffaab3d0, 
    0xff44567c, 0xff5e7096, 0xff55638a, 0xff4d5981, 0xff5d668d, 0xff6d769d, 0xff7880a5, 0xff6b7398, 0xff505a7d, 0xff495676, 0xff485776, 0xff516580, 0xff617991, 0xff708ca2, 0xff7494a9, 0xff7295a9, 0xff62899a, 0xff5e8898, 0xff719bab, 0xff648e9e, 0xff47717f, 0xff598391, 0xff739dab, 0xff6d97a5, 0xff628c98, 0xff5e8894, 0xff7ba5b1, 0xff80aab6, 0xff8cb7c0, 0xff9ec9d2, 0xff87b2bb, 0xff80acb5, 0xff92bec9, 0xff9bc7d2, 0xff9fc9d5, 0xff98c2ce, 0xff90b8c4, 0xff8fb5c2, 0xff92b6c4, 0xff94b8c6, 0xff97b8c7, 0xff8eafbe, 0xff7a9baa, 0xff345866, 0xff618794, 0xff86acb9, 0xff96bcc9, 0xff90b8c4, 0xff82b1bb, 0xff7dadb7, 0xff79a9b3, 0xff79a9b3, 0xff7eadb7, 0xff82b1bb, 0xff85b1bc, 0xff85b1bc, 0xff89b3bf, 0xff8eb8c4, 0xff92bac6, 0xff90b8c4, 0xff90b8c4, 0xff92bac6, 0xff93b9c6, 0xff90b8c4, 0xff92bdc6, 0xff95c0c9, 0xff96c1ca, 0xff97c2cb, 0xff99c3cf, 0xff98c2ce, 0xff8fb9c5, 0xff84aeba, 0xff7ea8b6, 0xff79a3b1, 0xff6b95a3, 0xff6b95a3, 0xff6892a2, 0xff739dad, 0xff90baca, 0xff94b5c8, 0xffa3b5cd, 0xffa8b4ce, 0xffa8b3cf, 0xffa7b3cd, 0xffa7b2ce, 0xffa8b4ce, 0xffabb4d1, 0xffadb7d2, 0xffaab3d0, 0xffaab4cf, 0xffabb4d1, 0xffabb5d0, 0xffacb5d2, 0xffadb7d2, 0xffadb6d3, 0xffaeb8d3, 
    0xff495b81, 0xff687aa0, 0xff637198, 0xff5b678f, 0xff626b92, 0xff6b739a, 0xff777ca2, 0xff6c7499, 0xff525c7f, 0xff4b5878, 0xff4a5b79, 0xff546984, 0xff668097, 0xff7392a7, 0xff7497ab, 0xff7196a9, 0xff628c9c, 0xff608b9b, 0xff739eae, 0xff6f9aaa, 0xff44707d, 0xff416d7a, 0xff6894a1, 0xff74a0ad, 0xff618d98, 0xff5a8691, 0xff7aa6b1, 0xff8cb8c3, 0xff9ac6cf, 0xff98c4cd, 0xff7ca8b1, 0xff89b5be, 0xff97c3ce, 0xff9ac8d5, 0xff9cc8d5, 0xff93bdcb, 0xff8cb3c2, 0xff8db2c2, 0xff91b5c5, 0xff96b7c8, 0xff8baabc, 0xffaacbdc, 0xff9fc0d1, 0xff91b5c5, 0xff92b7c7, 0xff86abbb, 0xff90b7c6, 0xff95bfcd, 0xff86b2bf, 0xff7eacb9, 0xff79a7b4, 0xff79a7b4, 0xff7fabb8, 0xff84b0bd, 0xff85b1be, 0xff84b0bd, 0xff87b1bf, 0xff8eb8c6, 0xff91bbc9, 0xff90bac8, 0xff8fb9c7, 0xff90bac8, 0xff93bac9, 0xff8fb9c5, 0xff90bbc4, 0xff93bec7, 0xff94bfc8, 0xff94bfc8, 0xff96c0cc, 0xff96c0cc, 0xff8fb9c5, 0xff86b0bc, 0xff7ca6b4, 0xff7aa4b2, 0xff6c96a4, 0xff6c96a4, 0xff6b95a5, 0xff76a0b0, 0xff91bbcb, 0xff90b1c4, 0xffa2b0ca, 0xffaab0ca, 0xffabb2ce, 0xffabb2cc, 0xffabb2ce, 0xffacb3cd, 0xffafb6d2, 0xffb2b9d3, 0xffb1b8d4, 0xffb2b9d3, 0xffb2b9d5, 0xffb3bad4, 0xffb2bcd7, 0xffb2bcd5, 0xffb3bdd8, 0xffb3bdd6, 
    0xff415379, 0xff65779d, 0xff69779e, 0xff66729a, 0xff6a739a, 0xff6d759c, 0xff767ba1, 0xff6d7298, 0xff50587c, 0xff495676, 0xff485977, 0xff536883, 0xff657f96, 0xff7190a5, 0xff7093a7, 0xff6b91a4, 0xff578191, 0xff5d8898, 0xff76a1b1, 0xff7faaba, 0xff517d8a, 0xff3f6b78, 0xff6793a0, 0xff74a0ad, 0xff64909b, 0xff628e99, 0xff78a4af, 0xff92bec9, 0xff9fcbd4, 0xff8cb8c1, 0xff7ba7b0, 0xff92bec7, 0xff95c3d0, 0xff99c7d4, 0xff98c4d1, 0xff8fb9c7, 0xff89b0bf, 0xff8bb0c0, 0xff93b4c5, 0xff95b6c7, 0xff8cabbd, 0xff9fbed0, 0xff80a1b2, 0xff90b4c4, 0xff9cc0d0, 0xff8cb1c1, 0xff90b7c6, 0xff8eb8c6, 0xff85b1be, 0xff7dabb8, 0xff78a6b3, 0xff78a6b3, 0xff7fabb8, 0xff83afbc, 0xff83afbc, 0xff81adba, 0xff83adbb, 0xff8bb5c3, 0xff90bac8, 0xff8eb8c6, 0xff8cb6c4, 0xff8eb8c6, 0xff8fb9c7, 0xff8eb8c4, 0xff8eb8c4, 0xff91bcc5, 0xff92bdc6, 0xff91bcc5, 0xff92bcc8, 0xff92bcc8, 0xff8db7c3, 0xff86b0bc, 0xff76a0ae, 0xff77a1af, 0xff6c96a4, 0xff6b95a3, 0xff6993a3, 0xff76a0b0, 0xff90baca, 0xff8faec2, 0xffa1adc5, 0xffa4a6bf, 0xffa0a4bf, 0xffa3a7c0, 0xffaaaec9, 0xffb0b4cd, 0xffb1b5d0, 0xffaeb4cc, 0xffaeb4ce, 0xffadb5cc, 0xffacb3cd, 0xffacb4cb, 0xffa9b3cc, 0xffa8b3c9, 0xffa8b2cb, 0xffa7b2c8, 
    0xff415379, 0xff65779d, 0xff6b79a0, 0xff6e7aa2, 0xff757ea5, 0xff747ca3, 0xff777ca2, 0xff6a6f95, 0xff4d5579, 0xff475272, 0xff455674, 0xff516681, 0xff627e94, 0xff6d8da2, 0xff6b8ea2, 0xff648a9d, 0xff4f7989, 0xff5b8696, 0xff709bab, 0xff7aa5b5, 0xff517d8a, 0xff436f7c, 0xff6b97a4, 0xff6a96a3, 0xff67939e, 0xff76a2ad, 0xff78a4af, 0xff8db9c4, 0xff96c2cb, 0xff7fabb4, 0xff80acb5, 0xff95c1ca, 0xff92c0cd, 0xff95c3d0, 0xff94c0cd, 0xff8bb5c3, 0xff86adbc, 0xff89aebe, 0xff92b3c4, 0xff94b5c6, 0xff93b2c4, 0xff88a7b9, 0xff658697, 0xff557989, 0xff82a6b6, 0xff8fb4c4, 0xff92b9c8, 0xff87b1bf, 0xff83afbc, 0xff7da9b6, 0xff77a3b0, 0xff79a5b2, 0xff7eaab7, 0xff82aebb, 0xff80acb9, 0xff7da9b6, 0xff7ba7b4, 0xff84b0bd, 0xff8ab6c3, 0xff89b5c2, 0xff86b2bf, 0xff87b3c0, 0xff89b5c2, 0xff88b4bf, 0xff8db7c3, 0xff90bbc4, 0xff91bcc5, 0xff8fbac3, 0xff8eb8c4, 0xff8fb9c5, 0xff8ab4c0, 0xff84aeba, 0xff719ba9, 0xff759fad, 0xff6b95a3, 0xff6993a1, 0xff6791a1, 0xff76a0b0, 0xff93bdcd, 0xff93b2c4, 0xffa3aec4, 0xff9292aa, 0xff7b7b95, 0xff797991, 0xff898ba4, 0xff9b9db4, 0xff9fa1ba, 0xff999eb4, 0xff969ab3, 0xff959bb1, 0xff939bb2, 0xff939bb0, 0xff929ab1, 0xff9199ae, 0xff8f9ab0, 0xff8e99ad, 
    0xff555c79, 0xff757c99, 0xff787d9b, 0xff7f809f, 0xff8786a6, 0xff8582a1, 0xff817c9c, 0xff6e6b8a, 0xff575675, 0xff4f5370, 0xff4d5772, 0xff576981, 0xff678096, 0xff7190a4, 0xff6d90a3, 0xff648b9c, 0xff537d8d, 0xff628c9c, 0xff6d97a7, 0xff6e98a8, 0xff4a7482, 0xff477380, 0xff719daa, 0xff65919e, 0xff6995a0, 0xff86b2bd, 0xff79a5b0, 0xff85b1bc, 0xff8bb7c2, 0xff77a3ac, 0xff86b2bd, 0xff95c1cc, 0xff92bec9, 0xff95c1cc, 0xff91bdca, 0xff88b4c1, 0xff82aebb, 0xff86b0be, 0xff8db4c3, 0xff92b6c6, 0xff92b2c1, 0xff92afbf, 0xff96b3c3, 0xff537080, 0xff7a99ab, 0xff8cb0c0, 0xff91b6c8, 0xff8cb3c4, 0xff82acba, 0xff7ca6b4, 0xff77a1af, 0xff78a2b0, 0xff7ea8b6, 0xff84abba, 0xff81a8b7, 0xff7da4b3, 0xff79a0af, 0xff80aaba, 0xff87b1c1, 0xff86b0c0, 0xff82adbd, 0xff83aebe, 0xff84afbf, 0xff83aebe, 0xff8eb5c4, 0xff91b9c5, 0xff92bac6, 0xff8fb7c3, 0xff8cb6c4, 0xff8cb6c4, 0xff89b3c1, 0xff83adbb, 0xff6f9ba8, 0xff749faf, 0xff6b96a6, 0xff6892a2, 0xff668fa1, 0xff77a0b2, 0xff98c1d3, 0xff9ab9cd, 0xffa5b2c5, 0xff808196, 0xff535167, 0xff48445d, 0xff625c76, 0xff7e7a93, 0xff87859d, 0xff82829a, 0xff7e8399, 0xff7d859a, 0xff7b869a, 0xff7c879b, 0xff80879a, 0xff81869a, 0xff85869b, 0xff85869b, 
    0xff776b79, 0xff827483, 0xff8b7a8a, 0xff8b7687, 0xff886f82, 0xff83687b, 0xff765b6e, 0xff664d62, 0xff5e4b5f, 0xff665a6e, 0xff6a697b, 0xff6b7485, 0xff718495, 0xff7794a4, 0xff6d919f, 0xff5d8493, 0xff537888, 0xff6d92a2, 0xff7da4b3, 0xff6e95a4, 0xff4f7685, 0xff436d7b, 0xff5a8693, 0xff78a4b1, 0xff5b8996, 0xff76a4b1, 0xff7aa8b5, 0xff81afbc, 0xff82b0bd, 0xff78a7b1, 0xff84b2bf, 0xff90bac6, 0xff95b9c5, 0xff98bcc8, 0xff83adb9, 0xff7aaab6, 0xff87bbc8, 0xff7db1be, 0xff5f8f9b, 0xff547e8a, 0xff7a9ca8, 0xff7892a1, 0xffbfd5e3, 0xffb9ccdd, 0xff99b0c2, 0xffa2bdd2, 0xff97b7cc, 0xff9ec1d5, 0xff93b8c8, 0xff779fab, 0xff799eae, 0xff87acbc, 0xff7fa3b3, 0xff7d9eaf, 0xff86a7ba, 0xff86a7ba, 0xff81a2b5, 0xff85a8bc, 0xff8aafc2, 0xff7ca2b5, 0xff8ab3c7, 0xff81acbf, 0xff8dbacf, 0xff80abbe, 0xff8cb1c4, 0xff8cafc2, 0xff8cafc2, 0xff8db2c4, 0xff8db4c5, 0xff87b1c1, 0xff83adbd, 0xff80abbb, 0xff74a2b1, 0xff6c9aaa, 0xff6997a7, 0xff6994a5, 0xff6b96a9, 0xff7ea9bc, 0xff8fb9cf, 0xff94b7cb, 0xff9fb2c1, 0xff8a91a1, 0xff545267, 0xff483e57, 0xff776784, 0xff8e7e9b, 0xff8b809e, 0xff9894af, 0xff9298b0, 0xff8996a9, 0xff8899ab, 0xff919eaf, 0xff9499ac, 0xff938ea2, 0xff9e8ca2, 0xffa993aa, 
    0xff745461, 0xff785664, 0xff765260, 0xff6f4656, 0xff683d4e, 0xff673b4c, 0xff64374b, 0xff5e3448, 0xff614053, 0xff5e475b, 0xff605365, 0xff676978, 0xff768393, 0xff7e95a3, 0xff70909d, 0xff5c808e, 0xff537787, 0xff6b8f9f, 0xff7ba0b0, 0xff6e93a3, 0xff4c7382, 0xff3f6675, 0xff557f8d, 0xff74a0ad, 0xff5e8a97, 0xff72a0ad, 0xff709eab, 0xff79a7b4, 0xff80aebb, 0xff7ba9b6, 0xff85b3c0, 0xff8eb6c2, 0xff97b5c0, 0xff9bb9c4, 0xff92b8c5, 0xff8bb9c6, 0xff89bdca, 0xff74aab6, 0xff50828d, 0xff39636f, 0xff5f7f8c, 0xff657c8a, 0xffa5b7c5, 0xffbdcddd, 0xffa7b9cd, 0xff95aec2, 0xff83a2b7, 0xff81a4b8, 0xff7ea2b2, 0xff7499a9, 0xff7397a7, 0xff7599a9, 0xff7c9dae, 0xff86a7b8, 0xff8aa9bd, 0xff809fb3, 0xff7fa0b3, 0xff84a4b9, 0xff89acc0, 0xff799fb2, 0xff80a9bd, 0xff79a4b7, 0xff8cb6cc, 0xff88b1c5, 0xff8cafc3, 0xff8fb0c3, 0xff8db0c3, 0xff8bb0c2, 0xff87aebf, 0xff80aaba, 0xff7da8b8, 0xff7caab9, 0xff73a3b1, 0xff6b9ba9, 0xff6a98a8, 0xff6896a6, 0xff6c97aa, 0xff7faabd, 0xff90bad0, 0xff93b8cb, 0xff9ab1bf, 0xff8f9ba9, 0xff67687c, 0xff60566f, 0xff867391, 0xff9683a3, 0xff9487a5, 0xffa29eb9, 0xffa6adc7, 0xff98a9bd, 0xff92a7b8, 0xff98a8b8, 0xff9fa2b3, 0xff9990a3, 0xff90768f, 0xff876681, 
    0xff562441, 0xff5b2644, 0xff5b2441, 0xff561e3b, 0xff571c3a, 0xff5f2442, 0xff6a324f, 0xff6f3a58, 0xff603350, 0xff54324b, 0xff503d53, 0xff625a6f, 0xff7b8093, 0xff8394a4, 0xff718b9a, 0xff5a7a89, 0xff567788, 0xff698d9d, 0xff789dad, 0xff6c91a1, 0xff49707f, 0xff375e6d, 0xff4b7583, 0xff6d99a6, 0xff6d99a6, 0xff7da9b6, 0xff739fac, 0xff78a4b1, 0xff83afbc, 0xff84b0bd, 0xff8ab8c5, 0xff8cb4c0, 0xff8ba5b4, 0xff8aa1b1, 0xff89a9b8, 0xff8aafbf, 0xff85b3c2, 0xff86b6c4, 0xff7eacb9, 0xff7199a5, 0xff7996a4, 0xff79909e, 0xff7c8f9d, 0xff98a9b9, 0xff9bb0c1, 0xff9cb6c7, 0xff99b8cc, 0xff80a3b6, 0xff335669, 0xff325568, 0xff2c5163, 0xff2d5264, 0xff3c5f72, 0xff4d7083, 0xff65889b, 0xff80a3b6, 0xff89acbf, 0xff85a8bb, 0xff88adbf, 0xff7da2b4, 0xff86adbe, 0xff86adbe, 0xff8db4c5, 0xff8bb0c2, 0xff89adbd, 0xff8eafbe, 0xff8cb0bc, 0xff87adb8, 0xff81a9b3, 0xff7ca7b0, 0xff7ca8b3, 0xff7baab4, 0xff73a3ad, 0xff6b9ba5, 0xff6a98a5, 0xff6a98a5, 0xff6e99a9, 0xff80abbb, 0xff90bbcc, 0xff93b8c8, 0xff9bb7c3, 0xff99a9b6, 0xff7f8497, 0xff747087, 0xff837692, 0xff807190, 0xff766d8c, 0xff80809c, 0xff707c96, 0xff74869c, 0xff7c91a4, 0xff899aac, 0xff9499ad, 0xff9389a1, 0xff816a86, 0xff6e506c, 
    0xff5c214d, 0xff60264f, 0xff632650, 0xff5f214a, 0xff591b44, 0xff5c1e47, 0xff642750, 0xff683158, 0xff54264a, 0xff462343, 0xff44304b, 0xff5b536a, 0xff787d91, 0xff8292a2, 0xff728997, 0xff5e7b89, 0xff597a89, 0xff698d9d, 0xff779cac, 0xff6a8f9f, 0xff466d7c, 0xff325968, 0xff456c7b, 0xff648e9c, 0xff7da7b5, 0xff8cb8c5, 0xff7ca8b5, 0xff77a3b0, 0xff7fabb8, 0xff82aebb, 0xff8bb7c4, 0xff8ab0bd, 0xff9cb1c2, 0xff92a5b6, 0xff8fa9ba, 0xff89aabb, 0xff7da4b3, 0xff88b4c1, 0xff97c1cf, 0xff90b6c3, 0xff8daab8, 0xff8da4b2, 0xff455867, 0xff516473, 0xff748b99, 0xff9db9c7, 0xffaacad9, 0xff7195a5, 0xff1a3b4e, 0xff123247, 0xff05283c, 0xff092c3f, 0xff0e3345, 0xff123749, 0xff496e80, 0xffa0c5d5, 0xff8eb3c3, 0xff7da3b0, 0xff7da1af, 0xff7a9eac, 0xff89aab9, 0xff8eb0bc, 0xff84a6b2, 0xff81a3af, 0xff8badb7, 0xff8bb0b9, 0xff88aeb7, 0xff83acb2, 0xff7faab1, 0xff7facb2, 0xff7cabb1, 0xff7aa9af, 0xff71a2a9, 0xff6a9ba2, 0xff6b9aa2, 0xff6b9aa2, 0xff6f9ba6, 0xff81adb8, 0xff90bcc9, 0xff92bac6, 0xff8dadb8, 0xff95abb8, 0xff949fb1, 0xff9ea0b7, 0xffaea8c4, 0xffaba2c1, 0xffa39ebe, 0xffaaaecb, 0xffacbad4, 0xffa1b5cd, 0xff96acc1, 0xff95a5bc, 0xff9aa0b8, 0xff908aa4, 0xff6a5574, 0xff402544, 
    0xff5c255e, 0xff622960, 0xff652c63, 0xff652a60, 0xff60255b, 0xff5e2558, 0xff632d5f, 0xff683967, 0xff5c365f, 0xff53385b, 0xff524362, 0xff626078, 0xff758092, 0xff7b919f, 0xff6e8c97, 0xff5e808a, 0xff5a7e8c, 0xff6c929f, 0xff789eab, 0xff678d9a, 0xff446a77, 0xff335966, 0xff426875, 0xff59818d, 0xff7ba3af, 0xff90bac6, 0xff81abb7, 0xff76a0ac, 0xff78a2ae, 0xff7ca8b3, 0xff88b4bf, 0xff87adba, 0xff92a7ba, 0xff90a1b5, 0xff96adbf, 0xff94b1c1, 0xff83a7b5, 0xff87afbb, 0xff91b7c4, 0xff86aab8, 0xff8ba8b6, 0xff90aab7, 0xff2c4250, 0xff485e6c, 0xff79909e, 0xff9bb7c3, 0xffa3c3d0, 0xff6b8c9b, 0xff7d9cb1, 0xff718da5, 0xff57758d, 0xff48687d, 0xff36596d, 0xff23485a, 0xff497081, 0xff98bfce, 0xff8fb5c0, 0xff80a5ae, 0xff86a8b2, 0xff83a4ad, 0xff84a0ab, 0xff8da8b1, 0xff869ea8, 0xff94afb8, 0xff90b1b8, 0xff8db2b8, 0xff89b0b5, 0xff84adb1, 0xff82adb3, 0xff83b0b5, 0xff7babaf, 0xff72a2a6, 0xff6fa0a5, 0xff699a9f, 0xff6b9aa0, 0xff6c9ba1, 0xff709da3, 0xff81aeb4, 0xff91bcc3, 0xff91b9c1, 0xff88adb6, 0xff8facba, 0xffa1b4c5, 0xffbec9df, 0xffd7dbf6, 0xffdad9f8, 0xffd2d6f3, 0xffd4ddfa, 0xffa2b3cd, 0xff8399b1, 0xff627a92, 0xff63748e, 0xff7d84a0, 0xff86819f, 0xff645576, 0xff382246, 
    0xff795291, 0xff7b5493, 0xff815998, 0xff865e9c, 0xff88609e, 0xff8b639f, 0xff9370a8, 0xff9b7daf, 0xff8c75a3, 0xff877ba1, 0xff7f7e9d, 0xff768298, 0xff768d9d, 0xff73939e, 0xff618a90, 0xff4d787f, 0xff5b838d, 0xff7197a4, 0xff789eab, 0xff618794, 0xff416774, 0xff365c69, 0xff3f6572, 0xff4b717e, 0xff6c929f, 0xff8cb4c0, 0xff87afbb, 0xff7ca4b0, 0xff7ba5b1, 0xff80aab6, 0xff8cb6c2, 0xff88aebb, 0xff7e98a9, 0xff869bae, 0xff8ea8b9, 0xff8eaebd, 0xff88acb8, 0xff8db3be, 0xff92b8c5, 0xff8eb2c0, 0xff90b0bf, 0xff91acbd, 0xff2e4555, 0xff698090, 0xff96b0bd, 0xff98b4c0, 0xff9bb9c3, 0xff8daab8, 0xff7f97af, 0xff839bb7, 0xff7f99b4, 0xff7c98b0, 0xff7d9db2, 0xff80a3b6, 0xff8eb3c3, 0xffa5cbd8, 0xff7da2ab, 0xff7b9da6, 0xff8eacb4, 0xff86a1a8, 0xff6e8289, 0xff76878e, 0xff77848c, 0xffa0b3ba, 0xff8faeb3, 0xff8eb5ba, 0xff8db6bc, 0xff85b0b6, 0xff81aeb3, 0xff7eadb3, 0xff75a4aa, 0xff68979d, 0xff6c9da2, 0xff67989d, 0xff6a999f, 0xff6c9ba1, 0xff719ea4, 0xff81aeb3, 0xff90bbc1, 0xff8db8bf, 0xff90b8c2, 0xff8bafbd, 0xff9bb6c7, 0xffbccee4, 0xffd0dcf6, 0xffd1d8f5, 0xffc8d1ee, 0xffc3d1ee, 0xffc5daf5, 0xffaec6e2, 0xff8ea6c2, 0xff7689a7, 0xff6c7594, 0xff646385, 0xff55496f, 0xff443459, 
    0xff9a95d7, 0xff9a94d4, 0xffa097d8, 0xffa69ddc, 0xffa89edd, 0xffa59dd9, 0xffa9a1dc, 0xffaca9de, 0xffa3a5d5, 0xffa2aed4, 0xff95aac7, 0xff7b9aae, 0xff6f97a3, 0xff699a9f, 0xff568c8e, 0xff3d7375, 0xff59858e, 0xff749ca8, 0xff789eab, 0xff597f8c, 0xff3e6270, 0xff3b5f6d, 0xff3f6371, 0xff3e6270, 0xff597d8b, 0xff84a8b6, 0xff88acba, 0xff83a9b6, 0xff86aeba, 0xff89b3bf, 0xff8db7c3, 0xff80a8b4, 0xff86a5b7, 0xff91aebe, 0xff8cadbc, 0xff84a8b4, 0xff85adb5, 0xff86aeb6, 0xff87afb9, 0xff8db3c0, 0xff90b1c2, 0xff83a2b4, 0xff1c374a, 0xff657f90, 0xff9bb5c4, 0xff99b4bf, 0xff90adb5, 0xff9ab4c1, 0xff91a2be, 0xff91a0c1, 0xff93a6c4, 0xff93a8c3, 0xff89a3ba, 0xff82a1b5, 0xff7a9bac, 0xff6c8c99, 0xff688690, 0xff6b868f, 0xff89a0a6, 0xff86979e, 0xff606b71, 0xff61666c, 0xff52525a, 0xff7b848b, 0xff809ca7, 0xff87afb9, 0xff8fb7c3, 0xff87b1bd, 0xff7eaab5, 0xff78a6b3, 0xff719fac, 0xff6696a2, 0xff6a9aa4, 0xff65959f, 0xff6998a0, 0xff6b9aa2, 0xff709ca5, 0xff80adb3, 0xff8fbac1, 0xff8bb6bd, 0xff8cb8c3, 0xff7fa9b7, 0xff8eafc0, 0xffaec9de, 0xffc3d7ef, 0xffcadaf4, 0xffcddcf9, 0xffcbdefc, 0xffc7dffb, 0xffc5defc, 0xffbfd6f5, 0xffabbede, 0xff8b95b9, 0xff6b6d94, 0xff5f5882, 0xff5f547e, 
    0xff576eb1, 0xff5a71b4, 0xff6a7dbf, 0xff7c8fd1, 0xff8799db, 0xff8897d8, 0xff8898d4, 0xff8a9dd5, 0xff8ca2d3, 0xff93afd7, 0xff86a9c7, 0xff6c96ac, 0xff6494a0, 0xff689fa4, 0xff579393, 0xff3e7677, 0xff57868e, 0xff769eaa, 0xff789eab, 0xff547a87, 0xff3b5f6d, 0xff3f606f, 0xff416271, 0xff375867, 0xff4c6c7b, 0xff7a9baa, 0xff84a5b4, 0xff83a7b5, 0xff8ab0bd, 0xff8bb3bf, 0xff88b0bc, 0xff6e98a4, 0xff6f96a5, 0xff82aab6, 0xff82aab4, 0xff7ea9b0, 0xff8bb6bc, 0xff89b6bb, 0xff83aeb5, 0xff8db5c1, 0xff8cb1c3, 0xff80a0b5, 0xff133247, 0xff5b778c, 0xffa4becf, 0xffb1cbd8, 0xff98b0ba, 0xff96a9b8, 0xff929dbb, 0xff838baf, 0xff8e9bbb, 0xff9dacc9, 0xff899db6, 0xff768fa5, 0xff768fa3, 0xff738d9c, 0xff7b939f, 0xff7b8f98, 0xff9caab3, 0xffa3acb3, 0xff7f8289, 0xff77757a, 0xff4e444c, 0xff63666f, 0xff6d8997, 0xff7ba4b6, 0xff8cb5c9, 0xff86b1c4, 0xff78a5b8, 0xff73a2b4, 0xff6f9eb2, 0xff6a99ab, 0xff6897a9, 0xff6493a3, 0xff6896a5, 0xff6b99a6, 0xff709ca7, 0xff80acb5, 0xff8eb9c2, 0xff89b5be, 0xff90becb, 0xff7caab7, 0xff87acbe, 0xffa3c2d6, 0xffb2cce3, 0xffbcd2ea, 0xffc6dbf8, 0xffcbe0fd, 0xffc0d7f6, 0xffbbd4f3, 0xffc1d8f8, 0xffccdeff, 0xffc4d0f8, 0xffa2a7d1, 0xff7b7aa6, 0xff63608d, 
    0xff2a5a9c, 0xff29599b, 0xff2b569a, 0xff2f579c, 0xff32599e, 0xff375a9e, 0xff375999, 0xff365993, 0xff3f6397, 0xff4c719d, 0xff6189ac, 0xff5d89a2, 0xff6495a4, 0xff6ea3a9, 0xff548c8d, 0xff407676, 0xff5d8a90, 0xff739ba5, 0xff7197a2, 0xff577d88, 0xff426672, 0xff355763, 0xff365663, 0xff42626f, 0xff3e5b69, 0xff71919e, 0xff87a7b4, 0xff7fa1ad, 0xff88acb8, 0xff8bb1bc, 0xff769ea8, 0xff66929d, 0xff6e9eaa, 0xff7badb6, 0xff74a5aa, 0xff84b5b9, 0xff85b5b7, 0xff91c1c3, 0xff7facb2, 0xff82aeb9, 0xff81aabe, 0xff698fa6, 0xff284a65, 0xff65839d, 0xff87a2b7, 0xffa4bbcb, 0xff899faa, 0xff7d8d9c, 0xff7b829f, 0xff878bb0, 0xff8d93b5, 0xff8c95b4, 0xff8c9ab5, 0xff92a2b9, 0xff91a2b6, 0xff8a9bab, 0xff8c9ca9, 0xff89969f, 0xff8d949e, 0xff5f5f67, 0xff9d989f, 0xff736870, 0xff6d5d67, 0xff585462, 0xff6b839b, 0xff82a9c6, 0xff8ab3d1, 0xff76a2bf, 0xff709cb9, 0xff729fbc, 0xff6d9ab9, 0xff6796b2, 0xff6594ae, 0xff6290a8, 0xff6592a7, 0xff6693a6, 0xff6b96a6, 0xff7eaab7, 0xff8eb8c4, 0xff8ab4c0, 0xff84b4c0, 0xff7caab9, 0xff83adbd, 0xff85a8bb, 0xff7e9db2, 0xff97b1c8, 0xffbbd5f0, 0xffc7dffb, 0xffc0d9f7, 0xffbcd5f4, 0xffc1d7fc, 0xffc7d9ff, 0xffc3d0fc, 0xffc0c8f6, 0xffb3b7e7, 0xff9b9dce, 
    0xff245da2, 0xff235ca1, 0xff245aa0, 0xff285aa1, 0xff2d5ca4, 0xff305ca3, 0xff335ba0, 0xff335a9b, 0xff375e97, 0xff577dae, 0xff7da4cb, 0xff77a0be, 0xff6b96a9, 0xff65919c, 0xff4c797e, 0xff406d70, 0xff648f96, 0xff75a0a9, 0xff7399a4, 0xff597d89, 0xff446672, 0xff365663, 0xff365361, 0xff415d6b, 0xff314d5b, 0xff597684, 0xff6d8a98, 0xff6f8f9c, 0xff7c9eaa, 0xff7ca0ac, 0xff6d939e, 0xff648f98, 0xff80b2bd, 0xff81b6be, 0xff77aaae, 0xff7aacad, 0xff7dadad, 0xff84b1b4, 0xff7facb2, 0xff89b5c2, 0xff88b2c8, 0xff658eaa, 0xff274b6b, 0xff6687a6, 0xff839fb7, 0xff7f98ac, 0xff566c79, 0xff566675, 0xff505573, 0xff5e6184, 0xff767a9d, 0xff8d93b3, 0xff97a1bc, 0xff99a3bc, 0xff9da8be, 0xffa5adc0, 0xff979eae, 0xff6e7180, 0xff60606c, 0xff78727e, 0xffc2b8c3, 0xff9d909a, 0xffa08f99, 0xff696576, 0xff7992b1, 0xff81a8d1, 0xff85afd7, 0xff82aed5, 0xff84afd9, 0xff79a6cd, 0xff6693ba, 0xff5c8aae, 0xff6290b2, 0xff5d8caa, 0xff628ea9, 0xff6794ab, 0xff6f9aab, 0xff81abbb, 0xff8cb4c0, 0xff82acb8, 0xff8fbdca, 0xff89b7c6, 0xff83adbd, 0xff779cae, 0xff6e8ea3, 0xff7897ac, 0xff89a5bd, 0xff8ba5c0, 0xff98afce, 0xff97aed0, 0xffa0b4d9, 0xffa9bae5, 0xffb1bfec, 0xffbdc9f9, 0xffc2c9fd, 0xffb9bff3, 
    0xff366cb4, 0xff376bb4, 0xff3a6cb5, 0xff3f6fb9, 0xff4671bf, 0xff4a74c0, 0xff4d73be, 0xff5072ba, 0xff5372b3, 0xff607db7, 0xff7692c2, 0xff708cb1, 0xff6e8ca6, 0xff7794a6, 0xff65818c, 0xff597a83, 0xff688e97, 0xff75a0a9, 0xff7096a1, 0xff537783, 0xff3f616d, 0xff32525f, 0xff304c5a, 0xff375160, 0xff233d4c, 0xff3d5766, 0xff516d7b, 0xff607d8b, 0xff6c8e9a, 0xff6a8e9a, 0xff658995, 0xff6a929c, 0xff81afbc, 0xff7faeb8, 0xff81aeb3, 0xff82abad, 0xff8db5b5, 0xff7ea6a8, 0xff7ca2ab, 0xff80aaba, 0xff84adc9, 0xff5984a6, 0xff244b72, 0xff6b8eb4, 0xff8eadcc, 0xff6d899f, 0xff2a4151, 0xff2e3f4f, 0xff323c57, 0xff292e4c, 0xff2d3451, 0xff4b526e, 0xff69708c, 0xff8288a2, 0xffa4aac2, 0xffc5cade, 0xffb4b7ca, 0xff9e9daf, 0xff908c9d, 0xffb8b1c1, 0xffd4c9d9, 0xffaca0ae, 0xffab9daa, 0xff5c5a70, 0xff577099, 0xff527aad, 0xff5880b3, 0xff628cbe, 0xff6e97cb, 0xff6995c6, 0xff628ebd, 0xff6695c1, 0xff5f8cb5, 0xff5a88aa, 0xff628dad, 0xff6c98b1, 0xff76a1b4, 0xff85afbf, 0xff8cb4c0, 0xff80aab6, 0xff7ca8b5, 0xff85b1be, 0xff80aaba, 0xff7ca1b3, 0xff88abbe, 0xff97b6cb, 0xff96b2ca, 0xff93abc7, 0xff869bba, 0xff8395b9, 0xff7c8eb6, 0xff7785b2, 0xff7785b4, 0xff7f8cc0, 0xff8792c8, 0xff8893c9, 
    0xff86b5fd, 0xff87b5ff, 0xff8cb8ff, 0xff90b9ff, 0xff95bbff, 0xff97bcff, 0xff97b8ff, 0xff97b4ff, 0xffa8c3ff, 0xff9bb3f3, 0xff93aae0, 0xff7b92bc, 0xff7289a8, 0xff72889f, 0xff586e7c, 0xff48636e, 0xff6d929b, 0xff77a2ab, 0xff6d959f, 0xff507480, 0xff3d5f6b, 0xff355260, 0xff304a59, 0xff334a5a, 0xff203747, 0xff304757, 0xff476170, 0xff5e7a88, 0xff648491, 0xff60828e, 0xff668a96, 0xff789ca8, 0xff88acba, 0xff81a3af, 0xff8aa7af, 0xff87a3a7, 0xff9fb8bc, 0xff88a1a6, 0xff829fad, 0xff7fa2b8, 0xff7fa6c7, 0xff709ac2, 0xff5580ad, 0xff84aed8, 0xff8fb3d7, 0xff5a7995, 0xff173243, 0xff253c4c, 0xff16263d, 0xff101e38, 0xff1f2b45, 0xff424c65, 0xff616882, 0xff6f758d, 0xff7a7f95, 0xff888aa1, 0xffaaabc0, 0xffb6b4c9, 0xff9794a7, 0xff817d8e, 0xff807c8d, 0xff9891a1, 0xffa9a2b2, 0xff797d98, 0xff435f8e, 0xff426ba3, 0xff436ba6, 0xff406aa4, 0xff426ca6, 0xff406ba2, 0xff416fa3, 0xff4f7dae, 0xff5480ad, 0xff517ea5, 0xff5d88a8, 0xff6a96af, 0xff749db1, 0xff81abb9, 0xff8ab2bc, 0xff84acb4, 0xff84aeba, 0xff93bac9, 0xff86adbc, 0xff80a5b7, 0xff9fc0d3, 0xffb5d4e8, 0xffb6d0e7, 0xffb6cee8, 0xffb4c7e7, 0xffb0c0e4, 0xff9ba8d2, 0xff818ebb, 0xff7381b2, 0xff6d7aaf, 0xff6877ae, 0xff6a79b0, 
    0xff587cc6, 0xff587cc6, 0xff597dcb, 0xff5a7dce, 0xff5b7bce, 0xff5878cb, 0xff5571c5, 0xff526dbe, 0xff455faa, 0xff4d64a7, 0xff657bb4, 0xff687fab, 0xff687f9f, 0xff6b8199, 0xff5b7081, 0xff56727d, 0xff7399a2, 0xff7ca7ae, 0xff7098a0, 0xff527780, 0xff44646f, 0xff3e5a66, 0xff39505e, 0xff374d5b, 0xff1f3543, 0xff2f4654, 0xff4e6573, 0xff637d8a, 0xff617f8a, 0xff5d7d88, 0xff6e909a, 0xff85a2b0, 0xff9daec2, 0xff8e99ad, 0xff8a90a0, 0xff808490, 0xff999da6, 0xff89919c, 0xff8695a8, 0xff8199b5, 0xff6383aa, 0xff779dcc, 0xff77a2d5, 0xff76a2d3, 0xff5680a8, 0xff284f6c, 0xff05283b, 0xff234352, 0xff2c4758, 0xff3d5768, 0xff576c7f, 0xff67788c, 0xff667288, 0xff586075, 0xff4a4f65, 0xff45475e, 0xff45455d, 0xff68697e, 0xff606176, 0xff393a4e, 0xff54576a, 0xff858a9d, 0xff5e6376, 0xff3d4c69, 0xff48689b, 0xff4e76b4, 0xff4971af, 0xff3c65a3, 0xff3f68a6, 0xff426faa, 0xff426fa6, 0xff4371a3, 0xff45719e, 0xff437097, 0xff527d9d, 0xff608da4, 0xff6993a3, 0xff76a1aa, 0xff84adb1, 0xff84adb1, 0xff8db3be, 0xff96bac8, 0xff82a6b4, 0xff7899aa, 0xff91b2c3, 0xffa8c4d9, 0xffacc4dc, 0xffb3c8e3, 0xffa4b4d5, 0xffa5b3d6, 0xff909cc6, 0xff7b86b4, 0xff7884b8, 0xff6f7fb3, 0xff6677af, 0xff687bb3, 
    0xff4363ac, 0xff4161aa, 0xff3f5fac, 0xff3e5dad, 0xff3d5db0, 0xff3c5cb1, 0xff3b5aad, 0xff3a56a9, 0xff3e5ba7, 0xff415ea0, 0xff5b78b0, 0xff6a87b3, 0xff7390b0, 0xff728ea4, 0xff597684, 0xff51727b, 0xff729aa2, 0xff7ba6ad, 0xff6c949c, 0xff4d727b, 0xff41616c, 0xff3f5b67, 0xff384f5d, 0xff324856, 0xff1c3240, 0xff324856, 0xff566c7a, 0xff647e8b, 0xff5d7985, 0xff61818c, 0xff7496a0, 0xff849bab, 0xff8888a2, 0xff887b97, 0xff7e6f86, 0xff756476, 0xff817382, 0xff766d7e, 0xff67677f, 0xff586587, 0xff425a88, 0xff4d71a5, 0xff5380b9, 0xff3f6ea2, 0xff2e5d89, 0xff356283, 0xff315a6e, 0xff416774, 0xff587e8b, 0xff6d8f9b, 0xff7a97a5, 0xff6b8292, 0xff495a6c, 0xff313e51, 0xff33394f, 0xff3c4157, 0xff32344b, 0xff3e4057, 0xff383d53, 0xff0a1227, 0xff2b364a, 0xff505f72, 0xff223345, 0xff2b4462, 0xff7093c9, 0xff6a93d3, 0xff527bbb, 0xff3a63a3, 0xff3c68a7, 0xff4571ae, 0xff416ea7, 0xff3c6a9c, 0xff44709d, 0xff406e92, 0xff4d7b95, 0xff5f8c9f, 0xff6995a0, 0xff77a3a6, 0xff86b0ae, 0xff89b1b1, 0xff88aab4, 0xff91b1c0, 0xff8cacbb, 0xff84a4b3, 0xff82a1b3, 0xff7b98aa, 0xff6f889e, 0xff6c809b, 0xff687796, 0xff727ca0, 0xff646d98, 0xff5f6796, 0xff727eb2, 0xff7584bb, 0xff6a7db5, 0xff6d83bc, 
    0xff5775bd, 0xff4d6eb5, 0xff4262ad, 0xff3859a6, 0xff3255a6, 0xff3055a6, 0xff3155a9, 0xff3156a6, 0xff4066af, 0xff3960a1, 0xff4b74aa, 0xff5883ad, 0xff6490ad, 0xff638ea1, 0xff497580, 0xff437076, 0xff6c999f, 0xff76a3a9, 0xff658d95, 0xff456a73, 0xff3d5b66, 0xff3b5562, 0xff314856, 0xff283e4c, 0xff1a2d3c, 0xff324856, 0xff586e7c, 0xff627987, 0xff5a7682, 0xff6a8a95, 0xff799ba5, 0xff798a9c, 0xff604f6f, 0xff795579, 0xff6f4b67, 0xff68455b, 0xff624154, 0xff5b4257, 0xff3e314f, 0xff2a2c53, 0xff3b4c80, 0xff33538c, 0xff416daa, 0xff2f6299, 0xff356998, 0xff6799bc, 0xff78a8be, 0xff75a3b0, 0xff5d8c94, 0xff679299, 0xff7197a0, 0xff6d8b96, 0xff4a616f, 0xff233444, 0xff172236, 0xff21273d, 0xff0f132c, 0xff1a2038, 0xff323d53, 0xff223146, 0xff2f4459, 0xff314a5e, 0xff1a364b, 0xff204160, 0xff284f86, 0xff335c9e, 0xff3861a3, 0xff3c68a9, 0xff4d79b8, 0xff5985c2, 0xff6392c8, 0xff72a2d2, 0xff4e7da7, 0xff457394, 0xff4f7d94, 0xff6593a2, 0xff75a2a7, 0xff83afae, 0xff8cb7b0, 0xff89b2ae, 0xff95b3bd, 0xff94afc0, 0xff96b3c1, 0xff89a6b4, 0xff638090, 0xff3e596c, 0xff2b4158, 0xff21324c, 0xff283555, 0xff353d62, 0xff29305c, 0xff333b6a, 0xff5e6a9e, 0xff707fb6, 0xff667bb4, 0xff6b84bd, 
    0xff96b1e8, 0xff84a1d9, 0xff6b89c9, 0xff5172b7, 0xff4163ad, 0xff385dab, 0xff355ca9, 0xff355da5, 0xff2c5597, 0xff39649b, 0xff5f8bb8, 0xff6d9abb, 0xff6795ac, 0xff5b8b99, 0xff4d7d87, 0xff5a898f, 0xff709ba2, 0xff7ca2ab, 0xff678b97, 0xff456771, 0xff3c5a65, 0xff3a5662, 0xff2e4855, 0xff233a48, 0xff152b39, 0xff324856, 0xff576d7b, 0xff5e7481, 0xff5d7581, 0xff79919d, 0xff839ea9, 0xff767b8f, 0xff5a365a, 0xff7c436c, 0xff683151, 0xff5a253f, 0xff421529, 0xff48253b, 0xff2f1b37, 0xff251e47, 0xff303a6f, 0xff324a86, 0xff567cba, 0xff3d6ba6, 0xff2b5e8d, 0xff588daf, 0xff7cafc4, 0xff81b2c0, 0xff78a9b0, 0xff649196, 0xff5d868c, 0xff62848d, 0xff4f6976, 0xff233645, 0xff081526, 0xff0a1227, 0xff181e34, 0xff11172f, 0xff18223b, 0xff0b1a31, 0xff0e223a, 0xff132b43, 0xff2a445b, 0xff1a3755, 0xff22416f, 0xff456399, 0xff6987bd, 0xff7997cd, 0xff6989bc, 0xff446696, 0xff345d89, 0xff467297, 0xff5885a4, 0xff49768d, 0xff52788b, 0xff6d919d, 0xff84a5aa, 0xff91b3b2, 0xff91b4ae, 0xff88a8a5, 0xff8ea7ae, 0xff7d93a0, 0xff76909d, 0xff637f8b, 0xff33505e, 0xff193646, 0xff223b51, 0xff2b3f58, 0xff283754, 0xff354264, 0xff2f3861, 0xff434e7b, 0xff8391c2, 0xffa3b4ea, 0xffa0b6ef, 0xffaac3fc, 
    0xffa2b3cd, 0xff9fb4d3, 0xff98b1da, 0xff8ca7dc, 0xff7695d5, 0xff5d80c4, 0xff456aae, 0xff385e9c, 0xff3e6397, 0xff466b95, 0xff658aa7, 0xff7a9eb4, 0xff688d9d, 0xff4f7781, 0xff4e7982, 0xff5b818c, 0xff819eac, 0xff76909f, 0xff6c8798, 0xff4a6674, 0xff3f5b69, 0xff395664, 0xff1a3843, 0xff1f3b47, 0xff25404b, 0xff3c5460, 0xff5f7280, 0xff6d7f8b, 0xff758390, 0xff8a96a4, 0xff7e8796, 0xff5b4d64, 0xff692f57, 0xff8d406a, 0xff7f3858, 0xff5b1d34, 0xff562332, 0xff5f384a, 0xff4e344d, 0xff2f2446, 0xff38386a, 0xff34407a, 0xff526aa8, 0xff395994, 0xff123a6b, 0xff618fb1, 0xff72a2b8, 0xff6e9fad, 0xff7aa6af, 0xff5b838b, 0xff4f747d, 0xff34545f, 0xff233e49, 0xff1a2d3b, 0xff000d1d, 0xff060e21, 0xff11162a, 0xff2d3248, 0xff131730, 0xff131933, 0xff080f2b, 0xff141d3a, 0xff222d4b, 0xff293452, 0xff4f5974, 0xffafb4d1, 0xffb6b5d4, 0xffbcbbda, 0xffa0a5c3, 0xff909eb9, 0xff3b576c, 0xff315668, 0xff4f7783, 0xff456771, 0xff6a808d, 0xff85939e, 0xff949ea7, 0xffa6b1b5, 0xff8fa3a2, 0xff758988, 0xff4a595c, 0xff69787d, 0xffa1b4bb, 0xffa9c1cb, 0xff91adbb, 0xff81a1b0, 0xff7392a6, 0xff7692a7, 0xff7991a9, 0xff7c91ae, 0xff8194b5, 0xff8c9ec6, 0xff99abd9, 0xffa0b5ea, 0xff9fb8f1, 0xff9db7f2, 
    0xffb1c1d1, 0xffb2c2db, 0xffaec4e9, 0xffa8c2f3, 0xff9cbaf6, 0xff8cadf0, 0xff7da0e0, 0xff7699d3, 0xff5a7aa9, 0xff5f80a3, 0xff7290a8, 0xff85a2b0, 0xff7d9ba5, 0xff65838d, 0xff61828b, 0xff75939e, 0xff879eae, 0xff8194a5, 0xff778c9d, 0xff4d6776, 0xff35515d, 0xff284651, 0xff102e38, 0xff16343e, 0xff46626d, 0xff4f6a73, 0xff637782, 0xff6b7c86, 0xff78828e, 0xff8b919d, 0xff777986, 0xff4f394e, 0xff713154, 0xff89395e, 0xff803a54, 0xff682b3b, 0xff5c2d37, 0xff623e4a, 0xff533c50, 0xff322747, 0xff393a6a, 0xff38417c, 0xff5b6fae, 0xff395692, 0xff0f3468, 0xff457299, 0xff5789a2, 0xff5b8b9f, 0xff426d7e, 0xff1d4455, 0xff0d3244, 0xff002132, 0xff051f30, 0xff091e31, 0xff000b1e, 0xff081327, 0xff181e34, 0xff20253b, 0xff0c1029, 0xff0e122d, 0xff0a0e2b, 0xff181d3a, 0xff1c213f, 0xff383955, 0xff9a95a9, 0xff9e91a2, 0xff725c71, 0xff705a6f, 0xff86778c, 0xffb1b0c0, 0xff748690, 0xff38555b, 0xff638287, 0xff465f64, 0xff6a747d, 0xff93939d, 0xff98959e, 0xff828187, 0xff4e5859, 0xff2f3b3b, 0xff30383b, 0xff626b72, 0xffabb9c4, 0xffb7cddb, 0xffa8c3d6, 0xffa7c6db, 0xff9fbfd6, 0xff9ebcd4, 0xff85a0bb, 0xff7d97b2, 0xff758aa9, 0xff6f83a8, 0xff6b80ab, 0xff6880b0, 0xff637cb2, 0xff5f79b2, 
    0xffaab9d6, 0xffabbedf, 0xffacc2f1, 0xffadc7ff, 0xffa9c8ff, 0xffa4c4ff, 0xffa0c1ff, 0xff9fbdfd, 0xff9db9eb, 0xff8ea6cc, 0xff71879f, 0xff657887, 0xff5a6e79, 0xff435760, 0xff465d65, 0xff6e848f, 0xff8194a2, 0xff7f92a0, 0xff798f9c, 0xff546c78, 0xff314c55, 0xff213f47, 0xff16343c, 0xff1e3d42, 0xff537076, 0xff597277, 0xff64787f, 0xff6d7e85, 0xff7f8992, 0xff8a919b, 0xff666874, 0xff332232, 0xff52203b, 0xff571732, 0xff571e2f, 0xff4b1c26, 0xff381519, 0xff3a2127, 0xff322536, 0xff13102d, 0xff151c4a, 0xff182863, 0xff4b62a5, 0xff284788, 0xff00245c, 0xff063563, 0xff124467, 0xff104362, 0xff022f50, 0xff001437, 0xff001233, 0xff001533, 0xff001a38, 0xff041c38, 0xff071834, 0xff1d2943, 0xff17213c, 0xff09102a, 0xff040a22, 0xff081027, 0xff0a1229, 0xff1a253b, 0xff141f35, 0xff56586f, 0xffa99aaf, 0xff6b4f65, 0xff401d35, 0xff3a172f, 0xff50344b, 0xff948799, 0xffa0a6b2, 0xff63767d, 0xff61767b, 0xff45575b, 0xff62656c, 0xff757077, 0xff615a61, 0xff484347, 0xff272d2b, 0xff1b2526, 0xff292d36, 0xff636979, 0xffb1bed1, 0xffc0d2ea, 0xffb3cae9, 0xffb9d6f6, 0xffb4d2f4, 0xffb0cdef, 0xffb4ceef, 0xffadc6e5, 0xffa5b9da, 0xff99add0, 0xff8aa0c7, 0xff7a91bb, 0xff6981af, 0xff5d78a7, 
    0xff7d90ba, 0xff7e93c2, 0xff7e97d0, 0xff7e9bdf, 0xff7e9eeb, 0xff7f9ef0, 0xff819eec, 0xff839ee3, 0xff8da2d9, 0xff788ab2, 0xff4d5973, 0xff374051, 0xff3b434e, 0xff333c43, 0xff495259, 0xff858f98, 0xff85939c, 0xff81929a, 0xff7b8d97, 0xff586c73, 0xff2d464b, 0xff1a363a, 0xff183438, 0xff1a3639, 0xff3c5559, 0xff425a5c, 0xff4a5d61, 0xff566568, 0xff727b80, 0xff868b91, 0xff6b6b73, 0xff41343e, 0xff492234, 0xff3a0a1a, 0xff3c131b, 0xff3b1b1c, 0xff260e0e, 0xff241518, 0xff221e2c, 0xff080c27, 0xff020f3b, 0xff0d225b, 0xff4964a7, 0xff325398, 0xff113977, 0xff00295d, 0xff002b56, 0xff002149, 0xff001c4d, 0xff001547, 0xff001b4a, 0xff001f4c, 0xff001943, 0xff00143a, 0xff102246, 0xff314061, 0xff1a2543, 0xff020c27, 0xff0c172d, 0xff0c172b, 0xff0a1527, 0xff162334, 0xff0d1a2a, 0xff797a8c, 0xff8e758b, 0xff461e38, 0xff4f1e3b, 0xff4d1c39, 0xff39112b, 0xff654c61, 0xffaca6b4, 0xff89909a, 0xff59646a, 0xff485053, 0xff5a555b, 0xff55484f, 0xff47383d, 0xff4d4543, 0xff444641, 0xff383e3e, 0xff343643, 0xff5d5f76, 0xff9da6c3, 0xffb1bfe2, 0xffaac0e7, 0xffb3cbf7, 0xffb2cdfa, 0xffb4cffc, 0xffb5cef7, 0xffb6ccf3, 0xffb8ccef, 0xffb9cdee, 0xffb7cbec, 0xffafc7e9, 0xffa6c0e5, 0xff9ebadf, 
    0xff4d669f, 0xff4d67a4, 0xff4b67b0, 0xff4968b8, 0xff4769c2, 0xff4a69c3, 0xff4e6abe, 0xff526ab4, 0xff54639e, 0xff555e89, 0xff3f435e, 0xff3c3b4b, 0xff4c4650, 0xff454046, 0xff524d53, 0xff818085, 0xff8d949a, 0xff859396, 0xff7b8a8f, 0xff5f7173, 0xff334748, 0xff1f3435, 0xff233839, 0xff1b312f, 0xff314745, 0xff3a4d4b, 0xff344242, 0xff2c3637, 0xff384042, 0xff484c4d, 0xff3e3e40, 0xff2d2228, 0xff41262f, 0xff2b0a11, 0xff301615, 0xff3a2721, 0xff271c16, 0xff221e1d, 0xff21232f, 0xff0a142d, 0xff03143f, 0xff0c2359, 0xff3b5798, 0xff315297, 0xff254c8d, 0xff15427b, 0xff1e4e7f, 0xff174778, 0xff0f3d79, 0xff0d3676, 0xff092f6d, 0xff0a2d67, 0xff032157, 0xff001848, 0xff142954, 0xff2e3d64, 0xff1f2c4c, 0xff0c1832, 0xff1d293f, 0xff152233, 0xff081623, 0xff0c1a25, 0xff05131c, 0xff8f8f9b, 0xff88677c, 0xff4f1a36, 0xff6c2f4e, 0xff672c4a, 0xff511f3a, 0xff78556b, 0xffbdacbc, 0xff88858e, 0xff5b5c61, 0xff403e41, 0xff56474c, 0xff6a5658, 0xff7c6668, 0xff8d7e7b, 0xff726e65, 0xff4d4d4b, 0xff444152, 0xff484766, 0xff73799d, 0xff8d99c3, 0xff93a5d7, 0xff9cb3e7, 0xffa2bbf3, 0xffb2cbff, 0xffb4cbfd, 0xffb5caf7, 0xffb6caef, 0xffb7cbec, 0xffbacfec, 0xffb9d1ed, 0xffb9d3ee, 0xffb8d3ee, 
    0xff415fa5, 0xff3e5ea9, 0xff3a5daf, 0xff385bb5, 0xff375bbb, 0xff385abb, 0xff3d5bb5, 0xff4459a8, 0xff505b99, 0xff585b88, 0xff514d68, 0xff504555, 0xff554851, 0xff49393c, 0xff433336, 0xff534749, 0xff555557, 0xff525858, 0xff50585a, 0xff495352, 0xff2a3636, 0xff21302d, 0xff364542, 0xff293833, 0xff374641, 0xff505c58, 0xff4f5855, 0xff3d4140, 0xff3a3c39, 0xff3b3a38, 0xff34302f, 0xff312728, 0xff342123, 0xff220c0e, 0xff25140d, 0xff32251d, 0xff282119, 0xff1e1f1a, 0xff191d26, 0xff081327, 0xff041438, 0xff051b4c, 0xff152d69, 0xff112f6f, 0xff163875, 0xff254c85, 0xff416b9d, 0xff4772a7, 0xff3965a6, 0xff3a62a8, 0xff30559b, 0xff3a5c9c, 0xff3e5b95, 0xff344b7f, 0xff2f416f, 0xff1e2d54, 0xff142141, 0xff0e1a34, 0xff1d283c, 0xff131f2d, 0xff08151e, 0xff0c1921, 0xff09181d, 0xff98979f, 0xff816073, 0xff632d47, 0xff753653, 0xff6c2d4a, 0xff78405d, 0xffb1879d, 0xffd7c0d0, 0xff8d838b, 0xff534d51, 0xff392f30, 0xff624c4f, 0xff947779, 0xffa98b8b, 0xffa88f8a, 0xff8a7e72, 0xff776f6c, 0xff645d6d, 0xff4a4764, 0xff5b5d83, 0xff6b76a3, 0xff6e7fb3, 0xff7185c0, 0xff728ac8, 0xff859dd9, 0xff9ab1e7, 0xffa3b7e9, 0xffafc2ec, 0xffb9cced, 0xffbed3f0, 0xffbcd4ec, 0xffb8d3e8, 0xffb4d0e5, 
    0xff4267b8, 0xff4066bb, 0xff3c63c0, 0xff3961c3, 0xff3760c9, 0xff3a60c5, 0xff405fbc, 0xff495eaf, 0xff4e5997, 0xff535480, 0xff544c64, 0xff574956, 0xff604b50, 0xff614949, 0xff573f3f, 0xff4b3736, 0xff281e1d, 0xff2c2825, 0xff363231, 0xff3e3d39, 0xff262624, 0xff262823, 0xff494b46, 0xff3a3f39, 0xff484d47, 0xff71746d, 0xff7b7c76, 0xff6d6a65, 0xff68635f, 0xff6b6360, 0xff706664, 0xff7e7371, 0xff4c3c3c, 0xff3b2927, 0xff2b1e18, 0xff291f16, 0xff262219, 0xff21201c, 0xff1b1e23, 0xff181f31, 0xff131e3c, 0xff17264f, 0xff0c1e50, 0xff0a1f54, 0xff0a2359, 0xff294577, 0xff43628e, 0xff4b6d9d, 0xff3a609f, 0xff446bb0, 0xff3c5fa3, 0xff4d6cac, 0xff5b74ad, 0xff526699, 0xff414f7c, 0xff152147, 0xff0d1635, 0xff111930, 0xff101729, 0xff070f1c, 0xff09121b, 0xff10191e, 0xff0d181a, 0xff87868c, 0xff896d7c, 0xff734359, 0xff723851, 0xff7f435d, 0xffad7791, 0xffd8aec2, 0xffd2bac7, 0xff968990, 0xff574c50, 0xff4e3e3f, 0xff836567, 0xffa88484, 0xffa6807d, 0xff9e7d76, 0xff927c6f, 0xff93847d, 0xff7f757e, 0xff5e5b70, 0xff606180, 0xff5f6891, 0xff59699d, 0xff586ca7, 0xff5068a8, 0xff5870b0, 0xff596fa9, 0xff677bb0, 0xff7d90bb, 0xff93a5cb, 0xffa4b9d6, 0xffb0c8e0, 0xffb7d2e7, 0xffbbd6e9, 
    0xff3c65bd, 0xff3863bf, 0xff3561c4, 0xff325fc6, 0xff315dc8, 0xff355cc5, 0xff3a5bba, 0xff435aaa, 0xff4d5995, 0xff51537c, 0xff5e546c, 0xff6c5b65, 0xff796365, 0xff856966, 0xff755854, 0xff533a36, 0xff5e4c48, 0xff655754, 0xff6e605d, 0xff726763, 0xff4f4641, 0xff47403a, 0xff6b645e, 0xff5a554f, 0xff766f69, 0xff948d85, 0xff8a817c, 0xff645953, 0xff554744, 0xff584944, 0xff6b5c59, 0xff8b7977, 0xff6b5959, 0xff514141, 0xff2d201a, 0xff190e08, 0xff1a130b, 0xff191512, 0xff1a191e, 0xff242632, 0xff1b2036, 0xff292f4f, 0xff141d44, 0xff13204a, 0xff05143d, 0xff1d2f55, 0xff263a5d, 0xff243d65, 0xff1f4076, 0xff2b4f8d, 0xff1d3d78, 0xff224076, 0xff304678, 0xff354674, 0xff36426a, 0xff161c3e, 0xff191d38, 0xff1e2035, 0xff0a0b1d, 0xff00010d, 0xff080b14, 0xff0e1118, 0xff050a0e, 0xff66636a, 0xffad99a5, 0xff7f5d6e, 0xff683b50, 0xff9a697f, 0xffe1b4c9, 0xffe9c4d5, 0xffb5a1aa, 0xff8a7f83, 0xff716567, 0xff736161, 0xff9f7d7e, 0xffac8283, 0xffa57875, 0xffa77f77, 0xff947769, 0xff7e6a5f, 0xff7e7272, 0xff66636e, 0xff6a6c81, 0xff636c8d, 0xff5d6e9c, 0xff6378b1, 0xff5a72b2, 0xff5871b3, 0xff6479b8, 0xff6176af, 0xff5f71a3, 0xff61749e, 0xff6e82a5, 0xff829ab6, 0xff9ab4cd, 0xffabc5dc, 
    0xff3462ba, 0xff3362be, 0xff3060c4, 0xff2f60c7, 0xff305fc9, 0xff335ec6, 0xff3a5db9, 0xff425ca9, 0xff4a5690, 0xff55577d, 0xff645b6e, 0xff736268, 0xff836e6b, 0xff8f746d, 0xff8d7068, 0xff876a64, 0xff8e7570, 0xff907874, 0xff917975, 0xff8c7772, 0xff87726d, 0xff85726c, 0xff887770, 0xff8a7b74, 0xff907f78, 0xff817069, 0xff6c5a56, 0xff5b4a43, 0xff55423e, 0xff58433e, 0xff5c4744, 0xff5f4a47, 0xff503a3d, 0xff503c3e, 0xff4c3a3a, 0xff40312e, 0xff342623, 0xff2e2222, 0xff31282b, 0xff362f37, 0xff373342, 0xff2f2d42, 0xff2f2f47, 0xff333752, 0xff2c324c, 0xff1e263d, 0xff1b263a, 0xff22334d, 0xff1e3b63, 0xff284976, 0xff284571, 0xff173059, 0xff15274d, 0xff202d50, 0xff222848, 0xff161733, 0xff1e1c32, 0xff201b2f, 0xff231c2c, 0xff1e1824, 0xff100d16, 0xff0d0a13, 0xff1e1b24, 0xff363138, 0xff9f959e, 0xffaf9faa, 0xffae92a1, 0xffd3b1c2, 0xffefcfde, 0xffd2b8c5, 0xff9f9299, 0xff666060, 0xff8e8684, 0xff8e7c7a, 0xff957372, 0xffa07272, 0xffaa7877, 0xffa77a74, 0xff997569, 0xff866f61, 0xff7d6f66, 0xff6f6b6a, 0xff555861, 0xff818ca2, 0xff98aad0, 0xff869dcf, 0xff96b0eb, 0xff8aa3e3, 0xff96aeee, 0xff99afeb, 0xff9eb2e7, 0xffa1b6e5, 0xffa5bbe4, 0xffa9bfe4, 0xffabc3e5, 0xffabc5e6, 
    0xff3662b9, 0xff3562bd, 0xff3361c1, 0xff3161c7, 0xff305fc7, 0xff345ec2, 0xff395db5, 0xff415ba5, 0xff4c598e, 0xff575a7b, 0xff655e6e, 0xff756669, 0xff87726d, 0xff937b71, 0xff987b73, 0xff977a72, 0xff967772, 0xff977875, 0xff957874, 0xff8e716d, 0xff846864, 0xff7d645f, 0xff7d645f, 0xff7e6761, 0xff715955, 0xff69514d, 0xff5e4642, 0xff553d39, 0xff503834, 0xff4e3632, 0xff4e3531, 0xff4b3331, 0xff4a3437, 0xff493238, 0xff443032, 0xff3f2b2d, 0xff3a2729, 0xff362627, 0xff37282b, 0xff372b2f, 0xff342931, 0xff2f2732, 0xff302a38, 0xff302e3c, 0xff2c2b39, 0xff262835, 0xff2d313d, 0xff354151, 0xff607a95, 0xff6483a2, 0xff5b7693, 0xff39516d, 0xff22324c, 0xff1f2942, 0xff24293f, 0xff27253a, 0xff322b3d, 0xff342c3b, 0xff362b39, 0xff332733, 0xff281e29, 0xff201621, 0xff211722, 0xff231d27, 0xff6a6b70, 0xffa8a7ad, 0xffd2c5cf, 0xffe2ceda, 0xffc3adb9, 0xff877781, 0xff6b666a, 0xff595959, 0xff777370, 0xff807070, 0xff8f6d6e, 0xff9b6d6f, 0xff9f6d6e, 0xff9b6b67, 0xff8e685f, 0xff81675a, 0xff6d5f52, 0xff656158, 0xff5c6063, 0xff9aa6b6, 0xffbfd2f3, 0xffb2cafa, 0xffbedbff, 0xffb4d0ff, 0xffbad3ff, 0xffbbd3ff, 0xffbfd3ff, 0xffc0d5ff, 0xffc1d7ff, 0xffc2d8ff, 0xffc1d9ff, 0xffc2daff, 
    0xff3b61b2, 0xff3961b6, 0xff3760ba, 0xff345fbe, 0xff335dbf, 0xff355cb9, 0xff3a5aad, 0xff41589c, 0xff4a5887, 0xff535774, 0xff605a66, 0xff6f6161, 0xff7d6a63, 0xff897368, 0xff90786e, 0xff967971, 0xff91736b, 0xff93726d, 0xff906f6a, 0xff876661, 0xff795a55, 0xff6e514b, 0xff694c48, 0xff674b47, 0xff5c433e, 0xff59403b, 0xff543b37, 0xff513834, 0xff4e3531, 0xff4b322e, 0xff472f2b, 0xff442c2c, 0xff4f383e, 0xff4a353c, 0xff473239, 0xff463136, 0xff463136, 0xff443133, 0xff412e32, 0xff3c2c2f, 0xff36272a, 0xff32272b, 0xff2d272b, 0xff2c272d, 0xff2e2d32, 0xff3d3e43, 0xff535459, 0xff5c666f, 0xff7a94a3, 0xff7d9cae, 0xff7893a4, 0xff5b7081, 0xff3a4757, 0xff282f3f, 0xff2d2f3c, 0xff373542, 0xff322a37, 0xff362a36, 0xff372a34, 0xff382833, 0xff382833, 0xff362631, 0xff2c1e2b, 0xff1f1923, 0xff1d2729, 0xff505b5d, 0xff7b787f, 0xff857b84, 0xff685c66, 0xff453e45, 0xff434448, 0xff424647, 0xff535351, 0xff645858, 0xff7d5f61, 0xff8b6163, 0xff8f5e61, 0xff895b5b, 0xff7f5c56, 0xff775f53, 0xff615346, 0xff545047, 0xff505457, 0xff919eae, 0xffb7cbec, 0xffa8c3f0, 0xffadccff, 0xffa5c4ff, 0xffa4c1ff, 0xffa4c0ff, 0xffa5bdfd, 0xffa5baf9, 0xffa3b9f5, 0xffa1b7f1, 0xff9fb5ef, 0xff9db6ef, 
    0xff4763ad, 0xff4562b0, 0xff4161b4, 0xff3e60b9, 0xff3b5eba, 0xff3d5db4, 0xff405ba8, 0xff465a97, 0xff49557f, 0xff50526b, 0xff585159, 0xff645655, 0xff6e5b54, 0xff745e53, 0xff796358, 0xff80655c, 0xff7e6058, 0xff805f58, 0xff7c5b56, 0xff74534e, 0xff684944, 0xff5d403a, 0xff563935, 0xff533733, 0xff59403b, 0xff563d38, 0xff503834, 0xff4c3430, 0xff48332e, 0xff48332e, 0xff483330, 0xff473332, 0xff473438, 0xff433238, 0xff423137, 0xff413036, 0xff402f35, 0xff3e2e31, 0xff38292c, 0xff322628, 0xff362c2b, 0xff332a2b, 0xff2c2827, 0xff2b2b2b, 0xff3b3f40, 0xff595e61, 0xff73787e, 0xff7a878f, 0xff69848d, 0xff6e8f98, 0xff78949f, 0xff70878f, 0xff58666f, 0xff3c454e, 0xff32353c, 0xff36333a, 0xff2d262e, 0xff31262e, 0xff33232d, 0xff312229, 0xff372630, 0xff3c2c36, 0xff392934, 0xff29242b, 0xff253334, 0xff273737, 0xff313237, 0xff353037, 0xff332c34, 0xff333037, 0xff303539, 0xff20282a, 0xff303435, 0xff463d3e, 0xff62474c, 0xff764f54, 0xff7a5054, 0xff774f50, 0xff6f524e, 0xff69554e, 0xff594c44, 0xff433f3c, 0xff3a3e47, 0xff6b778d, 0xff849abf, 0xff738fbf, 0xff7292cd, 0xff6d8fcf, 0xff6a89cc, 0xff6a87cd, 0xff6b83c9, 0xff6982c5, 0xff677ec2, 0xff657cc0, 0xff637ac0, 0xff6179bf, 
    0xff5463a6, 0xff5263a9, 0xff4f62af, 0xff4b60b1, 0xff495fb4, 0xff485faf, 0xff4b5da5, 0xff505c96, 0xff4f5379, 0xff4f4b62, 0xff52474f, 0xff594a47, 0xff604c45, 0xff604941, 0xff614a42, 0xff654c45, 0xff61463f, 0xff62453f, 0xff60453e, 0xff5c413a, 0xff563a36, 0xff4e3530, 0xff4a312c, 0xff47302a, 0xff4c3732, 0xff4b3631, 0xff483531, 0xff44322e, 0xff40312c, 0xff3d302a, 0xff3e302d, 0xff3c312d, 0xff3e3435, 0xff3f3639, 0xff41383b, 0xff40373a, 0xff3d3439, 0xff3a3136, 0xff373135, 0xff363233, 0xff333230, 0xff30322f, 0xff2f3533, 0xff394342, 0xff515f62, 0xff6b7a81, 0xff758690, 0xff718792, 0xff668790, 0xff698b94, 0xff75939d, 0xff7b969d, 0xff71848b, 0xff525f65, 0xff373c40, 0xff2b2a2f, 0xff322d31, 0xff362d30, 0xff362a2e, 0xff33272b, 0xff36272e, 0xff372a31, 0xff35282f, 0xff2c272b, 0xff2e393b, 0xff263435, 0xff34333b, 0xff312934, 0xff2c222d, 0xff34313a, 0xff32373d, 0xff222c2e, 0xff1d2527, 0xff2c2a2d, 0xff453037, 0xff58393f, 0xff623f45, 0xff614144, 0xff56433f, 0xff4e403f, 0xff493c43, 0xff383240, 0xff35374e, 0xff586587, 0xff697fae, 0xff5a77af, 0xff5578b8, 0xff557ac0, 0xff5476be, 0xff5575be, 0xff5773bd, 0xff5771bc, 0xff586fbb, 0xff576ebc, 0xff566dbd, 0xff546dbd, 
    0xff5b5d9a, 0xff595d9d, 0xff555ca4, 0xff525ba8, 0xff4e5ba9, 0xff4e5aa6, 0xff50599c, 0xff545990, 0xff535176, 0xff4a4257, 0xff47383f, 0xff4d3938, 0xff503c35, 0xff4f3832, 0xff4e3731, 0xff513a34, 0xff4b342c, 0xff4b342c, 0xff4a332b, 0xff4a332b, 0xff48312b, 0xff45312a, 0xff433029, 0xff413029, 0xff3d2c25, 0xff3e2f2a, 0xff41342e, 0xff40352f, 0xff3e3530, 0xff3b342e, 0xff3a332d, 0xff37342f, 0xff363531, 0xff383934, 0xff3a3a38, 0xff3a3a3a, 0xff38373c, 0xff38393d, 0xff3a3b3f, 0xff3b3f40, 0xff3b4443, 0xff3b4745, 0xff455353, 0xff576b6c, 0xff6b8288, 0xff728a96, 0xff688293, 0xff59788a, 0xff698d9b, 0xff668c97, 0xff6d8f99, 0xff78959d, 0xff788f97, 0xff66757c, 0xff495257, 0xff35393c, 0xff302e31, 0xff302a2c, 0xff342a2b, 0xff372d2e, 0xff372d2e, 0xff33292a, 0xff322829, 0xff302c2d, 0xff222a2c, 0xff1a2225, 0xff322a35, 0xff291b28, 0xff1b0f1b, 0xff332b36, 0xff3a3f45, 0xff343d42, 0xff1d262b, 0xff24252a, 0xff32232a, 0xff412730, 0xff492e35, 0xff483235, 0xff3b312f, 0xff322c2e, 0xff34283c, 0xff352c4b, 0xff44446a, 0xff5d6896, 0xff677cb5, 0xff5b79b9, 0xff5176bd, 0xff5278c3, 0xff5377c3, 0xff5576c3, 0xff5875c3, 0xff5a75c4, 0xff5a74c8, 0xff5c74ca, 0xff5c74cc, 0xff5c76cd, 
    0xff675c94, 0xff665c98, 0xff625d9f, 0xff5e5da3, 0xff5a5ca7, 0xff595da4, 0xff5c5d9d, 0xff5f5d8f, 0xff635c7e, 0xff524559, 0xff423137, 0xff442f2c, 0xff48302c, 0xff462d28, 0xff442c2a, 0xff45302b, 0xff422f28, 0xff402f25, 0xff3f2e26, 0xff3f2e26, 0xff402f28, 0xff3f3029, 0xff3f3029, 0xff3d3028, 0xff392e28, 0xff3b322d, 0xff3c3731, 0xff3e3b34, 0xff3e3d38, 0xff40413b, 0xff454640, 0xff484b44, 0xff3b453a, 0xff38443a, 0xff364139, 0xff36413d, 0xff3a4445, 0xff3e484a, 0xff414b4d, 0xff3f4d4e, 0xff4b5d5d, 0xff495e5f, 0xff546c6e, 0xff668389, 0xff6f8f9c, 0xff65889c, 0xff577b95, 0xff4d758e, 0xff588193, 0xff5b8593, 0xff628895, 0xff6b8b98, 0xff728d96, 0xff71848b, 0xff646f75, 0xff565b5f, 0xff3b3b3d, 0xff2d2b2c, 0xff2b2525, 0xff352d2b, 0xff39312e, 0xff352d2a, 0xff39312e, 0xff413d3a, 0xff414246, 0xff28272d, 0xff342633, 0xff220d1c, 0xff120010, 0xff332735, 0xff3a3a44, 0xff2e373e, 0xff262f36, 0xff262930, 0xff2e222c, 0xff36222d, 0xff3b262f, 0xff38292e, 0xff2d2c2a, 0xff27272f, 0xff2b2040, 0xff3b325f, 0xff555589, 0xff626ca7, 0xff6175ba, 0xff5876bf, 0xff4e72c0, 0xff5075c5, 0xff4f74c4, 0xff5073c4, 0xff5372c4, 0xff5672c5, 0xff5872c9, 0xff5871cb, 0xff5972ce, 0xff5872cf, 
    0xff856396, 0xff846398, 0xff80639d, 0xff7d63a2, 0xff7a64a4, 0xff7a64a2, 0xff7c659b, 0xff7f6592, 0xff856a89, 0xff6a4d61, 0xff52333b, 0xff4c2e30, 0xff4d2f2d, 0xff4a2c2a, 0xff482c2b, 0xff49312f, 0xff45322e, 0xff42302c, 0xff3f2d29, 0xff3c2d28, 0xff3d2e27, 0xff3d3028, 0xff3b3128, 0xff3a312a, 0xff3e3633, 0xff3b3734, 0xff373833, 0xff373936, 0xff383e3a, 0xff414a47, 0xff505956, 0xff596561, 0xff677870, 0xff5c716a, 0xff546964, 0xff546867, 0xff5b6f70, 0xff617478, 0xff62757b, 0xff5e7378, 0xff546c70, 0xff4d696c, 0xff547177, 0xff61828b, 0xff618595, 0xff55798f, 0xff4e738d, 0xff527a93, 0xff4c7587, 0xff56808e, 0xff628797, 0xff688a96, 0xff6f8b96, 0xff738b95, 0xff708189, 0xff68757b, 0xff525b60, 0xff363e40, 0xff262b2e, 0xff2d3132, 0xff323635, 0xff2f3332, 0xff383c3b, 0xff4a4e4f, 0xff31363c, 0xff1c1a25, 0xff2f2432, 0xff1d0f20, 0xff0f0112, 0xff312938, 0xff373945, 0xff2b323c, 0xff2c353e, 0xff2c3039, 0xff2f2935, 0xff302631, 0xff312732, 0xff302a34, 0xff2a2f33, 0xff292f3f, 0xff28264e, 0xff413e75, 0xff5f649e, 0xff636eae, 0xff5c70b7, 0xff5975bf, 0xff5474c1, 0xff597ac9, 0xff5677c6, 0xff5776c6, 0xff5976c6, 0xff5a74c8, 0xff5a74c8, 0xff5b73c9, 0xff5973ca, 0xff5972cc, 
    0xffa76392, 0xffa56391, 0xffa56393, 0xffa76595, 0xffab6999, 0xffad6b99, 0xffad6c96, 0xffac6c8f, 0xffaa6e8a, 0xff93596f, 0xff703c49, 0xff582931, 0xff52282c, 0xff552e2f, 0xff512f30, 0xff47292b, 0xff452b34, 0xff3f2a33, 0xff453035, 0xff392526, 0xff3a2826, 0xff443530, 0xff3a2d27, 0xff372d2b, 0xff363030, 0xff3d3d3f, 0xff3d4045, 0xff3b4449, 0xff3b494c, 0xff39484b, 0xff405254, 0xff536869, 0xff5a7679, 0xff577478, 0xff59767c, 0xff67848a, 0xff7c99a1, 0xff829fa7, 0xff6c8991, 0xff516e76, 0xff4b6870, 0xff4a6870, 0xff52707a, 0xff5e7c87, 0xff5f7f8c, 0xff597988, 0xff537284, 0xff527384, 0xff4f707f, 0xff4a6e7a, 0xff547682, 0xff668691, 0xff6f8d98, 0xff698791, 0xff67828d, 0xff6b868f, 0xff68808a, 0xff4f686f, 0xff3f565e, 0xff2e454d, 0xff283f45, 0xff4a6167, 0xff6f868c, 0xff738790, 0xff5a6875, 0xff394050, 0xff151829, 0xff0d0f1e, 0xff212332, 0xff363847, 0xff383b4a, 0xff2d333f, 0xff2a303c, 0xff2b313d, 0xff222534, 0xff2d2f3e, 0xff2e3142, 0xff252b3b, 0xff2d3647, 0xff28344e, 0xff35406d, 0xff4d5890, 0xff6473ae, 0xff6b7bb9, 0xff6679bd, 0xff6178bc, 0xff6278c1, 0xff637bc5, 0xff6a82cc, 0xff6a81cd, 0xff6a81cd, 0xff6b82ce, 0xff6b82ce, 0xff6b82ce, 0xff6c83cf, 0xff6c83cf, 
    0xff934871, 0xff924770, 0xff934670, 0xff964870, 0xff9a4c73, 0xff9d5074, 0xffa05171, 0xff9e5270, 0xffa05772, 0xff965569, 0xff83485a, 0xff693542, 0xff542932, 0xff4b272b, 0xff4b282c, 0xff472a2f, 0xff432c3c, 0xff3e2c3c, 0xff4a3642, 0xff413036, 0xff3e302f, 0xff413632, 0xff382e2c, 0xff3c3734, 0xff4f4f51, 0xff64676e, 0xff656e77, 0xff56646d, 0xff4e606a, 0xff485f65, 0xff3e575b, 0xff375458, 0xff4b6c75, 0xff557781, 0xff658791, 0xff7698a2, 0xff82a4ae, 0xff7c9ea8, 0xff62828d, 0xff476772, 0xff476772, 0xff486873, 0xff53717c, 0xff5e7c87, 0xff617f8a, 0xff5a7883, 0xff54707c, 0xff52707b, 0xff4b6974, 0xff476772, 0xff4f6f7a, 0xff60808b, 0xff688893, 0xff64848f, 0xff62828d, 0xff668691, 0xff7595a0, 0xff6d8d98, 0xff6e8e99, 0xff64848f, 0xff557580, 0xff62828d, 0xff7595a0, 0xff74909c, 0xff617483, 0xff465364, 0xff263343, 0xff1e2a3a, 0xff252e3d, 0xff2c3343, 0xff2b3340, 0xff29313e, 0xff414757, 0xff4b5262, 0xff454c5e, 0xff4c5467, 0xff4f5a6e, 0xff49556b, 0xff4d5971, 0xff41506f, 0xff5f71a1, 0xff697cb4, 0xff7487c2, 0xff7789c7, 0xff7589c8, 0xff778acc, 0xff778acf, 0xff7689ce, 0xff7084cd, 0xff7084cd, 0xff6f85ce, 0xff6f85ce, 0xff7086cf, 0xff7086cf, 0xff7087cd, 0xff7087cd, 
    0xff73375d, 0xff713559, 0xff6f3156, 0xff6f3254, 0xff743457, 0xff773759, 0xff793858, 0xff783956, 0xff753b54, 0xff774256, 0xff734456, 0xff663d4b, 0xff57363f, 0xff50353a, 0xff50373b, 0xff4c3841, 0xff3e3143, 0xff332a3d, 0xff403543, 0xff3b343b, 0xff352f2f, 0xff322e2b, 0xff2c2b29, 0xff3c3e3d, 0xff6e7377, 0xff909ba1, 0xff96a4ad, 0xff7b8f98, 0xff6f8791, 0xff6c878e, 0xff59787a, 0xff426466, 0xff486a73, 0xff5a7c86, 0xff72949e, 0xff7fa1ab, 0xff7fa1ab, 0xff6f919b, 0xff567681, 0xff41616c, 0xff456570, 0xff496974, 0xff55737e, 0xff5e7c87, 0xff5f7d88, 0xff587681, 0xff516d79, 0xff4d6b76, 0xff415f6a, 0xff3e5e69, 0xff476772, 0xff587883, 0xff61818c, 0xff60808b, 0xff60808b, 0xff64848f, 0xff678792, 0xff6f8f9a, 0xff7e9ea9, 0xff7b9ba6, 0xff668691, 0xff60808b, 0xff63838e, 0xff627e8a, 0xff607381, 0xff4a5a69, 0xff354554, 0xff2f3c4c, 0xff273341, 0xff1d2635, 0xff1b2433, 0xff212a39, 0xff82899b, 0xff9da5b8, 0xff9ca4b9, 0xff9da8be, 0xffa2aec6, 0xff9caac4, 0xff99a7c2, 0xff8696b8, 0xff788aba, 0xff7487bf, 0xff6e81bc, 0xff697bb9, 0xff6a7ebd, 0xff6f82c4, 0xff7184c9, 0xff6e81c6, 0xff7185ce, 0xff7185ce, 0xff7086cf, 0xff7086cf, 0xff7187d0, 0xff7187d0, 0xff7188ce, 0xff7188ce, 
    0xff6d4466, 0xff694060, 0xff663a5b, 0xff643758, 0xff653656, 0xff663757, 0xff663855, 0xff663854, 0xff633750, 0xff623b50, 0xff603f50, 0xff604453, 0xff634d59, 0xff66555f, 0xff61545b, 0xff574f5a, 0xff636075, 0xff484860, 0xff434456, 0xff383a46, 0xff2c3033, 0xff222828, 0xff1d2625, 0xff353f40, 0xff748187, 0xff9aabb3, 0xffa6bac5, 0xff8ea9b2, 0xff86a2ad, 0xff86a7ae, 0xff7aa0a1, 0xff6f9598, 0xff60828b, 0xff6d8f99, 0xff7a9ca6, 0xff7d9fa9, 0xff73959f, 0xff61838d, 0xff50707b, 0xff44646f, 0xff44646f, 0xff4b6b76, 0xff577580, 0xff5e7c87, 0xff5e7c87, 0xff56747f, 0xff4e6a76, 0xff486671, 0xff3b5964, 0xff395964, 0xff41616c, 0xff4f6f7a, 0xff5a7a85, 0xff5d7d88, 0xff5f7f8a, 0xff63838e, 0xff61818c, 0xff6b8b96, 0xff7898a3, 0xff7898a3, 0xff698994, 0xff5d7d88, 0xff5d7d88, 0xff627e8a, 0xff576d7a, 0xff445664, 0xff354755, 0xff334352, 0xff293744, 0xff162230, 0xff131f2f, 0xff1e2a3a, 0xff7e8699, 0xffaeb9cf, 0xffb5bfd8, 0xffb0bcd6, 0xffb5c3e0, 0xffb4c3e2, 0xffb3c2e3, 0xffa4b3dc, 0xff9dafe1, 0xff92a5df, 0xff8295d0, 0xff7486c4, 0xff6e82c1, 0xff7184c6, 0xff7184c9, 0xff6d80c5, 0xff6f83cc, 0xff6f83cc, 0xff6d83cc, 0xff6d83cc, 0xff6e84cd, 0xff6e85cb, 0xff6e85cb, 0xff6f86cc, 
    0xffac98b4, 0xffa894af, 0xffa58eaa, 0xffa289a6, 0xffa389a6, 0xffa588a6, 0xffa588a4, 0xffa589a2, 0xffa58ba4, 0xffa58da3, 0xffa18ea2, 0xff9f90a3, 0xffa199a8, 0xffa8a2b0, 0xffa4a2af, 0xff9c9daf, 0xff9fa4c1, 0xff737c9b, 0xff5a657b, 0xff414d5d, 0xff2d3a42, 0xff1f2e31, 0xff192929, 0xff2d4142, 0xff63787d, 0xff869fa6, 0xff99b5c0, 0xff93b4bd, 0xff8badb7, 0xff82a9b0, 0xff81aaac, 0xff89b2b4, 0xff80a5ad, 0xff85a7b1, 0xff85a7b1, 0xff7fa1ab, 0xff6f919b, 0xff5c7e88, 0xff4e6e79, 0xff486873, 0xff456570, 0xff4e6e79, 0xff5a7883, 0xff5e7c87, 0xff5b7984, 0xff54727d, 0xff4c6874, 0xff45636e, 0xff385661, 0xff375762, 0xff3e5e69, 0xff496974, 0xff54747f, 0xff5a7a85, 0xff5e7e89, 0xff61818c, 0xff64848f, 0xff688893, 0xff6d8d98, 0xff6f8f9a, 0xff6a8a95, 0xff5e7e89, 0xff5b7b86, 0xff65838e, 0xff58707c, 0xff445863, 0xff354954, 0xff364854, 0xff2e3e4d, 0xff1e2b3b, 0xff192637, 0xff222f42, 0xff6b768c, 0xffadb9d3, 0xffb9c4e2, 0xffaebbdb, 0xffb4c2e5, 0xffb5c5e9, 0xffbac9f2, 0xffb6c7f3, 0xffb0c1f7, 0xffa8bbf5, 0xff98abe6, 0xff8496d4, 0xff778bca, 0xff7386c8, 0xff7285ca, 0xff7083c8, 0xff6d81ca, 0xff6d81ca, 0xff6c82cb, 0xff6c82cb, 0xff6c83c9, 0xff6d84ca, 0xff6d84ca, 0xff6d84c8, 
    0xffb5b1ca, 0xffb2aec7, 0xffafa9c3, 0xffb0a7c2, 0xffb2a8c3, 0xffb6a9c5, 0xffb8aac4, 0xffb7a9c3, 0xffb2a5bf, 0xffb7adc5, 0xffbab2ca, 0xffb2afc4, 0xffabacc0, 0xffadb0c3, 0xffb1b6c9, 0xffb2bad1, 0xff9dabce, 0xff7383a7, 0xff5a6b89, 0xff405268, 0xff2e414f, 0xff24383f, 0xff1b3337, 0xff2e474b, 0xff577279, 0xff77949c, 0xff91b1bc, 0xff9abcc6, 0xff91b7c2, 0xff7ca7ae, 0xff78a4a7, 0xff88b4b7, 0xff8eb3bb, 0xff8fb1bb, 0xff8badb7, 0xff80a2ac, 0xff6b8d97, 0xff53757f, 0xff44646f, 0xff3f5f6a, 0xff476772, 0xff52727d, 0xff5d7b86, 0xff5e7c87, 0xff597782, 0xff52707b, 0xff4b6773, 0xff43616c, 0xff3a5863, 0xff395964, 0xff3d5d68, 0xff456570, 0xff4e6e79, 0xff567681, 0xff5b7b86, 0xff5d7d88, 0xff5c7c87, 0xff5e7e89, 0xff5e7e89, 0xff64848f, 0xff668691, 0xff54747f, 0xff496974, 0xff57757f, 0xff56717c, 0xff435964, 0xff334954, 0xff334752, 0xff304250, 0xff233444, 0xff1c2b3e, 0xff1e2d42, 0xff46546e, 0xff8e9cb9, 0xff95a2c4, 0xff8391b6, 0xff8594bd, 0xff8091bc, 0xff8495c3, 0xff899bcd, 0xff8598d0, 0xff8699d4, 0xff8092d0, 0xff7587c7, 0xff6b7ec0, 0xff6b7ec2, 0xff6d80c5, 0xff6d7fc7, 0xff6f83ca, 0xff6f83cc, 0xff6d83cc, 0xff6e85cb, 0xff6e85cb, 0xff6e85c9, 0xff6f86ca, 0xff6f86ca, 
    0xffb2bad1, 0xffb1b9d0, 0xffb0b6ce, 0xffb1b5ce, 0xffb5b7d0, 0xffb8b8d2, 0xffb9b6d3, 0xffb9b6d3, 0xffb2afcc, 0xffb6b6d0, 0xffb9bad6, 0xffb4b8d1, 0xffb1b7cf, 0xffb1b9d0, 0xffafb9d2, 0xffabb9d6, 0xff6e81ab, 0xff556a97, 0xff4d6388, 0xff3c5470, 0xff31485a, 0xff2a424e, 0xff203b42, 0xff2f4a51, 0xff56727d, 0xff74929d, 0xff8caeba, 0xff99bdc9, 0xff99bfcc, 0xff87b2bb, 0xff80abb1, 0xff8ab5bb, 0xff8fb4bd, 0xff8fb1bb, 0xff8badb7, 0xff7d9fa9, 0xff62848e, 0xff466872, 0xff3a5a65, 0xff395964, 0xff4a6a75, 0xff557580, 0xff607e89, 0xff5d7b86, 0xff56747f, 0xff516f7a, 0xff4b6773, 0xff43616c, 0xff3c5a65, 0xff3b5b66, 0xff3d5d68, 0xff42626d, 0xff496974, 0xff52727d, 0xff567681, 0xff577782, 0xff597984, 0xff5d7d88, 0xff5b7b86, 0xff61818c, 0xff658590, 0xff4e6e79, 0xff40606b, 0xff52707a, 0xff4d6974, 0xff3f5a63, 0xff324a54, 0xff2e444f, 0xff2b414e, 0xff263948, 0xff1e2f43, 0xff1a2a41, 0xff384862, 0xff7b8caa, 0xff7c8cb0, 0xff697ba3, 0xff7081ad, 0xff6779a9, 0xff6478ab, 0xff6c80b5, 0xff6a7db7, 0xff6e81bc, 0xff7082c0, 0xff6d7fbf, 0xff6c7fc1, 0xff7184c8, 0xff7386cb, 0xff7385cd, 0xff7084cb, 0xff7084cb, 0xff6e85cb, 0xff6f86cc, 0xff6f86cc, 0xff6f86ca, 0xff7087cb, 0xff7087ca, 
    0xffafbed5, 0xffadbcd3, 0xffaebad2, 0xffaebad2, 0xffb2b9d3, 0xffb2b9d3, 0xffb2b6d3, 0xffb1b5d2, 0xffb7bbd8, 0xffb3b7d4, 0xffaeb3d0, 0xffaeb5d1, 0xffb5bcd8, 0xffb2bed8, 0xffa3aeca, 0xff8e9ebf, 0xff556a99, 0xff4c6494, 0xff546b94, 0xff48617f, 0xff3b546a, 0xff314b58, 0xff233f4a, 0xff2d4a52, 0xff53717c, 0xff6e8e99, 0xff84a5b4, 0xff8eb4c1, 0xff99bfcc, 0xff94bfc8, 0xff90bbc2, 0xff94bfc5, 0xff93b8c1, 0xff93b5bf, 0xff8dafb9, 0xff7b9da7, 0xff5b7d87, 0xff3f616b, 0xff395964, 0xff40606b, 0xff4c6c77, 0xff587883, 0xff617f8a, 0xff5d7b86, 0xff55737e, 0xff506e79, 0xff4b6773, 0xff44626d, 0xff3e5c67, 0xff3c5c67, 0xff3d5d68, 0xff3f5f6a, 0xff466671, 0xff4e6e79, 0xff53737e, 0xff53737e, 0xff4f6f7a, 0xff557580, 0xff51717c, 0xff567681, 0xff597984, 0xff40606b, 0xff355560, 0xff4c6d76, 0xff47636e, 0xff3f5c64, 0xff374f59, 0xff304852, 0xff2e4452, 0xff2a3f50, 0xff24364a, 0xff1c2e46, 0xff2c3d59, 0xff6d7d9e, 0xff6c7ea4, 0xff6475a0, 0xff7587b7, 0xff6e82b5, 0xff6d81b6, 0xff768bc2, 0xff7184bf, 0xff7386c1, 0xff7284c4, 0xff7082c2, 0xff6f82c6, 0xff7285c9, 0xff7082ca, 0xff6b7dc5, 0xff6f83ca, 0xff6f83ca, 0xff6d84ca, 0xff6e85cb, 0xff6e85c9, 0xff6e85c9, 0xff6f86c9, 0xff6f86c9
};
// Define the tImage structure if not already included from a header
typedef struct {
    const uint32_t* data;
    uint16_t width;
    uint16_t height;
} tImage;

const tImage laoban = { image_data_laoban, 128, 96};

// 简单的测试图像数据（16x16像素）
const uint32_t test_image_data[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00,
    0x00, 0x00, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0x00, 0x00,
    0x00, 0x00, 0xFF, 0x00, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0x00, 0xFF, 0x00, 0x00,
    0x00, 0x00, 0xFF, 0x00, 0xFF, 0x00, 0xFF, 0x00, 0x00, 0xFF, 0x00, 0xFF, 0x00, 0xFF, 0x00, 0x00,
    0x00, 0x00, 0xFF, 0x00, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0x00, 0xFF, 0x00, 0x00,
    0x00, 0x00, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0x00, 0x00,
    0x00, 0x00, 0xFF, 0x00, 0xFF, 0x00, 0xFF, 0x00, 0x00, 0xFF, 0x00, 0xFF, 0x00, 0xFF, 0x00, 0x00,
    0x00, 0x00, 0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x00, 0x00, 0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00,
    0x00, 0x00, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0x00, 0x00,
    0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
};

// 测试图像结构体
const tImage test_image = { test_image_data, 16, 12 };

// 简单的测试显示函数
void test_display_simple_image(void)
{
    // 清屏（可选）
    // tft180_clear(0x0000);  // 黑色背景
    
    // 在屏幕中央显示测试图像
    tft180_show_rgb565_image(
        0, 0,                              // 屏幕中央位置 (128-16)/2=56, (160-12)/2=74
        (const uint16_t *)test_image_data,   // 测试图像数据
        16, 12,                              // 图像尺寸
        128, 96,                              // 显示尺寸（不缩放）
        0                                    // 颜色模式，0表示16位RGB565
    );
}

// 封装的图像显示函数
// 此函数使用 tft180_show_rgb565_image 来显示图像
// 它会将 128x153 的源图像数据显示在屏幕上
void display_laoban_image(void)
{
    uint16_t src_width = 128;
    uint16_t src_height = 96;
    
    // 尝试交换宽度和高度来修正旋转
    tft180_show_gray_image(
        0, 0,                                // 起始坐标
        (const uint8 *)image_data_laoban,    // 图像数据
        src_height, src_width,               // 交换宽度和高度
        src_height, src_width,               // 显示尺寸也相应交换
        0                                    // 使用灰度模式
    );
}