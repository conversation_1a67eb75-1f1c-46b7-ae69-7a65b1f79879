#include "zf_common_headfile.h"
#include "sxt.h"
#include "yuansu.h"
#include "math.h"
uint8_t *PerImg_ip[RESULT_ROW][RESULT_COL];

void ImagePerspective_Init(void) {

    static uint8_t BlackColor = 0;
    double change_un_Mat[3][3] ={{1.083333,-0.748454,17.637035},{0.000000,0.244163,-6.705097},{0.000000,-0.008827,1.242415}};
    for (int i = 0; i < RESULT_COL ;i++) {
        for (int j = 0; j < RESULT_ROW ;j++) {
            int local_x = (int) ((change_un_Mat[0][0] * i
                    + change_un_Mat[0][1] * j + change_un_Mat[0][2])
                    / (change_un_Mat[2][0] * i + change_un_Mat[2][1] * j
                            + change_un_Mat[2][2]));
            int local_y = (int) ((change_un_Mat[1][0] * i
                    + change_un_Mat[1][1] * j + change_un_Mat[1][2])
                    / (change_un_Mat[2][0] * i + change_un_Mat[2][1] * j
                            + change_un_Mat[2][2]));
            if (local_x
                    >= 0&& local_y >= 0 && local_y < USED_ROW && local_x < USED_COL){
                PerImg_ip[j][i] = &PER_IMG[local_y][local_x];
            }
            else {
                PerImg_ip[j][i] = &BlackColor;          //&PER_IMG[0][0];
            }

        }
    }
}


/*完成摄像头初始化后，调用一次ImagePerspective_Init，此后，直接调用ImageUsed   即为透视结果*/

// 全局定义正向透视变换矩阵 hd（change_un_Mat 的逆矩阵）
float hd[3][3] = {
    {0.92308, 2.92692, 2.69229},
    {0.0,     5.08841, 27.46124},
    {0.0,     0.03615,  0.99999}
};

float Get_angle(float Ax, float Ay, float Bx, float By, float Cx, float Cy) 
{ 
    float BA = 0.00;      // 向量 BA 的模
    float BC = 0.00;      // 向量 BC 的模
    float SBA_BC = 0.00;  // 向量点乘的值
    float angle = 0.00;   // 角度（弧度）

    // 变换点 A
    float denom_A = hd[2][0] * Ax + hd[2][1] * Ay + hd[2][2];
    if (fabs(denom_A) < 1e-6) return 0.0;  // 避免除零
    float AX = (hd[0][0] * Ax + hd[0][1] * Ay + hd[0][2]) / denom_A;
    float AY = (hd[1][0] * Ax + hd[1][1] * Ay + hd[1][2]) / denom_A;

    // 变换点 B
    float denom_B = hd[2][0] * Bx + hd[2][1] * By + hd[2][2];
    if (fabs(denom_B) < 1e-6) return 0.0;
    float BX = (hd[0][0] * Bx + hd[0][1] * By + hd[0][2]) / denom_B;
    float BY = (hd[1][0] * Bx + hd[1][1] * By + hd[1][2]) / denom_B;

    // 变换点 C
    float denom_C = hd[2][0] * Cx + hd[2][1] * Cy + hd[2][2];
    if (fabs(denom_C) < 1e-6) return 0.0;
    float CX = (hd[0][0] * Cx + hd[0][1] * Cy + hd[0][2]) / denom_C;
    float CY = (hd[1][0] * Cx + hd[1][1] * Cy + hd[1][2]) / denom_C;

    // 计算向量 BA 和 BC 的模
    BA = sqrt((AX - BX) * (AX - BX) + (AY - BY) * (AY - BY));
    BC = sqrt((CX - BX) * (CX - BX) + (CY - BY) * (CY - BY));

    // 检查向量模是否为零
    if (BA < 1e-6 || BC < 1e-6) return 0.0;

    // 计算点积
    SBA_BC = (AX - BX) * (CX - BX) + (AY - BY) * (CY - BY);

    // 计算最小夹角（使用 fabs 确保角度在 0° 至 90°）
    float cos_theta = fabs(SBA_BC) / (BA * BC);
    if (cos_theta > 1.0) cos_theta = 1.0;  // 防止浮点误差
    angle = acos(cos_theta);

    // 转换为度数并返回
    return angle * 57.3;
}

uint8_t mt9v03x_image_2[MT9V03X_H][MT9V03X_W];
uint8_t mid[MT9V03X_H];
int side_left[MT9V03X_H], side_right[MT9V03X_H];
int feedbackposition=0;
// 输出图像数组
int Left_Line[MT9V03X_H];    // 左边界数组
int Right_Line[MT9V03X_H];   // 右边界数组
int Mid_Line[MT9V03X_H];     // 中线数组
uint8_t mt9v03x_image_eroded[MT9V03X_H][MT9V03X_W];
uint8_t mt9v03x_image_dilated[MT9V03X_H][MT9V03X_W];

static uint8_t last_threshold = 128;
// Define GrayScale
#define GRAY_SCALE 256
uint8_t otsuThreshold(uint8_t *image)
{
    int Pixel_Max = 0;
    int Pixel_Min = 255;
    uint16_t width = MT9V03X_W;
    uint16_t height = MT9V03X_H;
    uint32_t pixelSum = (width * height) / 4; // Subsampled pixel count
    uint32_t gray_sum = 0;
    uint8_t threshold = 0;

    // Initialize pixelCount array to zero
    uint32_t pixelCount[GRAY_SCALE];
    memset(pixelCount, 0, sizeof(pixelCount));

    // Subsample the image by taking every 2nd pixel in both dimensions
    uint8_t* data = image;
    uint32_t idx;
    for (uint16_t i = 0; i < height; i += 2)
    {
        for (uint16_t j = 0; j < width; j += 2)
        {
            idx = i * width + j;
            uint8_t pixel = data[idx];
            pixelCount[pixel]++;
            gray_sum += pixel;

            if (pixel > Pixel_Max) Pixel_Max = pixel;
            if (pixel < Pixel_Min) Pixel_Min = pixel;
        }
    }

    // Calculate total sum only once
    uint32_t total_sum = gray_sum;

    // Compute class probabilities and cumulative sums
    uint32_t sumB = 0;
    uint32_t wB = 0;
    uint32_t wF = 0;

    float varMax = 0.0f;

    // Iterate through all possible thresholds
    for (uint8_t t = Pixel_Min; t <= Pixel_Max; t++)
    {
        wB += pixelCount[t];
        if (wB == 0)
            continue;

        wF = pixelSum - wB;
        if (wF == 0)
            break;

        sumB += t * pixelCount[t];

        // Compute class means
        float mB = (float)sumB / (float)wB;
        float mF = ((float)total_sum - (float)sumB) / (float)wF;

        // Compute between-class variance
        float varBetween = (float)wB * (float)wF * (mB - mF) * (mB - mF);

        // Update threshold if new maximum found
        if (varBetween > varMax)
        {
            varMax = varBetween;
            threshold = t;
        }
    }

    return threshold;
}

//-------------------------------------------------------------------------------------------------------------------
//  @brief      使用大津法阈值对图像进行二值化
//  @return     void
//-------------------------------------------------------------------------------------------------------------------
void binarizeImage(uint8_t *image, uint8_t *binary_image)
{
    // 计算大津法阈值
    uint8_t threshold = otsuThreshold(image);
    // tft180_show_uint(0,80,threshold,5);
    // 遍历图像并进行二值化
    for (int i = 0; i < MT9V03X_H; i++)
    {
        for (int j = 0; j < MT9V03X_W; j++)
        {
            uint8_t pixel = image[i * MT9V03X_W + j];
            binary_image[i * MT9V03X_W + j] = (pixel < threshold) ? 0 : 255;
        }
    }
}

//-------------------------------------------------------------------------------------------------------------------
//  @brief      对二值化图像进行腐蚀操作
//  @return     void
//-------------------------------------------------------------------------------------------------------------------
void erodeImage(uint8_t *binary_image, uint8_t *eroded_image)
{
    // 初始化腐蚀后的图像为0
    memset(eroded_image, 0, MT9V03X_H * MT9V03X_W * sizeof(uint8_t));

    for (int i = 1; i < MT9V03X_H - 1; i++)
    {
        for (int j = 1; j < MT9V03X_W - 1; j++)
        {
            // 检查当前像素及其8邻域是否全为白色
            if (binary_image[i * MT9V03X_W + j] == 255 &&
                binary_image[(i - 1) * MT9V03X_W + (j - 1)] == 255 &&
                binary_image[(i - 1) * MT9V03X_W + j] == 255 &&
                binary_image[(i - 1) * MT9V03X_W + (j + 1)] == 255 &&
                binary_image[i * MT9V03X_W + (j - 1)] == 255 &&
                binary_image[i * MT9V03X_W + (j + 1)] == 255 &&
                binary_image[(i + 1) * MT9V03X_W + (j - 1)] == 255 &&
                binary_image[(i + 1) * MT9V03X_W + j] == 255 &&
                binary_image[(i + 1) * MT9V03X_W + (j + 1)] == 255)
            {
                eroded_image[i * MT9V03X_W + j] = 255;
            }
        }
    }
}

//-------------------------------------------------------------------------------------------------------------------
//  @brief      对腐蚀后的图像进行膨胀操作
//  @return     void
//-------------------------------------------------------------------------------------------------------------------
void dilateImage(uint8_t *eroded_image, uint8_t *dilated_image)
{
    // 初始化膨胀后的图像为0
    memset(dilated_image, 0, MT9V03X_H * MT9V03X_W * sizeof(uint8_t));

    for (int i = 1; i < MT9V03X_H - 1; i++)
    {
        for (int j = 1; j < MT9V03X_W - 1; j++)
        {
            // 如果当前像素或其8邻域中有白色，则设置为白色
            if (eroded_image[i * MT9V03X_W + j] == 255 ||
                eroded_image[(i - 1) * MT9V03X_W + (j - 1)] == 255 ||
                eroded_image[(i - 1) * MT9V03X_W + j] == 255 ||
                eroded_image[(i - 1) * MT9V03X_W + (j + 1)] == 255 ||
                eroded_image[i * MT9V03X_W + (j - 1)] == 255 ||
                eroded_image[i * MT9V03X_W + (j + 1)] == 255 ||
                eroded_image[(i + 1) * MT9V03X_W + (j - 1)] == 255 ||
                eroded_image[(i + 1) * MT9V03X_W + j] == 255 ||
                eroded_image[(i + 1) * MT9V03X_W + (j + 1)] == 255)
            {
                dilated_image[i * MT9V03X_W + j] = 255;
            }
        }
    }
}

//-------------------------------------------------------------------------------------------------------------------
//  @brief      使用大津法和形态学操作进行图像二值化
//  @return     void
//-------------------------------------------------------------------------------------------------------------------
void otsu_erzhihua(uint8 *image, uint8_t *binary_image)
{
//    system_start();

    // Step 1: 二值化
    binarizeImage(image, binary_image);

//    // Step 2: 腐蚀pow vc(1)
//    erodeImage(binary_image, mt9v03x_image_eroded);
//
//    // Step 3: 膨胀
//    dilateImage(mt9v03x_image_eroded, mt9v03x_image_dilated);

    // Step 4: 将处理后的图像作为最终输出
    memcpy(binary_image, binary_image, MT9V03X_H * MT9V03X_W * sizeof(uint8_t));

//    int ti=system_getval_us();
//    tft180_show_uint(0,100,ti,5);
}
// void draw_boundaries() {
//     int i;

//     // 绘制左边界粗线
//     for (i = 119; i > 0; i--) {
//         if (side_left[i] != -1 && side_left[i-1] != -1) {
//             // 限制 i 的范围在0-127之间
//             int y = (i < 0) ? 0 : ((i > 119) ? 119 : i);
//             // 限制 x 坐标在0-159之间
//             int x = (side_left[i] < 0) ? 0 : ((side_left[i] > 159) ? 159 : side_left[i]);
            
//             // 确保绘制点及其左右点都在屏幕范围内
//             if (x >= 1 && x <= 158) {
//                 tft180_draw_point(x-1, y, RGB565_GREEN);    // 左点
//                 tft180_draw_point(x, y, RGB565_GREEN);      // 中心点
//                 tft180_draw_point(x+1, y, RGB565_GREEN);    // 右点
//             }
//         }
//     }

//     // 绘制右边界粗线
//     for (i = 119; i > 0; i--) {
//         if (side_right[i] != -1 && side_right[i-1] != -1) {
//             // 限制 i 的范围在0-127之间
//             int y = (i < 0) ? 0 : ((i > 119) ? 119 : i);
//             // 限制 x 坐标在0-159之间
//             int x = (side_right[i] < 0) ? 0 : ((side_right[i] > 159) ? 159 : side_right[i]);
            
//             // 确保绘制点及其左右点都在屏幕范围内
//             if (x >= 1 && x <= 158) {
//                 tft180_draw_point(x-1, y, RGB565_YELLOW);   // 左点
//                 tft180_draw_point(x, y, RGB565_YELLOW);     // 中心点
//                 tft180_draw_point(x+1, y, RGB565_YELLOW);   // 右点
//             }
//         }
//     }
// }

// void draw_mid_line() {
//     int i;

//     for (i = low; i > top; i--) {
//         if (mid[i] != -1 && mid[i-1] != -1) {
//             // 限制 i 的范围在0-127之间
//             int y = (i < 0) ? 0 : ((i > 119) ? 119 : i);
//             // 限制 x 坐标在0-159之间
//             int x = (mid[i] < 0) ? 0 : ((mid[i] > 159) ? 159 : mid[i]);
            
//             // 确保绘制点及其左右点都在屏幕范围内
//             if (x >= 1 && x <= 158) {
//                 tft180_draw_point(x-1, y, RGB565_RED);  // 左点
//                 tft180_draw_point(x, y, RGB565_RED);    // 中心点
//                 tft180_draw_point(x+1, y, RGB565_RED);  // 右点
//             }
//         }
//     }
// }
//-------------------------------------------------------------------------------------------------------------------
//  @brief      绘制左边界（绿色）和右边界（黄色）粗线
//  @return     void
//-------------------------------------------------------------------------------------------------------------------
void draw_boundaries(void)
{
    int i;

    // 绘制左边界粗线（绿色）
    for (i = MT9V03X_H - 1; i > 0; i--)
    {
        if (Left_Line[i] != -1 && Left_Line[i - 1] != -1)
        {
            int y = i;
            int x = Left_Line[i];
            if (x >= 1 && x <= MT9V03X_W - 2)
            {
                tft180_draw_point(x - 1, y, RGB565_GREEN);
                tft180_draw_point(x, y, RGB565_GREEN);
                tft180_draw_point(x + 1, y, RGB565_GREEN);
            }
        }
    }

    // 绘制右边界粗线（黄色）
    for (i = MT9V03X_H - 1; i > 0; i--)
    {
        if (Right_Line[i] != -1 && Right_Line[i - 1] != -1)
        {
            int y = i;
            int x = Right_Line[i];
            if (x >= 1 && x <= MT9V03X_W - 2)
            {
                tft180_draw_point(x - 1, y, RGB565_YELLOW);
                tft180_draw_point(x, y, RGB565_YELLOW);
                tft180_draw_point(x + 1, y, RGB565_YELLOW);
            }
        }
    }
}

//-------------------------------------------------------------------------------------------------------------------
//  @brief      绘制中线（红色）粗线
//  @return     void
//-------------------------------------------------------------------------------------------------------------------
void draw_mid_line(void)
{
    int i;

    for (i = low1; i > top1; i--)
    {
        if (Mid_Line[i] != -1 && Mid_Line[i - 1] != -1)
        {
            int y = i;
            int x = Mid_Line[i];
            if (x >= 1 && x <= MT9V03X_W - 2)
            {
                tft180_draw_point(x - 1, y, RGB565_RED);
                tft180_draw_point(x, y, RGB565_RED);
                tft180_draw_point(x + 1, y, RGB565_RED);
            }
        }
    }
}

extern const uint8 Image_Flags[][9][8];     //放在图上的数字标记
uint8 original_image[MT9V03X_H][MT9V03X_W]; // 储存原始图像
uint8 bin_image[MT9V03X_H][MT9V03X_W];      // 二值化后的图像 要使用的图像
volatile int Road_Wide[MT9V03X_H]; //赛宽数组
volatile int White_Column[MT9V03X_W];    //每列白列长度
volatile int Search_Stop_Line;     //搜索截止行,只记录长度，想要坐标需要用视野高度减去该值
volatile int Boundry_Start_Left;   //左右边界起始点
volatile int Boundry_Start_Right;  //第一个非丢线点,常规边界起始点
volatile int Left_Lost_Time;       //边界丢线数
volatile int Right_Lost_Time;
volatile int Both_Lost_Time;//两边同时丢线数
int Longest_White_Column_Left[2]; //最长白列,[0]是最长白列的长度，也就是Search_Stop_Line搜索截止行，[1】是第某列
int Longest_White_Column_Right[2];//最长白列,[0]是最长白列的长度，也就是Search_Stop_Line搜索截止行，[1】是第某列
int Left_Lost_Flag[MT9V03X_H] ; //左丢线数组，丢线置1，没丢线置0
int Right_Lost_Flag[MT9V03X_H]; //右丢线数组，丢线置1，没丢线置0
int camera_line = 0;

//坡道
volatile int Ramp_Flag=0;//坡道标志

//十字
volatile int Cross_Flag=0;
volatile int Left_Down_Find=0; //十字使用，找到被置行数，没找到就是0
volatile int Left_Up_Find=0;   //四个拐点标志
volatile int Right_Down_Find=0;
volatile int Right_Up_Find=0;

//图像消失
volatile uint8 Img_Disappear_Flag=0; //图像消失标志位，1就说明丢图了


/*
函数名称：int my_abs(int value)
功能说明：求绝对值
参数说明：
函数返回：绝对值
修改时间：2022年9月8日
备    注：
example：  my_abs( x)；
 */
int my_abs(int value)
{
if(value>=0) return value;
else return -value;
}

int16 limit_a_b(int16 x, int a, int b)
{
    if(x<a) x = a;
    if(x>b) x = b;
    return x;
}

/*
函数名称：int16 limit(int16 x, int16 y)
功能说明：求x,y中的最小值
参数说明：
函数返回：返回两值中的最小值
修改时间：2022年9月8日
备    注：
example：  limit( x,  y)
 */
int16 limit1(int16 x, int16 y)
{
    if (x > y)             return y;
    else if (x < -y)       return -y;
    else                return x;
}
int feedbackposition1 = 0;
int feedbackposition2 = 0;
int feedbackpositionleijia1 = 0;
int valid_count1 = 0;
/*-------------------------------------------------------------------------------------------------------------------
  @brief     双最长白列巡线
  @param     null
  @return    null
  Sample     Longest_White_Column_Left();
  @note      最长白列巡线，寻找初始边界，丢线，最长白列等基础元素，后续读取这些变量来进行赛道识别
-------------------------------------------------------------------------------------------------------------------*/
void Longest_White_Column()//最长白列巡线
{
    int i, j;
    int start_column=5;//最长白列的搜索区间
    int end_column=MT9V03X_W-5;
    int left_border = 0, right_border = 0;//临时存储赛道位置
    Longest_White_Column_Left[0] = 0;//最长白列,[0]是最长白列的长度，[1】是第某列
    Longest_White_Column_Left[1] = 0;//最长白列,[0]是最长白列的长度，[1】是第某列
    Longest_White_Column_Right[0] = 0;//最长白列,[0]是最长白列的长度，[1】是第某列
    Longest_White_Column_Right[1] = 0;//最长白列,[0]是最长白列的长度，[1】是第某列
    Right_Lost_Time = 0;    //边界丢线数
    Left_Lost_Time  = 0;
    Boundry_Start_Left  = 0;//第一个非丢线点,常规边界起始点
    Boundry_Start_Right = 0;
    Both_Lost_Time = 0;//两边同时丢线数

    for (i = 0; i <=MT9V03X_H-1; i++)//数据清零
    {
        Right_Lost_Flag[i] = 0;
        Left_Lost_Flag[i] = 0;
        Left_Line[i] = 0;
        Right_Line[i] = MT9V03X_W-1;
    }
    for(i=0;i<=MT9V03X_W-1;i++)
    {
        White_Column[i] = 0;
    }

//环岛需要对最长白列范围进行限定，环岛3状态找不到上角点，可以修改下述参数
    //环岛3状态需要改变最长白列寻找范围
    if(Right_Island_Flag==1)//右环
    {
        if(Island_State==3)
        {
            start_column=80;
            end_column=MT9V03X_W-10;
        }
    }
    else if(Left_Island_Flag==1)//左环
    {
        if(Island_State==3)
        {
            start_column=20;
            end_column=MT9V03X_W - 40;
        }
    }

    //从左到右，从下往上，遍历全图记录范围内的每一列白点数量
    for (j =start_column; j<=end_column; j++)
    {
        for (i = MT9V03X_H - 1; i >= 0; i--)
        {
            if(mt9v03x_image_2[i][j] == BLACK_PIXEL)
                break;
            else
                White_Column[j]++;
        }
    }

    //从左到右找左边最长白列
    Longest_White_Column_Left[0] =0;
    for(i=start_column;i<=end_column;i++)
    {
        if (Longest_White_Column_Left[0] < White_Column[i])//找最长的那一列
        {
            Longest_White_Column_Left[0] = White_Column[i];//【0】是白列长度
            Longest_White_Column_Left[1] = i;              //【1】是下标，第j列
        }
    }
    //从右到左找右左边最长白列
    Longest_White_Column_Right[0] = 0;//【0】是白列长度
    for(i=end_column;i>=start_column;i--)//从右往左，注意条件，找到左边最长白列位置就可以停了
    {
        if (Longest_White_Column_Right[0] < White_Column[i])//找最长的那一列
        {
            Longest_White_Column_Right[0] = White_Column[i];//【0】是白列长度
            Longest_White_Column_Right[1] = i;              //【1】是下标，第j列
        }
    }

    Search_Stop_Line = Longest_White_Column_Left[0];//搜索截止行选取左或者右区别不大，他们两个理论上是一样的
    for (i = MT9V03X_H - 1; i >=MT9V03X_H-Search_Stop_Line; i--)//常规巡线
    {
        for (j = Longest_White_Column_Right[1]; j <= MT9V03X_W - 1 - 2; j++)
        {
            if (mt9v03x_image_2[i][j] ==WHITE_PIXEL && mt9v03x_image_2[i][j + 1] == BLACK_PIXEL && mt9v03x_image_2[i][j + 2] == BLACK_PIXEL)//白黑黑，找到右边界
            {
                right_border = j;
                Right_Lost_Flag[i] = 0; //右丢线数组，丢线置1，不丢线置0
                break;
            }
            else if(j>=MT9V03X_W-1-2)//没找到右边界，把屏幕最右赋值给右边界
            {
                right_border = MT9V03X_W;
                Right_Lost_Flag[i] = 1; //右丢线数组，丢线置1，不丢线置0
                break;
            }
        }
        for (j = Longest_White_Column_Left[1]; j >= 0 + 2; j--)//往左边扫描
        {
            if (mt9v03x_image_2[i][j] ==WHITE_PIXEL && mt9v03x_image_2[i][j - 1] == BLACK_PIXEL && mt9v03x_image_2[i][j - 2] == BLACK_PIXEL)//黑黑白认为到达左边界
            {
                left_border = j;
                Left_Lost_Flag[i] = 0; //左丢线数组，丢线置1，不丢线置0
                break;
            }
            else if(j<=0+2)
            {
                left_border = 0;//找到头都没找到边，就把屏幕最左当做边界
                Left_Lost_Flag[i] = 1; //左丢线数组，丢线置1，不丢线置0
                break;
            }
        }
        Left_Line [i] = left_border;       //左边线线数组
        Right_Line[i] = right_border;      //右边线线数组
        Mid_Line[i] = (Left_Line [i] + Right_Line[i]) / 2;


    }
    /*******************归线*****************/
//        for(i=65;i>3;i--)
//        {
//           if((mt9v03x_image[i][Mid_Line[i]]<Threshold)&&(mt9v03x_image[i-1][Mid_Line[i-1]]<Threshold)
//                   &&(mt9v03x_image[i-2][Mid_Line[i-2]]<Threshold))
//           {
//               camera_line=i;
//               break;
//           }
//        }

    for (i = MT9V03X_H - 1; i >= 0; i--)//赛道数据初步分析
    {
        if (Left_Lost_Flag[i]  == 1)//单边丢线数
            Left_Lost_Time++;
        if (Right_Lost_Flag[i] == 1)
            Right_Lost_Time++;
        if (Left_Lost_Flag[i] == 1 && Right_Lost_Flag[i] == 1)//双边丢线数
            Both_Lost_Time++;
        if (Boundry_Start_Left ==  0 && Left_Lost_Flag[i]  != 1)//记录第一个非丢线点，边界起始点
            Boundry_Start_Left = i;
        if (Boundry_Start_Right == 0 && Right_Lost_Flag[i] != 1)
            Boundry_Start_Right = i;
        Road_Wide[i]=Right_Line[i]-Left_Line[i];
    }
}

/*-------------------------------------------------------------------------------------------------------------------
  @brief     左补线
  @param     补线的起点，终点
  @return    null
  Sample     Left_Add_Line(int x1,int y1,int x2,int y2);
  @note      补的直接是边界，点最好是可信度高的,不要乱补
-------------------------------------------------------------------------------------------------------------------*/
void Left_Add_Line(int x1,int y1,int x2,int y2)//左补线,补的是边界
{
    int i,max,a1,a2;
    int hx;
    if(x1>=MT9V03X_W-1)//起始点位置校正，排除数组越界的可能
       x1=MT9V03X_W-1;
    else if(x1<=0)
        x1=0;
     if(y1>=MT9V03X_H-1)
        y1=MT9V03X_H-1;
     else if(y1<=0)
        y1=0;
     if(x2>=MT9V03X_W-1)
        x2=MT9V03X_W-1;
     else if(x2<=0)
             x2=0;
     if(y2>=MT9V03X_H-1)
        y2=MT9V03X_H-1;
     else if(y2<=0)
             y2=0;
    a1=y1;
    a2=y2;
    if(a1>a2)//坐标互换
    {
        max=a1;
        a1=a2;
        a2=max;
    }
    for(i=a1;i<=a2;i++)//根据斜率补线即可
    {
        hx=(i-y1)*(x2-x1)/(y2-y1)+x1;
        if(hx>=MT9V03X_W)
            hx=MT9V03X_W;
        else if(hx<=0)
            hx=0;
        Left_Line[i]=hx;
        // side_left[i]=hx;
    }
}

/*-------------------------------------------------------------------------------------------------------------------
  @brief     右补线
  @param     补线的起点，终点
  @return    null
  Sample     Right_Add_Line(int x1,int y1,int x2,int y2);
  @note      补的直接是边界，点最好是可信度高的，不要乱补
-------------------------------------------------------------------------------------------------------------------*/
void Right_Add_Line(int x1,int y1,int x2,int y2)//右补线,补的是边界
{
    int i,max,a1,a2;
    int hx;
    if(x1>=MT9V03X_W-1)//起始点位置校正，排除数组越界的可能
       x1=MT9V03X_W-1;
    else if(x1<=0)
        x1=0;
    if(y1>=MT9V03X_H-1)
        y1=MT9V03X_H-1;
    else if(y1<=0)
        y1=0;
    if(x2>=MT9V03X_W-1)
        x2=MT9V03X_W-1;
    else if(x2<=0)
        x2=0;
    if(y2>=MT9V03X_H-1)
        y2=MT9V03X_H-1;
    else if(y2<=0)
         y2=0;
    a1=y1;
    a2=y2;
    if(a1>a2)//坐标互换
    {
        max=a1;
        a1=a2;
        a2=max;
    }
    for(i=a1;i<=a2;i++)//根据斜率补线即可
    {
        hx=(i-y1)*(x2-x1)/(y2-y1)+x1;
        if(hx>=MT9V03X_W)
            hx=MT9V03X_W;
        else if(hx<=0)
            hx=0;
        Right_Line[i]=hx;
        // side_right[i]=hx;
    }
}

/*-------------------------------------------------------------------------------------------------------------------
  @brief     左边界延长
  @param     延长起始行数，延长到某行
  @return    null
  Sample     Stop_Detect(void)
  @note      从起始点向上找5个点，算出斜率，向下延长，直至结束点
-------------------------------------------------------------------------------------------------------------------*/
void Lengthen_Left_Boundry(int start,int end)
{
    int i,t;
    float k=0;
    if(start>=MT9V03X_H-1)//起始点位置校正，排除数组越界的可能
        start=MT9V03X_H-1;
    else if(start<=0)
        start=0;
    if(end>=MT9V03X_H-1)
        end=MT9V03X_H-1;
    else if(end<=0)
        end=0;
    if(end<start)//++访问，坐标互换
    {
        t=end;
        end=start;
        start=t;
    }

    if(start<=5)//因为需要在开始点向上找3个点，对于起始点过于靠上，不能做延长，只能直接连线
    {
         Left_Add_Line(Left_Line[start],start,Left_Line[end],end);
    }

    else
    {
        k=(float)(Left_Line[start]-Left_Line[start-4])/5.0;//这里的k是1/斜率
        for(i=start;i<=end;i++)
        {
            Left_Line[i]=(int)(i-start)*k+Left_Line[start];//(x=(y-y1)*k+x1),点斜式变形
            if(Left_Line[i]>=MT9V03X_W-1)
            {
                Left_Line[i]=MT9V03X_W-1;
            }
            else if(Left_Line[i]<=0)
            {
                Left_Line[i]=0;
            }
        }
    }
}

/*-------------------------------------------------------------------------------------------------------------------
  @brief     右左边界延长
  @param     延长起始行数，延长到某行
  @return    null
  Sample     Stop_Detect(void)
  @note      从起始点向上找3个点，算出斜率，向下延长，直至结束点
-------------------------------------------------------------------------------------------------------------------*/
void Lengthen_Right_Boundry(int start,int end)
{
    int i,t;
    float k=0;
    if(start>=MT9V03X_H-1)//起始点位置校正，排除数组越界的可能
        start=MT9V03X_H-1;
    else if(start<=0)
        start=0;
    if(end>=MT9V03X_H-1)
        end=MT9V03X_H-1;
    else if(end<=0)
        end=0;
    if(end<start)//++访问，坐标互换
    {
        t=end;
        end=start;
        start=t;
    }

    if(start<=5)//因为需要在开始点向上找3个点，对于起始点过于靠上，不能做延长，只能直接连线
    {
        Right_Add_Line(Right_Line[start],start,Right_Line[end],end);
    }
    else
    {
        k=(float)(Right_Line[start]-Right_Line[start-4])/5.0;//这里的k是1/斜率
        for(i=start;i<=end;i++)
        {
            Right_Line[i]=(int)(i-start)*k+Right_Line[start];//(x=(y-y1)*k+x1),点斜式变形
            if(Right_Line[i]>=MT9V03X_W-1)
            {
                Right_Line[i]=MT9V03X_W-1;
            }
            else if(Right_Line[i]<=0)
            {
                Right_Line[i]=0;
            }
        }
    }
}


/*-------------------------------------------------------------------------------------------------------------------
  @brief     画线
  @param     输入起始点，终点坐标，补一条宽度为2的黑线
  @return    null
  Sample     Draw_Line(0, 0,MT9V03X_W-1,MT9V03X_H-1);
             Draw_Line(MT9V03X_W-1, 0,0,MT9V03X_H-1);
                                    画一个大×
  @note     补的就是一条线，需要重新扫线
-------------------------------------------------------------------------------------------------------------------*/
void Draw_Line(int startX, int startY, int endX, int endY)
{
    int i,x,y;
    int start=0,end=0;
    if(startX>=MT9V03X_W-1)//限幅处理
        startX=MT9V03X_W-1;
    else if(startX<=0)
        startX=0;
    if(startY>=MT9V03X_H-1)
        startY=MT9V03X_H-1;
    else if(startY<=0)
        startY=0;
    if(endX>=MT9V03X_W-1)
        endX=MT9V03X_W-1;
    else if(endX<=0)
        endX=0;
    if(endY>=MT9V03X_H-1)
        endY=MT9V03X_H-1;
    else if(endY<=0)
        endY=0;
    if(startX==endX)//一条竖线
    {
        if (startY > endY)//互换
        {
            start=endY;
            end=startY;
        }
        for (i = start; i <= end; i++)
        {
            if(i<=1)
                i=1;
            bin_image[i][startX]=BLACK_PIXEL;
            bin_image[i-1][startX]=BLACK_PIXEL;
        }
    }
    else if(startY == endY)//补一条横线
    {
        if (startX > endX)//互换
        {
            start=endX;
            end=startX;
        }
        for (i = start; i <= end; i++)
        {
            if(startY<=1)
                startY=1;
            bin_image[startY][i]=BLACK_PIXEL;
            bin_image[startY-1][i]=BLACK_PIXEL;
        }
    }
    else //上面两个是水平，竖直特殊情况，下面是常见情况
    {
        if(startY>endY)//起始点矫正
        {
            start=endY;
            end=startY;
        }
        else
        {
            start=startY;
            end=endY;
        }
        for (i = start; i <= end; i++)//纵向补线，保证每一行都有黑点
        {
            x =(int)(startX+(endX-startX)*(i-startY)/(endY-startY));//两点式变形
            if(x>=MT9V03X_W-1)
                x=MT9V03X_W-1;
            else if (x<=1)
                x=1;
            bin_image[i][x] = BLACK_PIXEL;
            bin_image[i][x-1] = BLACK_PIXEL;
        }
        if(startX>endX)
        {
            start=endX;
            end=startX;
        }
        else
        {
            start=startX;
            end=endX;
        }
        for (i = start; i <= end; i++)//横向补线，保证每一列都有黑点
        {

            y =(int)(startY+(endY-startY)*(i-startX)/(endX-startX));//两点式变形
            if(y>=MT9V03X_H-1)
                y=MT9V03X_H-1;
            else if (y<=0)
                y=0;
            bin_image[y][i] = BLACK_PIXEL;
        }
    }
}

/*-------------------------------------------------------------------------------------------------------------------
  @brief     边界显示，用于图传，显示到屏幕上，
  @param     null
  @return    null
  Sample     直接调用
  @note      显示左中右边界，中线，
                                           正常情况下不要用，因为直接在原图上写入了边界信息
                                           会对元素判断造成干扰的，调试时候调用
-------------------------------------------------------------------------------------------------------------------*/
void Show_Boundry(void)
{
    int16 i;
    for(i=MT9V03X_H-1;i>=MT9V03X_H-Search_Stop_Line;i--)//从最底下往上扫描
    {
        bin_image[i][Left_Line[i]+1]=RGB565_RED;
        bin_image[i][(Left_Line[i]+Right_Line[i])>>1]=BLACK_PIXEL;
        bin_image[i][Right_Line[i]-1]=RGB565_BLUE;
    }

    //在屏幕理论中线处显示红线，用于调整摄像头
    tft180_draw_line ( MT9V03X_W/2, MT9V03X_H-1, MT9V03X_W/2, 0, RGB565_RED);
}


/*-------------------------------------------------------------------------------------------------------------------
  @brief     找下面的两个拐点，供十字使用
  @param     搜索的范围起点，终点
  @return    修改两个全局变量
             Right_Down_Find=0;
             Left_Down_Find=0;
  Sample     Find_Down_Point(int start,int end)
  @note      运行完之后查看对应的变量，注意，没找到时对应变量将是0
-------------------------------------------------------------------------------------------------------------------*/
void Find_Down_Point(int start,int end)
{
    int i,t;
    Right_Down_Find=0;
    Left_Down_Find=0;
    if(start<end)
    {
        t=start;
        start=end;
        end=t;
    }
    if(start>=MT9V03X_H-1-5)//下面5行数据不稳定，不能作为边界点来判断，舍弃
        start=MT9V03X_H-1-5;
    if(end<=MT9V03X_H-Search_Stop_Line)
        end=MT9V03X_H-Search_Stop_Line;
    if(end<=5)
       end=5;
    for(i=start;i>=end;i--)
    {
        if(Left_Down_Find==0&&//只找第一个符合条件的点
           abs(Left_Line[i]-Left_Line[i+1])<=5&&//角点的阈值可以更改
           abs(Left_Line[i+1]-Left_Line[i+2])<=5&&
           abs(Left_Line[i+2]-Left_Line[i+3])<=5&&
              (Left_Line[i]-Left_Line[i-2])>=8&&
              (Left_Line[i]-Left_Line[i-3])>=15&&
              (Left_Line[i]-Left_Line[i-4])>=15)
        {
            Left_Down_Find=i;//获取行数即可
        }
        if(Right_Down_Find==0&&//只找第一个符合条件的点
           abs(Right_Line[i]-Right_Line[i+1])<=5&&//角点的阈值可以更改
           abs(Right_Line[i+1]-Right_Line[i+2])<=5&&
           abs(Right_Line[i+2]-Right_Line[i+3])<=5&&
              (Right_Line[i]-Right_Line[i-2])<=-8&&
              (Right_Line[i]-Right_Line[i-3])<=-15&&
              (Right_Line[i]-Right_Line[i-4])<=-15)
        {
            Right_Down_Find=i;
        }
        if(Left_Down_Find!=0&&Right_Down_Find!=0)//两个找到就退出
        {
            break;
        }
    }
}

/*-------------------------------------------------------------------------------------------------------------------
  @brief     找上面的两个拐点，供十字使用
  @param     搜索的范围起点，终点
  @return    修改两个全局变量
             Left_Up_Find=0;
             Right_Up_Find=0;
  Sample     Find_Up_Point(int start,int end)
  @note      运行完之后查看对应的变量，注意，没找到时对应变量将是0
-------------------------------------------------------------------------------------------------------------------*/
void Find_Up_Point(int start,int end)
{
    int i,t;
    Left_Up_Find=0;
    Right_Up_Find=0;
    if(start<end)
    {
        t=start;
        start=end;
        end=t;
    }
    if(end<=MT9V03X_H-Search_Stop_Line)
        end=MT9V03X_H-Search_Stop_Line;
    if(end<=5)//即使最长白列非常长，也要舍弃部分点，防止数组越界
        end=5;
    if(start>=MT9V03X_H-1-5)//下面5行数据不稳定，不能作为边界点来判断，舍弃
        start=MT9V03X_H-1-5;
    for(i=start;i>=end;i--)
    {
        if(Left_Up_Find==0&&//只找第一个符合条件的点
           abs(Left_Line[i]-Left_Line[i-1])<=5&&
           abs(Left_Line[i-1]-Left_Line[i-2])<=5&&
           abs(Left_Line[i-2]-Left_Line[i-3])<=5&&
              (Left_Line[i]-Left_Line[i+2])>=8&&
              (Left_Line[i]-Left_Line[i+3])>=15&&
              (Left_Line[i]-Left_Line[i+4])>=15)
        {
            Left_Up_Find=i;//获取行数即可
        }
        if(Right_Up_Find==0&&//只找第一个符合条件的点
           abs(Right_Line[i]-Right_Line[i-1])<=5&&//下面两行位置差不多
           abs(Right_Line[i-1]-Right_Line[i-2])<=5&&
           abs(Right_Line[i-2]-Right_Line[i-3])<=5&&
              (Right_Line[i]-Right_Line[i+2])<=-8&&
              (Right_Line[i]-Right_Line[i+3])<=-15&&
              (Right_Line[i]-Right_Line[i+4])<=-15)
        {
            Right_Up_Find=i;//获取行数即可
        }
        if(Left_Up_Find!=0&&Right_Up_Find!=0)//下面两个找到就出去
        {
            break;
        }
    }
    if(abs(Right_Up_Find-Left_Up_Find)>=30)//纵向撕裂过大，视为误判
    {
        Right_Up_Find=0;
        Left_Up_Find=0;
    }
}



#define IMAGE_W MT9V03X_W
#define IMAGE_H MT9V03X_H
int16 L_start_x, L_start_y, R_start_x, R_start_y;
uint8 left_findflag, right_findflag;
uint8 Boundary_search_end = 0; // 假设搜索终止行（根据需要调整）


//-------------------------------------------------------------------------------------------------------------------
//  @brief      给图像画黑框为八邻域做准备
//  @return     void
//  @since      v1.0
//  Sample usage:   image_draw_rectan(Image_use);
//-------------------------------------------------------------------------------------------------------------------
void image_draw_rectan(uint8(*image)[IMAGE_W])
{
    uint8 i = 0;
    for (i = 0; i < IMAGE_H; i++)
    {
        image[i][0] = 0;
        image[i][1] = 0;
        image[i][IMAGE_W - 1] = 0;
        image[i][IMAGE_W - 2] = 0;
    }
    for (i = 0; i < IMAGE_W; i++)
    {
        image[0][i] = 0;
        image[1][i] = 0;
    }
}


/*---------------------------------------------------------------
 【函    数】search_neighborhood
 【功    能】八邻域找边界
 【参    数】无
 【返 回 值】无
 【注意事项】
 ----------------------------------------------------------------*/
struct LEFT_EDGE
{
    int16 row;  //行坐标
    int16 col;  //列坐标
    uint8 flag; //存在边界的标志
};
struct RIGHT_EDGE
{
    int16 row;  //行坐标
    int16 col;  //列坐标
    uint8 flag; //存在边界的标志
};

struct LEFT_EDGE  L_edge[140];     //左边界结构体
struct RIGHT_EDGE R_edge[140];    //右边界结构体
uint8 L_edge_count=0, R_edge_count = 0;                     //左右边点的个数
uint8 dire_left,dire_right;                                 //记录上一个点的相对位置
uint8 L_search_amount = 140, R_search_amount = 140;  //左右边界搜点时最多允许的点
void search_neighborhood(void)
{
    L_edge_count = 0;  // 左边点个数清零
    R_edge_count = 0;  // 右边点个数清零

    if (left_findflag)  // 如果左边界点存在并找到，则开始爬线
    {
        // 初始化左边界起始点
        L_edge[0].row = L_start_y;
        L_edge[0].col = L_start_x;
        L_edge[0].flag = 1;
        int16_t curr_row = L_start_y;  // 当前行坐标
        int16_t curr_col = L_start_x;  // 当前列坐标
        dire_left = 0;  // 初始化上个边界点的来向

        // 开始搜线，最多取 L_search_amount 个点
        for (int i = 1; i < L_search_amount; i++)
        {
            // 越界检查：行越界（向上或向下）
            if (curr_row + 1 < Boundary_search_end || curr_row > IMAGE_H - 1) break;

            // 八邻域搜线过程
            if (dire_left != 2 && mt9v03x_image_2[curr_row - 1][curr_col - 1] == BLACK_PIXEL && mt9v03x_image_2[curr_row - 1][curr_col] == WHITE_PIXEL)  // 左上黑，右边白
            {
                curr_row = curr_row - 1;
                curr_col = curr_col - 1;
                L_edge_count = L_edge_count + 1;
                dire_left = 7;
                L_edge[i].row = curr_row;
                L_edge[i].col = curr_col;
                L_edge[i].flag = 1;
            }
            else if (dire_left != 3 && mt9v03x_image_2[curr_row - 1][curr_col + 1] == BLACK_PIXEL && mt9v03x_image_2[curr_row][curr_col + 1] == WHITE_PIXEL)  // 右上黑，下边白
            {
                curr_row = curr_row - 1;
                curr_col = curr_col + 1;
                L_edge_count = L_edge_count + 1;
                dire_left = 6;
                L_edge[i].row = curr_row;
                L_edge[i].col = curr_col;
                L_edge[i].flag = 1;
            }
            else if (mt9v03x_image_2[curr_row - 1][curr_col] == BLACK_PIXEL && mt9v03x_image_2[curr_row - 1][curr_col + 1] == WHITE_PIXEL)  // 正上黑，右白
            {
                curr_row = curr_row - 1;
                L_edge_count = L_edge_count + 1;
                dire_left = 0;
                L_edge[i].row = curr_row;
                L_edge[i].col = curr_col;
                L_edge[i].flag = 1;
            }
            else if (dire_left != 5 && mt9v03x_image_2[curr_row][curr_col - 1] == BLACK_PIXEL && mt9v03x_image_2[curr_row - 1][curr_col - 1] == WHITE_PIXEL)  // 正左黑，上白
            {
                curr_col = curr_col - 1;
                L_edge_count = L_edge_count + 1;
                dire_left = 4;
                L_edge[i].row = curr_row;
                L_edge[i].col = curr_col;
                L_edge[i].flag = 1;
            }
            else if (dire_left != 4 && mt9v03x_image_2[curr_row][curr_col + 1] == BLACK_PIXEL && mt9v03x_image_2[curr_row + 1][curr_col + 1] == WHITE_PIXEL)  // 正右黑，下白
            {
                curr_col = curr_col + 1;
                L_edge_count = L_edge_count + 1;
                dire_left = 5;
                L_edge[i].row = curr_row;
                L_edge[i].col = curr_col;
                L_edge[i].flag = 1;
            }
            else if (dire_left != 6 && mt9v03x_image_2[curr_row + 1][curr_col - 1] == BLACK_PIXEL && mt9v03x_image_2[curr_row][curr_col - 1] == WHITE_PIXEL)  // 左下黑，上白
            {
                curr_row = curr_row + 1;
                curr_col = curr_col - 1;
                L_edge_count = L_edge_count + 1;
                dire_left = 3;
                L_edge[i].row = curr_row;
                L_edge[i].col = curr_col;
                L_edge[i].flag = 1;
            }
            else if (dire_left != 7 && mt9v03x_image_2[curr_row + 1][curr_col + 1] == BLACK_PIXEL && mt9v03x_image_2[curr_row + 1][curr_col] == WHITE_PIXEL)  // 右下黑，左白
            {
                curr_row = curr_row + 1;
                curr_col = curr_col + 1;
                L_edge_count = L_edge_count + 1;
                dire_left = 2;
                L_edge[i].row = curr_row;
                L_edge[i].col = curr_col;
                L_edge[i].flag = 1;
            }
            else
            {
                break;  // 未找到边界点，退出循环
            }
        }
    }

    if (right_findflag)  // 如果右边界存在并找到，则开始爬线
    {
        // 初始化右边界起始点
        R_edge[0].row = R_start_y;
        R_edge[0].col = R_start_x;
        R_edge[0].flag = 1;
        int16_t curr_row = R_start_y;
        int16_t curr_col = R_start_x;
        dire_right = 0;

        // 开始搜线，最多取 R_search_amount 个点
        for (int i = 1; i < R_search_amount; i++)
        {
            // 越界检查：行越界（向上或向下）
            if (curr_row < Boundary_search_end || curr_row > IMAGE_H - 1 || curr_row + 1 < Boundary_search_end) break;

            // 八邻域爬线过程
            if (curr_col < IMAGE_W && dire_right != 3 && mt9v03x_image_2[curr_row - 1][curr_col + 1] == BLACK_PIXEL && mt9v03x_image_2[curr_row - 1][curr_col] == WHITE_PIXEL)  // 右上黑，左白
            {
                curr_row = curr_row - 1;
                curr_col = curr_col + 1;
                R_edge_count = R_edge_count + 1;
                dire_right = 6;
                R_edge[i].row = curr_row;
                R_edge[i].col = curr_col;
                R_edge[i].flag = 1;
            }
            else if (dire_right != 2 && mt9v03x_image_2[curr_row - 1][curr_col - 1] == BLACK_PIXEL && mt9v03x_image_2[curr_row][curr_col - 1] == WHITE_PIXEL)  // 左上黑，下白
            {
                curr_row = curr_row - 1;
                curr_col = curr_col - 1;
                R_edge_count = R_edge_count + 1;
                dire_right = 7;
                R_edge[i].row = curr_row;
                R_edge[i].col = curr_col;
                R_edge[i].flag = 1;
            }
            else if (mt9v03x_image_2[curr_row - 1][curr_col] == BLACK_PIXEL && mt9v03x_image_2[curr_row - 1][curr_col - 1] == WHITE_PIXEL)  // 正上黑，左白
            {
                curr_row = curr_row - 1;
                R_edge_count = R_edge_count + 1;
                dire_right = 0;
                R_edge[i].row = curr_row;
                R_edge[i].col = curr_col;
                R_edge[i].flag = 1;
            }
            else if (dire_right != 4 && mt9v03x_image_2[curr_row][curr_col + 1] == BLACK_PIXEL && mt9v03x_image_2[curr_row - 1][curr_col + 1] == WHITE_PIXEL)  // 正右黑，上白
            {
                curr_col = curr_col + 1;
                R_edge_count = R_edge_count + 1;
                dire_right = 5;
                R_edge[i].row = curr_row;
                R_edge[i].col = curr_col;
                R_edge[i].flag = 1;
            }
            else if (dire_right != 5 && mt9v03x_image_2[curr_row][curr_col - 1] == BLACK_PIXEL && mt9v03x_image_2[curr_row + 1][curr_col - 1] == WHITE_PIXEL)  // 正左黑，下白
            {
                curr_col = curr_col - 1;
                R_edge_count = R_edge_count + 1;
                dire_right = 4;
                R_edge[i].row = curr_row;
                R_edge[i].col = curr_col;
                R_edge[i].flag = 1;
            }
            else if (dire_right != 6 && mt9v03x_image_2[curr_row + 1][curr_col - 1] == BLACK_PIXEL && mt9v03x_image_2[curr_row + 1][curr_col] == WHITE_PIXEL)  // 左下黑，右白
            {
                curr_row = curr_row + 1;
                curr_col = curr_col - 1;
                R_edge_count = R_edge_count + 1;
                dire_right = 3;
                R_edge[i].row = curr_row;
                R_edge[i].col = curr_col;
                R_edge[i].flag = 1;
            }
            else if (dire_right != 7 && mt9v03x_image_2[curr_row + 1][curr_col + 1] == BLACK_PIXEL && mt9v03x_image_2[curr_row][curr_col + 1] == WHITE_PIXEL)  // 右下黑，上白
            {
                curr_row = curr_row + 1;
                curr_col = curr_col + 1;
                R_edge_count = R_edge_count + 1;
                dire_right = 2;
                R_edge[i].row = curr_row;
                R_edge[i].col = curr_col;
                R_edge[i].flag = 1;
            }
            else
            {
                break;  // 未找到边界点，退出循环
            }
        }
    }
}

/*---------------------------------------------------------------
 【函    数】clear_find_point
 【功    能】八邻域边界初始化
 【参    数】无
 【返 回 值】
 【注意事项】
 ----------------------------------------------------------------*/
void clear_find_point(void)
{
    for(int i = 0;i<L_edge_count;i++)
    {
        L_edge[i].row = 0;
        L_edge[i].col = 0;
        L_edge[i].flag = 0;
    }
    for(int i = 0;i<R_edge_count;i++)
    {
        R_edge[i].row = 0;
        R_edge[i].col = 0;
        R_edge[i].flag = 0;
    }
}
/*---------------------------------------------------------------
 【函    数】calc_diff
 【功    能】差比和
 【参    数】无
 【返 回 值】
 【注意事项】约放大128倍
 ----------------------------------------------------------------*/
int16 calc_diff(int16 x, int16 y)
{
    return ( ((x-y)<<7)/(x+y) );
}

//-------------------------------------------------------------------------------------------------------------------
//  @brief      限幅
//  @param      x               被限幅的数据
//  @param      y               限幅范围(数据会被限制在-y至+y之间)
//  @return     float           限幅之后的数据
//  Sample usage:               float dat = limit(500,300);//数据被限制在-300至+300之间  因此返回的结果是300
//-------------------------------------------------------------------------------------------------------------------
float limit(float x, int32 y)
{
    if(x>y)             return (float)y;
    else if(x<-y)       return (float)(-y);
    else                return x;
}
void draw_eight_neighborhood_boundaries(void) {
    int i;

    // 绘制左边界粗线
    for (i = 0; i < L_edge_count; i++) {
        if (L_edge[i].flag == 1) {  // 仅绘制有效的边界点
            // 获取边界点的坐标
            int y = L_edge[i].row;
            int x = L_edge[i].col;

            // 限制 y 和 x 在显示屏范围内
            y = (y < 0) ? 0 : ((y > 127) ? 127 : y);
            x = (x < 0) ? 0 : ((x > 159) ? 159 : x);

            // 确保绘制点及其左右点在屏幕范围内
            if (x >= 1 && x <= 158) {
                tft180_draw_point(x - 1, y, RGB565_GREEN);  // 左点
                tft180_draw_point(x, y, RGB565_GREEN);      // 中心点
                tft180_draw_point(x + 1, y, RGB565_GREEN);  // 右点
            }
        }
    }

    // 绘制右边界粗线
    for (i = 0; i < R_edge_count; i++) {
        if (R_edge[i].flag == 1) {  // 仅绘制有效的边界点
            // 获取边界点的坐标
            int y = R_edge[i].row;
            int x = R_edge[i].col;

            // 限制 y 和 x 在显示屏范围内
            y = (y < 0) ? 0 : ((y > 127) ? 127 : y);
            x = (x < 0) ? 0 : ((x > 159) ? 159 : x);

            // 确保绘制点及其左右点在屏幕范围内
            if (x >= 1 && x <= 158) {
                tft180_draw_point(x - 1, y, RGB565_YELLOW); // 左点
                tft180_draw_point(x, y, RGB565_YELLOW);     // 中心点
                tft180_draw_point(x + 1, y, RGB565_YELLOW); // 右点
            }
        }
    }
}
int16 L_corner_flag = 0;//左拐点存在标志
int16 L_corner_row = 0;//左拐点所在行
int16 L_corner_col = 0;//左拐点所在列
int L_corner_angle = 0;//左拐点角度
int16 R_corner_flag = 0;//右拐点存在标志
int16 R_corner_row = 0;//右拐点所在行
int16 R_corner_col = 0;//右拐点所在列
int R_corner_angle = 0;//右拐点角度
uint8 enable_L_corner=1,enable_R_corner=1;
void get_turning_point(void)
{
    L_corner_flag = 0;// 初始化变量
    L_corner_row = 0;
    L_corner_col = 0;
    L_corner_angle = 0;
    if(enable_L_corner) //如果使能搜索左拐点
    {
        if(L_edge_count > 9&&L_start_y>=IMAGE_H/2)
        {
            for(int i = 0; i<L_edge_count-9;i++)
            {
                if(L_edge[i+8].row>5)
                {
                    if((L_edge[i].col - L_edge[i + 4].col) * (L_edge[i + 8].col - L_edge[i + 4].col) +
                       (L_edge[i].row - L_edge[i + 4].row) * (L_edge[i + 8].row - L_edge[i + 4].row) >= 0) //初步确认为锐角或者直角 向量法
                    {
                        L_corner_angle = Get_angle(L_edge[i].col, L_edge[i].row, L_edge[i + 4].col, L_edge[i + 4].row, L_edge[i + 8].col, L_edge[i + 8].row); //求角度
                        if(L_edge[i+4].col>L_edge[i+8].col&&L_corner_angle>=28&&L_corner_angle<=110)
                        {
                            L_corner_flag = 1;
                            L_corner_row = L_edge[i+4].row;
                            L_corner_col = L_edge[i+4].col;
                            break;
                        }
                    }
                }
            }
        }
    }
    R_corner_flag = 0;//初始化变量
    R_corner_row = 0;
    R_corner_col = 0;
    R_corner_angle = 0;
    if(enable_R_corner)    //如果使能搜索右拐点
    {
        if(R_edge_count > 9&&R_start_y>=IMAGE_H/2)
        {
            for(int i = 0; i<R_edge_count-9;i++)
            {
                if(R_edge[i+8].row>5)
                {
                    if((R_edge[i].col - R_edge[i + 4].col) * (R_edge[i + 8].col - R_edge[i + 4].col) +
                    (R_edge[i].row - R_edge[i + 4].row) * (R_edge[i + 8].row - R_edge[i + 4].row) >= 0) //初步确认为锐角或者直角 向量法
                    {
                        R_corner_angle = Get_angle(R_edge[i].col, R_edge[i].row, R_edge[i + 4].col, R_edge[i + 4].row, R_edge[i + 8].col, R_edge[i + 8].row); //求角度
                        if(R_edge[i+8].col>R_edge[i+4].col&&R_corner_angle>=28&&R_corner_angle<=110)
                        {
                            R_corner_flag = 1;
                            R_corner_row = R_edge[i+4].row;
                            R_corner_col = R_edge[i+4].col;
                            break;
                        }
                    }
                }
            }
        }
    }
}
void balinyu() {
    // 步骤 1 & 2：画黑框
    image_draw_rectan(mt9v03x_image_2);

    // 步骤 3：设置起始点
    L_start_y = R_start_y = MT9V03X_H - 1;
    for (int16 col = 0; col < MT9V03X_W; col++) {
        if (mt9v03x_image_2[L_start_y][col] != 0) {
            L_start_x = col;
            break;
        }
    }
    for (int16 col = MT9V03X_W - 1; col >= 0; col--) {
        if (mt9v03x_image_2[R_start_y][col] != 0) {
            R_start_x = col;
            break;
        }
    }
    left_findflag = 1;
    right_findflag = 1;

    // 步骤 4：搜索边界和拐点
    search_neighborhood();
    get_turning_point();
}
// 调整中心位置 center_x 如果中心像素为黑色
void adjust_center_x_if_black(int *center_x) {

    // 检查中心像素是否为黑色
    if (mt9v03x_image_2[119][80] == BLACK_PIXEL) {
        int left_distance = -1, right_distance = -1;
        // 搜索左侧最近的白色像素
        for (int j = 79; j >= 0; j--) {
            if (mt9v03x_image_2[119][j] == WHITE_PIXEL) {
                left_distance = *center_x - j;
                // tft180_show_uint(64,20,left_distance,5);
                break;
            }
        }

        // 搜索右侧最近的白色像素
        for (int j = 81; j < 160; j++) {
            if (mt9v03x_image_2[119][j] == WHITE_PIXEL) {
                right_distance = j - *center_x;
                // tft180_show_uint(64,40,right_distance,5);
                break;
            }
        }

        // 根据最近的白色像素调整 center_x
        if ((left_distance != -1 && (right_distance == -1 || left_distance <= right_distance))) {
            // 左侧找到白色像素，或者右侧未找到，或左侧距离更近
            *center_x = 40;
        } else if ((right_distance != -1 && (left_distance == -1 || right_distance < left_distance))) {
            // 右侧找到白色像素，或者左侧未找到，或右侧距离更近
            *center_x = 120;
        } else {
            // 两侧均未找到白色像素，保持默认值
            *center_x = 80;
        }
    } else {
        // 如果中心像素不是黑色，保持 center_x 为默认值
        *center_x = 80;
    }
}

void xunji_Mid(void) {
    // Sobel滤波等预处理步骤（如果需要）
    // sobel_filter();

    int feedbackpositionleijia = 0, valid_count = 0;
    int center_x = 80;
    int half_window = 160 / 2;

    // 初始化 side_left 和 side_right
    for (int i = 0; i < MT9V03X_H + 1; i++) {
        side_left[i] = -1;
        side_right[i] = -1;
    }

    // 调整中心位置，如果中心像素为黑色
    adjust_center_x_if_black(&center_x);
// 第二次边缘检测循环
for (int i = 119; i > 0; i--) {
    // 存储原始中心点，避免因边缘检测导致的中心点漂移
    int original_center_x = center_x;
    
    // 检测右边缘
    for (int j = center_x; j < center_x + half_window && j < MT9V03X_W - 1; j++) {
        if (mt9v03x_image_2[i][j] == BLACK_PIXEL) {
            side_right[i] = j;
            break;
        }
    }
    


    // 检测左边缘
    for (int j = center_x; j > center_x - half_window && j > 1; j--) {
        if (mt9v03x_image_2[i][j] == BLACK_PIXEL) {
            side_left[i] = j;
            break;
        }
    }
    
    // 计算 mid[i]
    if (side_left[i] != -1 && side_right[i] != -1) {
        // 两边都检测到边缘，取中点
        mid[i] = (side_right[i] + side_left[i]) / 2;
    } else if (side_left[i] == -1 && side_right[i] != -1) {
        // 仅检测到右边缘
        // 根据行号动态调整偏移量
        float offset = half_window * (float)(i + 40) / 160.0f;
        float temp = (float)side_right[i] - offset;
        if (temp > 1.0f) {
            mid[i] = (int)temp;
        } else {
            mid[i] = 1;
        }
    } else if (side_right[i] == -1 && side_left[i] != -1) {
        // 仅检测到左边缘
        // 根据行号动态调整偏移量
        float offset = half_window * (float)(i + 40) / 160.0f;
        float temp = (float)side_left[i] + offset;
        if (temp < 159.0f) {
            mid[i] = (int)temp;
        } else {
            mid[i] = 159;
        }
    } else {
        // 未检测到任何边缘，保持中线
        mid[i] = 80;
    }

        // 更新反馈位置
        if (i >= top1 && i <= low1) {
            feedbackpositionleijia += mid[i];
            valid_count++;
            center_x = mid[i]; // 更新中心位置
        } else {
            // 如果i不在top和low之间，仍然更新center_x以保持追踪
            center_x = mid[i];
        }
    }

    feedbackposition = (valid_count > 0) ? (feedbackpositionleijia / valid_count) : -1;
    // tft180_show_uint(0, 40, feedbackposition, 5);
    // process_intersection();
}
void zongsaoxian() {
    // xunji_Mid();
    Longest_White_Column();
    balinyu(); 
    Cross_Detect();
    Island_Detect();
    for (int i = 0; i < MT9V03X_H; i++) {
      Mid_Line[i] = (Left_Line[i] + Right_Line[i]) / 2;  // 计算中线
    }
    // 修正计算 feedbackposition1 的代码
    int feedbackpositionleijia1 = 0;  // 在循环外重置累加变量
    int valid_count1 = 0;             // 在循环外定义计数器
    for (int i = MT9V03X_H - 1; i >= MT9V03X_H-Search_Stop_Line; i--) {
        Mid_Line[i] = (Left_Line[i] + Right_Line[i])/2;  // 计算中线
        if (i >= top1 && i <= low1) {
            feedbackpositionleijia1 += Mid_Line[i];
            valid_count1++;
        }
    }
    feedbackposition1 = feedbackpositionleijia1 / valid_count1;
}